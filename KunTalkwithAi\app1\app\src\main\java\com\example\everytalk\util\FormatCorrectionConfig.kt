package com.example.everytalk.util

/**
 * 格式矫正配置
 */
data class FormatCorrectionConfig(
    // 基本开关
    val enablePerformanceOptimization: Boolean = true,
    val enableCodeBlockCorrection: Boolean = true,
    val enableMarkdownCorrection: <PERSON><PERSON><PERSON> = true,
    val enableIntelligentFormatting: <PERSON><PERSON>an = true,
    val enableRealtimePreprocessing: <PERSON>olean = true,
    
    // 高级功能
    val enableCaching: Boolean = true,
    val enableAsyncProcessing: Boolean = false,
    val enableProgressiveCorrection: Boolean = true,
    
    // 性能参数
    val maxProcessingTimeMs: Long = 2000L,
    val maxCacheSize: Int = 100,
    val chunkSizeThreshold: Int = 1000,
    
    // 矫正强度
    val correctionIntensity: CorrectionIntensity = CorrectionIntensity.MODERATE
)