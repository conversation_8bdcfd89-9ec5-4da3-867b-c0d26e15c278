androidx.compose.ui.input.rotary.RotaryInputElement
com.example.everytalk.data.DataClass.SafetySetting
androidx.core.app.RemoteActionCompatParcelizer
com.example.everytalk.data.DataClass.ChatRequest
androidx.compose.ui.graphics.GraphicsLayerElement
androidx.compose.foundation.ScrollingContainerElement
androidx.compose.foundation.ScrollingLayoutElement
com.example.everytalk.data.DataClass.GenerationConfig
com.example.everytalk.data.DataClass.Part
com.example.everytalk.data.DataClass.Part$InlineData
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer
com.example.everytalk.data.DataClass.PromptFeedback$Companion
androidx.compose.foundation.text.input.internal.CoreTextFieldSemanticsModifier
com.example.everytalk.data.DataClass.PartsApiMessage
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion
androidx.compose.foundation.layout.PaddingValuesElement
androidx.compose.foundation.gestures.ScrollableElement
androidx.compose.ui.input.pointer.StylusHoverIconModifierElement
androidx.compose.ui.focus.FocusChangedElement
android.support.v4.app.RemoteActionCompatParcelizer
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion
androidx.navigation.compose.BackStackEntryIdViewModel
com.example.everytalk.data.DataClass.Sender$Companion
androidx.lifecycle.ProcessLifecycleOwner$attach$1
androidx.compose.ui.draw.DrawWithContentElement
androidx.profileinstaller.ProfileInstallerInitializer
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer
com.example.everytalk.data.DataClass.Message
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings
com.example.everytalk.data.DataClass.SafetyRating$Companion
androidx.compose.foundation.layout.PaddingElement
androidx.compose.ui.viewinterop.FocusTargetPropertiesElement
com.google.gson.reflect.TypeToken
androidx.versionedparcelable.ParcelImpl
com.example.everytalk.data.DataClass.ThinkingConfig
com.example.everytalk.data.DataClass.ContentPart$Code
com.example.everytalk.data.DataClass.Content
com.example.everytalk.data.DataClass.ChatRequest$$serializer
com.example.everytalk.data.DataClass.IMessage
kotlin.coroutines.jvm.internal.BaseContinuationImpl
androidx.compose.foundation.ClickableElement
androidx.compose.animation.SizeAnimationModifierElement
com.example.everytalk.data.DataClass.Message$Companion
androidx.compose.ui.input.pointer.PointerInputEventHandler
androidx.compose.foundation.lazy.layout.LazyLayoutBeyondBoundsModifierElement
androidx.compose.ui.input.pointer.SuspendPointerInputElement
androidx.compose.ui.semantics.ClearAndSetSemanticsElement
com.example.everytalk.data.DataClass.Part$Companion
com.example.everytalk.data.DataClass.PartsApiMessage$Companion
com.example.everytalk.data.DataClass.WebSearchResult$$serializer
androidx.versionedparcelable.CustomVersionedParcelable
androidx.compose.foundation.text.modifiers.TextAnnotatedStringElement
coil3.compose.internal.ContentPainterElement
com.example.everytalk.data.DataClass.Candidate
com.example.everytalk.data.DataClass.Part$InlineData$Companion
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation
androidx.core.app.CoreComponentFactory
androidx.core.content.FileProvider
com.example.everytalk.data.DataClass.ChatRequest$Companion
androidx.compose.ui.platform.AndroidComposeView$bringIntoViewNode$1
androidx.compose.animation.EnterExitTransitionElement
androidx.compose.foundation.text.input.internal.LegacyAdaptingPlatformTextInputModifier
androidx.graphics.path.PathIteratorPreApi34Impl
androidx.compose.foundation.MagnifierElement
androidx.navigation.NavGraphNavigator
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion
androidx.compose.ui.draw.DrawBehindElement
androidx.compose.foundation.lazy.layout.TraversablePrefetchStateModifierElement
androidx.compose.foundation.text.modifiers.TextStringSimpleElement
androidx.compose.foundation.BackgroundElement
com.example.everytalk.data.DataClass.PromptFeedback
com.example.everytalk.data.DataClass.ApiContentPart$InlineData
androidx.compose.material3.internal.DraggableAnchorsElement
com.example.everytalk.data.DataClass.WebSearchResult$Companion
androidx.compose.foundation.text.handwriting.StylusHandwritingElement
androidx.navigation.ActivityNavigator
com.example.everytalk.data.DataClass.SafetyRating$$serializer
androidx.compose.foundation.relocation.BringIntoViewRequesterElement
androidx.emoji2.text.EmojiCompatInitializer
androidx.compose.ui.graphics.BlockGraphicsLayerElement
com.example.everytalk.data.DataClass.ModalityType$Companion
androidx.compose.foundation.layout.WrapContentElement
com.example.everytalk.data.DataClass.GithubRelease
kotlinx.coroutines.android.AndroidDispatcherFactory
androidx.compose.foundation.layout.SizeElement
androidx.compose.ui.layout.OnGloballyPositionedElement
com.example.everytalk.data.DataClass.ContentPart
androidx.compose.foundation.lazy.layout.LazyLayoutItemAnimator$DisplayingDisappearingItemsElement
androidx.compose.ui.draw.PainterElement
com.example.everytalk.data.DataClass.ApiConfig$Companion
com.example.everytalk.data.DataClass.SafetySetting$Companion
androidx.lifecycle.ReportFragment$LifecycleCallbacks
com.example.everytalk.data.DataClass.WebSearchResult
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion
com.example.everytalk.statecontroller.MainActivity
com.example.everytalk.data.DataClass.ThinkingConfig$Companion
androidx.core.graphics.drawable.IconCompat
com.example.everytalk.data.DataClass.Content$$serializer
com.example.everytalk.data.DataClass.Part$FileUri$Companion
androidx.compose.foundation.layout.AlignmentLineOffsetDpElement
com.example.everytalk.data.DataClass.ApiConfig
androidx.compose.foundation.layout.VerticalAlignElement
androidx.compose.foundation.HoverableElement
com.example.everytalk.data.DataClass.GeminiApiRequest
com.example.everytalk.data.DataClass.Part$FileUri$$serializer
androidx.compose.ui.layout.LayoutIdElement
com.example.everytalk.data.DataClass.Part$Text$Companion
io.ktor.client.engine.android.AndroidEngineContainer
androidx.core.graphics.drawable.IconCompatParcelizer
androidx.navigation.compose.ComposeNavGraphNavigator
androidx.compose.material3.MinimumInteractiveModifier
androidx.compose.foundation.CombinedClickableElement
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion
androidx.compose.ui.input.pointer.PointerHoverIconModifierElement
androidx.graphics.path.ConicConverter
androidx.compose.foundation.layout.IntrinsicWidthElement
androidx.profileinstaller.ProfileInstallReceiver
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion
androidx.compose.foundation.layout.LayoutWeightElement
com.example.everytalk.data.DataClass.GithubRelease$Companion
androidx.compose.ui.focus.FocusOwnerImpl$modifier$1
com.example.everytalk.data.DataClass.ApiConfig$$serializer
androidx.compose.foundation.layout.FillElement
androidx.compose.foundation.lazy.layout.LazyLayoutSemanticsModifier
com.example.everytalk.data.DataClass.GeminiApiResponse
com.example.everytalk.data.DataClass.Part$Text$$serializer
androidx.compose.ui.focus.FocusRequesterElement
androidx.compose.animation.AnimatedContentTransitionScopeImpl$SizeModifierElement
com.example.everytalk.data.DataClass.ApiContentPart
androidx.compose.foundation.FocusableElement
androidx.lifecycle.ReportFragment
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback
com.example.everytalk.data.DataClass.ModalityType
com.example.everytalk.data.DataClass.Part$Text
androidx.lifecycle.SavedStateHandlesVM
com.example.everytalk.data.DataClass.ApiContentPart$Text
androidx.compose.ui.semantics.AppendedSemanticsElement
com.example.everytalk.data.DataClass.Message$$serializer
com.example.everytalk.data.DataClass.MessageKt
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer
com.example.everytalk.data.DataClass.PromptFeedback$$serializer
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion
com.example.everytalk.data.DataClass.MessageKt$WhenMappings
androidx.compose.ui.input.key.KeyInputElement
com.example.everytalk.data.DataClass.SafetySetting$$serializer
androidx.compose.ui.platform.AndroidCompositionLocals_androidKt
com.example.everytalk.data.DataClass.ApiContentPart$Companion
android.support.v4.graphics.drawable.IconCompatParcelizer
com.example.everytalk.data.DataClass.Part$FileUri
androidx.compose.foundation.IndicationModifierElement
androidx.compose.foundation.layout.OffsetElement
androidx.compose.ui.viewinterop.FocusGroupPropertiesElement
androidx.compose.ui.draw.ShadowGraphicsLayerElement
androidx.compose.ui.focus.FocusTargetNode$FocusTargetElement
androidx.compose.ui.input.nestedscroll.NestedScrollElement
com.example.everytalk.data.DataClass.SafetyRating
com.example.everytalk.data.DataClass.Sender
androidx.compose.ui.draw.DrawWithCacheElement
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer
com.example.everytalk.data.DataClass.ApiContentPart$FileUri
kotlinx.coroutines.internal.StackTraceRecoveryKt
androidx.navigation.NavControllerViewModel
androidx.core.app.RemoteActionCompat
androidx.compose.ui.layout.OnSizeChangedModifier
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer
androidx.compose.foundation.layout.HorizontalAlignElement
com.example.everytalk.data.DataClass.ContentPart$Audio
androidx.navigation.compose.ComposeNavigator
androidx.compose.ui.layout.LayoutElement
com.example.everytalk.data.DataClass.GithubRelease$$serializer
androidx.navigation.compose.DialogNavigator
com.example.everytalk.data.DataClass.GenerationConfig$$serializer
com.example.everytalk.statecontroller.LRUCache
com.example.everytalk.data.DataClass.Part$InlineData$$serializer
com.example.everytalk.data.DataClass.Content$Companion
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer
androidx.compose.ui.draganddrop.AndroidDragAndDropManager$modifier$1
com.example.everytalk.data.DataClass.SimpleTextApiMessage
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1
okhttp3.internal.publicsuffix.PublicSuffixDatabase
androidx.compose.foundation.layout.BoxChildDataElement
com.example.everytalk.data.DataClass.Candidate$$serializer
androidx.navigation.NavBackStackEntry$SavedStateViewModel
com.example.everytalk.data.DataClass.AbstractApiMessage
com.example.everytalk.data.DataClass.GenerationConfig$Companion
androidx.lifecycle.ProcessLifecycleInitializer
com.example.everytalk.data.DataClass.ContentPart$Html
androidx.compose.foundation.BorderModifierNodeElement
androidx.annotation.Keep
com.example.everytalk.data.DataClass.Candidate$Companion
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer
androidx.compose.foundation.layout.UnspecifiedConstraintsElement
androidx.compose.foundation.gestures.DraggableElement
androidx.compose.foundation.text.modifiers.SelectableTextAnnotatedStringElement
androidx.startup.InitializationProvider
androidx.compose.ui.semantics.EmptySemanticsElement
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.ThreadState: int _state$volatile
com.example.everytalk.data.DataClass.Message$$serializer: int $stable
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String provider
com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: int $stable
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
com.example.everytalk.data.DataClass.ApiConfig: boolean isValid
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler$volatile
androidx.compose.runtime.ParcelableSnapshotMutableFloatState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.Message: java.util.List imageUrls
com.example.everytalk.data.DataClass.WebSearchResult: com.example.everytalk.data.DataClass.WebSearchResult$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String base64Data
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: long deqIdx$volatile
com.example.everytalk.data.DataClass.Message: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ApiConfig$Companion Companion
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String id
io.ktor.client.engine.HttpClientEngineBase: int closed
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion Companion
com.example.everytalk.data.DataClass.Part$InlineData: com.example.everytalk.data.DataClass.Part$InlineData$Companion Companion
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev$volatile
com.example.everytalk.data.DataClass.ApiContentPart$Text: int $stable
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String code
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: com.example.everytalk.data.DataClass.WebSearchResult$$serializer INSTANCE
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.Set customProviders
kotlinx.serialization.json.JsonObject: kotlinx.serialization.json.JsonObject$Companion Companion
com.example.everytalk.data.network.AppStreamEvent: com.example.everytalk.data.network.AppStreamEvent$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$StatusUpdate: com.example.everytalk.data.network.AppStreamEvent$StatusUpdate$Companion Companion
com.example.everytalk.data.network.ApiClient$FileMetadata$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String contentId
com.example.everytalk.data.DataClass.Candidate$$serializer: int $stable
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex$volatile
com.example.everytalk.data.DataClass.AbstractApiMessage: kotlin.Lazy $cachedSerializer$delegate
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String imageSize
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next$volatile
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
io.ktor.utils.io.core.internal.ChunkBuffer: int refCount
kotlinx.coroutines.EventLoopImplBase: int _isCompleted$volatile
com.example.everytalk.models.SelectedMediaItem$ImageFromUri: com.example.everytalk.models.SelectedMediaItem$ImageFromUri$Companion Companion
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String data
io.ktor.client.HttpClient: int closed
com.example.everytalk.data.DataClass.ApiContentPart: kotlin.Lazy $cachedSerializer$delegate
kotlinx.coroutines.internal.ThreadSafeHeap: int _size$volatile
com.example.everytalk.data.DataClass.Message$$serializer: com.example.everytalk.data.DataClass.Message$$serializer INSTANCE
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float guidanceScale
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ModalityType: java.lang.String displayName
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String category
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender System
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag$volatile
com.example.everytalk.data.DataClass.SafetyRating$$serializer: com.example.everytalk.data.DataClass.SafetyRating$$serializer INSTANCE
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String markdownWithKatex
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: int $stable
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: java.lang.Object tail$volatile
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String address
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder$volatile
io.ktor.utils.io.jvm.javaio.BlockingAdapter: int result
kotlinx.coroutines.internal.Segment: int cleanedAndPointers$volatile
io.ktor.utils.io.ByteBufferChannel: long totalBytesRead
com.example.everytalk.data.DataClass.Message: boolean isError
com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String key
com.example.everytalk.data.network.AppStreamEvent$StreamEnd: com.example.everytalk.data.network.AppStreamEvent$StreamEnd$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: int $stable
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.GenerationConfig generationConfig
com.example.everytalk.data.network.AppStreamEvent$Finish: com.example.everytalk.data.network.AppStreamEvent$Finish$Companion Companion
com.example.everytalk.data.DataClass.Candidate: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender User
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender[] $VALUES
com.example.everytalk.data.DataClass.Part: kotlin.Lazy $cachedSerializer$delegate
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List contents
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.network.ApiClient$FileMetadata: com.example.everytalk.data.network.ApiClient$FileMetadata$Companion Companion
com.example.everytalk.data.DataClass.Message: java.util.List attachments
com.example.everytalk.models.SelectedMediaItem$Audio$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.PromptFeedback: int $stable
com.example.everytalk.data.DataClass.ApiConfig$$serializer: int $stable
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: int $stable
com.example.everytalk.data.DataClass.Sender: kotlin.Lazy $cachedSerializer$delegate
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
com.example.everytalk.data.DataClass.Part$InlineData: int $stable
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState$volatile
com.example.everytalk.data.DataClass.Content: java.util.List parts
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String model
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex$volatile
com.example.everytalk.data.DataClass.Candidate: java.util.List safetyRatings
com.example.everytalk.data.DataClass.ChatRequest$$serializer: int $stable
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: int $stable
com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap: com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap$Companion Companion
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String provider
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GeminiApiRequest$Companion Companion
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer INSTANCE
androidx.compose.runtime.ParcelableSnapshotMutableIntState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.ApiContentPart: com.example.everytalk.data.DataClass.ApiContentPart$Companion Companion
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String id
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String fileUri
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _readOp
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String uri
kotlinx.serialization.json.JsonPrimitive: kotlinx.serialization.json.JsonPrimitive$Companion Companion
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String body
com.example.everytalk.data.DataClass.ContentPart$Code: int $stable
com.example.everytalk.data.DataClass.Message: java.lang.String name
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String category
com.example.everytalk.data.network.AppStreamEvent$StatusUpdate$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: int $stable
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.statecontroller.LRUCache: int maxSize
com.example.everytalk.data.DataClass.PartsApiMessage: java.util.List parts
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus$volatile
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType$Companion Companion
com.example.everytalk.data.DataClass.Part$Text: java.lang.String text
com.example.everytalk.data.DataClass.Candidate: int $stable
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: int $stable
com.example.everytalk.data.network.AppStreamEvent$Content$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String data
com.example.everytalk.data.DataClass.GithubRelease$$serializer: com.example.everytalk.data.DataClass.GithubRelease$$serializer INSTANCE
com.example.everytalk.data.DataClass.Candidate: int index
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String contentId
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Sender sender
com.example.everytalk.data.DataClass.Content: java.lang.String role
androidx.activity.result.ActivityResult: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: int $stable
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Object toolChoice
com.example.everytalk.data.DataClass.Part$Text: com.example.everytalk.data.DataClass.Part$Text$Companion Companion
com.example.everytalk.data.DataClass.ContentPart: int $stable
com.example.everytalk.data.DataClass.ContentPart$Audio: int $stable
com.example.everytalk.data.DataClass.Message: java.lang.String reasoning
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer INSTANCE
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Integer maxOutputTokens
io.ktor.utils.io.internal.RingBufferCapacity: int _pendingToFlush
com.example.everytalk.data.DataClass.SafetySetting: int $stable
io.ktor.utils.io.ByteBufferChannel: long totalBytesWritten
com.example.everytalk.data.DataClass.Message: java.lang.String id
com.example.everytalk.data.DataClass.GithubRelease: com.example.everytalk.data.DataClass.GithubRelease$Companion Companion
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.ChatRequest$Companion Companion
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List safetySettings
androidx.activity.result.IntentSenderRequest: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.WebSearchResult: int index
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state$volatile
com.example.everytalk.data.DataClass.PromptFeedback: java.util.List safetyRatings
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause$volatile
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer numInferenceSteps
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
io.ktor.utils.io.pool.SingleInstancePool: java.lang.Object instance
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String mimeType
com.example.everytalk.data.DataClass.Message: java.lang.String text
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle$volatile
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: com.example.everytalk.data.DataClass.Part$InlineData$$serializer INSTANCE
com.example.everytalk.data.DataClass.ThinkingConfig: com.example.everytalk.data.DataClass.ThinkingConfig$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String mimeType
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.ThinkingConfig thinkingConfig
com.example.everytalk.data.DataClass.ContentPart: java.lang.String contentId
io.ktor.utils.io.ByteBufferChannel: io.ktor.utils.io.internal.JoiningState joining
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String probability
com.example.everytalk.data.DataClass.SimpleTextApiMessage: int $stable
androidx.lifecycle.Lifecycle$Event: kotlin.enums.EnumEntries $ENTRIES
com.example.everytalk.data.DataClass.SafetyRating: int $stable
com.example.everytalk.data.DataClass.Part: com.example.everytalk.data.DataClass.Part$Companion Companion
com.example.everytalk.statecontroller.LRUCache: int $stable
kotlinx.coroutines.CompletedExceptionally: int _handled$volatile
kotlinx.coroutines.DispatchedCoroutine: int _decision$volatile
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
com.example.everytalk.data.DataClass.WebSearchResult: int $stable
com.example.everytalk.data.DataClass.Content$$serializer: com.example.everytalk.data.DataClass.Content$$serializer INSTANCE
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender AI
com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: com.example.everytalk.data.DataClass.PromptFeedback$$serializer INSTANCE
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: int $stable
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.network.AppStreamEvent$ToolCall$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.flow.ChannelAsFlow: int consumed$volatile
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType AUDIO
kotlinx.coroutines.DefaultExecutor: int debugStatus
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: int $stable
com.example.everytalk.data.DataClass.PartsApiMessage: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.ThinkingConfig: int $stable
io.ktor.utils.io.pool.DefaultPool: long top
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean qwenEnableSearch
com.example.everytalk.data.DataClass.AbstractApiMessage: int $stable
com.example.everytalk.models.SelectedMediaItem$GenericFile$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.models.SelectedMediaItem$ImageFromUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Part$Text$$serializer: int $stable
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: int $stable
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Content content
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType MULTIMODAL
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
com.example.everytalk.data.DataClass.Candidate: java.lang.String finishReason
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GenerationConfig generationConfig
com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse: com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse$Companion Companion
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: com.example.everytalk.data.DataClass.ThinkingConfig$$serializer INSTANCE
com.example.everytalk.data.DataClass.GeminiApiRequest: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.Message: java.util.List webSearchResults
kotlinx.serialization.json.JsonElement: kotlinx.serialization.json.JsonElement$Companion Companion
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: int $stable
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: com.example.everytalk.statecontroller.AppViewModel$ExportedSettings$Companion Companion
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String htmlUrl
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev$volatile
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation$volatile
com.example.everytalk.data.DataClass.SafetySetting: com.example.everytalk.data.DataClass.SafetySetting$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.network.AppStreamEvent$Error: com.example.everytalk.data.network.AppStreamEvent$Error$Companion Companion
com.example.everytalk.data.DataClass.PartsApiMessage: com.example.everytalk.data.DataClass.PartsApiMessage$Companion Companion
kotlinx.coroutines.CancelledContinuation: int _resumed$volatile
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state$volatile
io.ktor.utils.io.jvm.javaio.BlockingAdapter: java.lang.Object state
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String model
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: long enqIdx$volatile
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl$volatile
com.example.everytalk.data.DataClass.GenerationConfig: int $stable
com.example.everytalk.data.DataClass.GithubRelease: int $stable
com.example.everytalk.data.network.AppStreamEvent$Reasoning$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
androidx.compose.runtime.ParcelableSnapshotMutableLongState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.JobSupport: java.lang.Object _state$volatile
com.example.everytalk.data.DataClass.Part$Text: int $stable
com.example.everytalk.data.DataClass.Part: int $stable
io.ktor.utils.io.internal.RingBufferCapacity: int _availableForWrite$internal
com.example.everytalk.data.DataClass.ContentPart$Html: int $stable
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String language
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ModalityType modalityType
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
com.example.everytalk.data.DataClass.ModalityType: kotlin.enums.EnumEntries $ENTRIES
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers$volatile
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack$volatile
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: int $stable
com.example.everytalk.data.DataClass.PromptFeedback: com.example.everytalk.data.DataClass.PromptFeedback$Companion Companion
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.GeminiApiResponse: java.util.List candidates
com.example.everytalk.data.DataClass.ChatRequest: java.util.List tools
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur$volatile
kotlinx.coroutines.EventLoopImplBase$DelayedTask: java.lang.Object _heap
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String apiAddress
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Candidate$Companion Companion
com.example.everytalk.data.DataClass.ApiConfig: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer INSTANCE
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex$volatile
kotlinx.serialization.json.JsonArray: kotlinx.serialization.json.JsonArray$Companion Companion
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.PromptFeedback promptFeedback
com.example.everytalk.data.network.AppStreamEvent$Reasoning: com.example.everytalk.data.network.AppStreamEvent$Reasoning$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion Companion
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: java.lang.Object head$volatile
com.example.everytalk.data.network.AppStreamEvent$ToolCall: com.example.everytalk.data.network.AppStreamEvent$ToolCall$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$StreamEnd$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
io.ktor.utils.io.internal.CancellableReusableContinuation: java.lang.Object jobCancellationHandler
com.example.everytalk.data.DataClass.ChatRequest: java.util.List messages
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: com.example.everytalk.data.DataClass.PartsApiMessage$$serializer INSTANCE
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean forceGoogleReasoningPrompt
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd$volatile
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map customModelParameters
com.example.everytalk.data.DataClass.Message: long timestamp
io.ktor.utils.io.internal.RingBufferCapacity: int _availableForRead$internal
com.example.everytalk.data.DataClass.SafetyRating$$serializer: int $stable
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender$Companion Companion
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String id
androidx.compose.runtime.ParcelableSnapshotMutableState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.SimpleTextApiMessage: com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion Companion
com.example.everytalk.data.DataClass.Part$FileUri: com.example.everytalk.data.DataClass.Part$FileUri$Companion Companion
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String name
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner$volatile
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType IMAGE
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String apiKey
com.example.everytalk.data.DataClass.MessageKt$WhenMappings: int[] $EnumSwitchMapping$0
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer INSTANCE
com.example.everytalk.data.network.AppStreamEvent$Content: com.example.everytalk.data.network.AppStreamEvent$Content$Companion Companion
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
com.example.everytalk.data.DataClass.ApiContentPart: int $stable
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next$volatile
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause$volatile
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String contentId
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask$volatile
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String content
com.example.everytalk.data.DataClass.Sender: kotlin.enums.EnumEntries $ENTRIES
com.example.everytalk.data.DataClass.ApiConfig: int $stable
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float temperature
com.example.everytalk.data.network.AppStreamEvent$Error$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiConfig$$serializer: com.example.everytalk.data.DataClass.ApiConfig$$serializer INSTANCE
com.example.everytalk.data.DataClass.Part$Text$$serializer: com.example.everytalk.data.DataClass.Part$Text$$serializer INSTANCE
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef$volatile
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
androidx.compose.foundation.lazy.layout.DefaultLazyKey: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.Content$$serializer: int $stable
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Message$Companion Companion
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer INSTANCE
com.example.everytalk.data.DataClass.PromptFeedback: kotlinx.serialization.KSerializer[] $childSerializers
io.ktor.utils.io.ByteBufferChannel: int writeSuspensionSize
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
coil3.compose.internal.DeferredDispatchCoroutineDispatcher: int _unconfined$volatile
com.example.everytalk.data.DataClass.SafetyRating: com.example.everytalk.data.DataClass.SafetyRating$Companion Companion
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String tagName
kotlin.coroutines.SafeContinuation: java.lang.Object result
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender Tool
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
io.ktor.utils.io.pool.SingleInstancePool: int borrowed
androidx.navigation.NavBackStackEntryState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: int $stable
com.example.everytalk.data.DataClass.ApiConfig: float temperature
com.example.everytalk.data.DataClass.Message: boolean isPlaceholderName
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: com.example.everytalk.data.DataClass.GenerationConfig$$serializer INSTANCE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String title
kotlinx.coroutines.channels.BufferedChannel: long receivers$volatile
com.example.everytalk.models.SelectedMediaItem: com.example.everytalk.models.SelectedMediaItem$Companion Companion
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed$volatile
com.example.everytalk.data.network.AppStreamEvent$WebSearchResults$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String threshold
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map customExtraBody
com.example.everytalk.models.SelectedMediaItem$GenericFile: com.example.everytalk.models.SelectedMediaItem$GenericFile$Companion Companion
com.example.everytalk.data.DataClass.SafetySetting$$serializer: com.example.everytalk.data.DataClass.SafetySetting$$serializer INSTANCE
com.example.everytalk.data.DataClass.ApiContentPart$Text: com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion Companion
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Boolean defaultUseWebSearch
com.example.everytalk.data.DataClass.ChatRequest: int $stable
com.example.everytalk.data.DataClass.Candidate$$serializer: com.example.everytalk.data.DataClass.Candidate$$serializer INSTANCE
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: com.example.everytalk.data.DataClass.Part$FileUri$$serializer INSTANCE
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean useWebSearch
kotlin.SafePublicationLazyImpl: java.lang.Object _value
com.example.everytalk.data.network.AppStreamEvent$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.PartsApiMessage: int $stable
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer INSTANCE
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment$volatile
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment$volatile
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue$volatile
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.List apiConfigs
com.example.everytalk.data.network.AppStreamEvent$Text: com.example.everytalk.data.network.AppStreamEvent$Text$Companion Companion
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String mimeType
com.example.everytalk.data.DataClass.Message: java.lang.String currentWebSearchStage
com.example.everytalk.data.DataClass.Content: com.example.everytalk.data.DataClass.Content$Companion Companion
com.example.everytalk.data.DataClass.Message: boolean contentStarted
io.ktor.client.call.HttpClientCall: int received
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String snippet
com.example.everytalk.data.DataClass.Content: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.Content: int $stable
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
com.example.everytalk.data.DataClass.GeminiApiRequest: int $stable
com.example.everytalk.data.DataClass.GeminiApiResponse: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.Message: int $stable
com.example.everytalk.data.network.AppStreamEvent$Finish$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String text
com.example.everytalk.data.DataClass.Part$FileUri: int $stable
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _state
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer$volatile
com.example.everytalk.models.SelectedMediaItem$Audio: com.example.everytalk.models.SelectedMediaItem$Audio$Companion Companion
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Boolean includeThoughts
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.GenerationConfig$Companion Companion
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String role
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType VIDEO
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String role
com.example.everytalk.data.network.AppStreamEvent$WebSearchResults: com.example.everytalk.data.network.AppStreamEvent$WebSearchResults$Companion Companion
com.example.everytalk.data.DataClass.SafetySetting$$serializer: int $stable
io.ktor.utils.io.pool.SingleInstancePool: int disposed
com.example.everytalk.data.DataClass.ChatRequest$$serializer: com.example.everytalk.data.DataClass.ChatRequest$$serializer INSTANCE
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer maxTokens
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String name
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.GeminiApiResponse$Companion Companion
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle$volatile
com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus: com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus$Companion Companion
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String href
com.example.everytalk.data.DataClass.GeminiApiResponse: int $stable
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated$volatile
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Integer thinkingBudget
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType[] $VALUES
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _closed
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _writeOp
io.ktor.utils.io.core.internal.ChunkBuffer: java.lang.Object nextRef
io.ktor.utils.io.internal.CancellableReusableContinuation: java.lang.Object state
kotlinx.coroutines.InvokeOnCancelling: int _invoked$volatile
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting$volatile
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.AbstractApiMessage: com.example.everytalk.data.DataClass.AbstractApiMessage$Companion Companion
com.example.everytalk.data.DataClass.ChatRequest: kotlinx.serialization.KSerializer[] $childSerializers
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next$volatile
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType TEXT
com.example.everytalk.data.DataClass.GithubRelease$$serializer: int $stable
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state$volatile
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: int $stable
coil3.RealImageLoader: int shutdown$volatile
io.ktor.utils.io.ByteBufferChannel: kotlinx.coroutines.Job attachedJob
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float topP
io.ktor.util.collections.CopyOnWriteHashMap: java.lang.Object current
kotlinx.serialization.json.JsonNull: kotlinx.serialization.json.JsonNull INSTANCE
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: int _availablePermits$volatile
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment$volatile
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String name
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String mimeType
io.ktor.util.pipeline.Pipeline: java.lang.Object _interceptors
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float topP
androidx.compose.runtime.Recomposer$State: androidx.compose.runtime.Recomposer$State[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.compose.foundation.layout.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
androidx.core.content.FileProvider: FileProvider()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiContentPart$InlineData)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String toString()
com.example.everytalk.data.DataClass.ApiContentPart$Text: ApiContentPart$Text(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: kotlin.coroutines.CoroutineContext getCoroutineContext()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
com.example.everytalk.data.DataClass.ChatRequest: void getCustomModelParameters$annotations()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: com.example.everytalk.data.DataClass.SafetySetting deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Sender$Companion: Sender$Companion()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.example.everytalk.data.DataClass.SafetySetting: SafetySetting(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
kotlin.reflect.KVariance: kotlin.reflect.KVariance valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiResponse: void write$Self$app_release(com.example.everytalk.data.DataClass.GeminiApiResponse,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.material3.SnackbarResult: androidx.compose.material3.SnackbarResult valueOf(java.lang.String)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.data.DataClass.WebSearchResult: int component1()
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String getProbability()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: AbstractApiMessage$Companion()
androidx.compose.ui.platform.AndroidComposeView: void getFontLoader$annotations()
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.platform.AndroidComposeView: void setLayoutDirection(androidx.compose.ui.unit.LayoutDirection)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.AbstractApiMessage: AbstractApiMessage()
androidx.core.view.WindowInsetsCompat$BuilderImpl34: WindowInsetsCompat$BuilderImpl34()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
androidx.compose.ui.platform.AndroidComposeView: android.view.View findViewByAccessibilityIdTraversal(int)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.compose.material3.internal.TextFieldType: androidx.compose.material3.internal.TextFieldType[] values()
com.example.everytalk.data.DataClass.ChatRequest: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion: SimpleTextApiMessage$Companion()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
androidx.compose.ui.window.PopupLayout: android.view.WindowManager$LayoutParams getParams$ui_release()
androidx.compose.animation.core.AnimationEndReason: androidx.compose.animation.core.AnimationEndReason valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.input.InputModeManager getInputModeManager()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.ClipboardManager getClipboardManager()
com.example.everytalk.data.DataClass.Part$Text: Part$Text(int,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
coil3.size.Scale: coil3.size.Scale[] values()
androidx.compose.ui.contentcapture.ContentCaptureEventType: androidx.compose.ui.contentcapture.ContentCaptureEventType[] values()
androidx.compose.foundation.text.HandleState: androidx.compose.foundation.text.HandleState[] values()
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String getCategory()
com.example.everytalk.data.DataClass.Part$Text: int hashCode()
com.example.everytalk.data.DataClass.GeminiApiRequest: int hashCode()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender valueOf(java.lang.String)
androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand: androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand valueOf(java.lang.String)
androidx.compose.ui.viewinterop.ViewFactoryHolder: void setSavableRegistryEntry(androidx.compose.runtime.saveable.SaveableStateRegistry$Entry)
androidx.compose.ui.util.ListUtilsKt: void throwUnsupportedOperationException(java.lang.String)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.Set component2()
androidx.compose.ui.viewinterop.AndroidViewHolder: android.view.View getView()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.navigation.compose.BackStackEntryIdViewModel: BackStackEntryIdViewModel(androidx.lifecycle.SavedStateHandle)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$FileUri deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.statecontroller.LRUCache: java.util.Collection getValues()
androidx.compose.material3.ScaffoldLayoutContent: androidx.compose.material3.ScaffoldLayoutContent valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion: ApiContentPart$Text$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$Text: ApiContentPart$Text(int,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.graphics.path.PathIteratorPreApi34Impl: void destroyInternalPathIterator(long)
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String component2()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String toString()
androidx.core.view.WindowInsetsCompat$Impl34: WindowInsetsCompat$Impl34(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String getMimeType()
androidx.compose.ui.text.font.FontWeightAdjustmentHelperApi31: int fontWeightAdjustment(android.content.Context)
androidx.compose.ui.focus.FocusStateImpl: androidx.compose.ui.focus.FocusStateImpl[] values()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion: GeminiApiRequest$Companion()
com.example.everytalk.data.DataClass.ContentPart$Audio: ContentPart$Audio(java.lang.String,java.lang.String,java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
com.example.everytalk.data.DataClass.ContentPart$Audio: com.example.everytalk.data.DataClass.ContentPart$Audio copy$default(com.example.everytalk.data.DataClass.ContentPart$Audio,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion: kotlinx.serialization.KSerializer serializer()
kotlinx.serialization.json.DecodeSequenceMode: kotlinx.serialization.json.DecodeSequenceMode valueOf(java.lang.String)
kotlinx.serialization.json.ClassDiscriminatorMode: kotlinx.serialization.json.ClassDiscriminatorMode[] values()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig(java.lang.Boolean,java.lang.Integer)
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.GeminiApiResponse: int hashCode()
androidx.compose.ui.focus.FocusTargetNode$FocusTargetElement: FocusTargetNode$FocusTargetElement()
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function0 getUpdate()
io.ktor.util.date.Month: io.ktor.util.date.Month[] values()
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map component13()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String toString()
com.example.everytalk.data.DataClass.Content$$serializer: com.example.everytalk.data.DataClass.Content deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getAddress()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component2()
okhttp3.internal.publicsuffix.PublicSuffixDatabase: PublicSuffixDatabase()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component4()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: SafetySetting$$serializer()
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String getId()
androidx.compose.ui.window.PopupLayout: kotlin.jvm.functions.Function2 getContent()
androidx.compose.ui.viewinterop.ViewFactoryHolder: void setUpdateBlock(kotlin.jvm.functions.Function1)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
kotlinx.serialization.json.JsonPrimitive$Companion: kotlinx.serialization.KSerializer serializer()
kotlinx.serialization.json.JsonNull: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String getContentId()
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.PromptFeedback component2()
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean getForceGoogleReasoningPrompt()
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.compose.ui.unit.Density getDensity()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.PromptFeedback: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Part$InlineData: int hashCode()
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.AbstractApiMessage toApiMessage()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getProvider()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.SafetyRating: int hashCode()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getImageSize()
com.example.everytalk.data.DataClass.ChatRequest: void getMessages$annotations()
androidx.compose.ui.window.PopupLayout: android.view.View getViewRoot()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component13()
com.example.everytalk.data.DataClass.ChatRequest: void getApiKey$annotations()
com.example.everytalk.data.DataClass.GithubRelease$Companion: GithubRelease$Companion()
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.GeminiApiRequest: GeminiApiRequest(java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List)
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.platform.AndroidComposeView getOwnerView()
com.example.everytalk.data.DataClass.GenerationConfig: void write$Self$app_release(com.example.everytalk.data.DataClass.GenerationConfig,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Content component1()
androidx.core.view.WindowInsetsCompat$Impl34: WindowInsetsCompat$Impl34(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl34)
com.example.everytalk.data.DataClass.Candidate: void write$Self$app_release(com.example.everytalk.data.DataClass.Candidate,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.models.SelectedMediaItem$ImageFromUri$Companion: kotlinx.serialization.KSerializer serializer()
org.slf4j.event.Level: org.slf4j.event.Level[] values()
androidx.compose.ui.viewinterop.FocusGroupPropertiesElement: FocusGroupPropertiesElement()
androidx.compose.ui.window.PopupLayout: void setLayoutDirection(int)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: com.example.everytalk.statecontroller.AppViewModel$ExportedSettings copy$default(com.example.everytalk.statecontroller.AppViewModel$ExportedSettings,java.util.List,java.util.Set,int,java.lang.Object)
com.example.everytalk.data.network.AppStreamEvent$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String getMimeType()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.contentcapture.AndroidContentCaptureManager getContentCaptureManager$ui_release()
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String getMimeType()
org.slf4j.helpers.Reporter$TargetChoice: org.slf4j.helpers.Reporter$TargetChoice[] values()
com.example.everytalk.data.DataClass.ThinkingConfig: com.example.everytalk.data.DataClass.ThinkingConfig copy$default(com.example.everytalk.data.DataClass.ThinkingConfig,java.lang.Boolean,java.lang.Integer,int,java.lang.Object)
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType[] $values()
com.example.everytalk.data.DataClass.ApiContentPart$Text: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: com.example.everytalk.data.DataClass.PromptFeedback deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.unit.IntRect getVisibleDisplayBounds()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List component1()
androidx.compose.ui.unit.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.spatial.RectManager getRectManager()
androidx.compose.ui.platform.AndroidViewsHandler: java.util.HashMap getLayoutNodeToHolder()
com.example.everytalk.data.DataClass.Message: boolean component5()
androidx.compose.ui.platform.AndroidComposeViewVerificationHelperMethodsN: void setPointerIcon(android.view.View,androidx.compose.ui.input.pointer.PointerIcon)
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String component2()
com.example.everytalk.statecontroller.LRUCache: int getSize()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.compose.material3.tokens.ShapeKeyTokens: androidx.compose.material3.tokens.ShapeKeyTokens[] values()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String getTitle()
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Part$InlineData: void write$Self$app_release(com.example.everytalk.data.DataClass.Part$InlineData,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.LayoutNodeDrawScope getSharedDrawScope()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: ApiContentPart$Text$$serializer()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: int hashCode()
com.example.everytalk.data.DataClass.ChatRequest: void getUseWebSearch$annotations()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: SafetyRating$$serializer()
com.example.everytalk.data.DataClass.Part$Companion: Part$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.PromptFeedback: com.example.everytalk.data.DataClass.PromptFeedback copy$default(com.example.everytalk.data.DataClass.PromptFeedback,java.util.List,int,java.lang.Object)
com.example.everytalk.data.DataClass.Candidate: Candidate(int,com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.Part$InlineData$Companion: Part$InlineData$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.unit.LayoutDirection getLayoutDirection()
androidx.core.view.WindowInsetsCompat$Impl34: androidx.core.graphics.Insets getInsets(int)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.network.AppStreamEvent$StreamEnd$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.input.pointer.PointerEventPass: androidx.compose.ui.input.pointer.PointerEventPass[] values()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig(java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: ChatRequest$$serializer()
androidx.core.view.WindowInsetsCompat$Impl34: boolean isVisible(int)
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.Message: java.util.List getImageUrls()
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Boolean component1()
androidx.compose.ui.platform.AndroidComposeView: boolean getHasPendingMeasureOrLayout()
com.example.everytalk.data.DataClass.PromptFeedback: int hashCode()
androidx.compose.runtime.collection.MutableVectorKt: void throwReversedIndicesException(int,int)
com.example.everytalk.data.DataClass.ContentPart$Audio: boolean equals(java.lang.Object)
androidx.compose.runtime.collection.MutableVectorKt: void throwOutOfRangeException(int,int)
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List getContents()
com.example.everytalk.data.DataClass.Content: java.lang.String toString()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String component4()
androidx.compose.ui.platform.AbstractComposeView: void setShowLayoutBounds(boolean)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: com.example.everytalk.data.DataClass.GenerationConfig deserialize(kotlinx.serialization.encoding.Decoder)
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorRawSize(long)
androidx.compose.ui.node.LayoutNode$LayoutState: androidx.compose.ui.node.LayoutNode$LayoutState[] values()
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse(int,java.util.List,com.example.everytalk.data.DataClass.PromptFeedback,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.foundation.text.Handle: androidx.compose.foundation.text.Handle[] values()
com.example.everytalk.data.DataClass.GenerationConfig: void getMaxOutputTokens$annotations()
androidx.compose.ui.platform.DrawChildContainer: int getChildCount()
androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand: androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand[] values()
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse()
com.example.everytalk.data.DataClass.WebSearchResult$Companion: com.example.everytalk.data.DataClass.WebSearchResult fromMap(java.util.Map)
androidx.activity.EdgeToEdgeApi29: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.compose.ui.window.PopupLayout: void setPopupContentSize-fhxjrPA(androidx.compose.ui.unit.IntSize)
androidx.compose.ui.platform.AndroidComposeView: void setFontFamilyResolver(androidx.compose.ui.text.font.FontFamily$Resolver)
com.example.everytalk.data.DataClass.Content: java.util.List getParts()
androidx.core.view.WindowInsetsCompat$Impl30: boolean isVisible(int)
com.example.everytalk.data.DataClass.WebSearchResult: com.example.everytalk.data.DataClass.WebSearchResult copy$default(com.example.everytalk.data.DataClass.WebSearchResult,int,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.lifecycle.LifecycleOwner getLifecycleOwner()
com.example.everytalk.data.DataClass.SafetyRating: void write$Self$app_release(com.example.everytalk.data.DataClass.SafetyRating,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig(java.lang.Boolean,java.lang.Integer,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.Set getCustomProviders()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean getQwenEnableSearch()
com.example.everytalk.data.DataClass.Candidate$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Candidate)
com.example.everytalk.data.DataClass.PromptFeedback: java.util.List getSafetyRatings()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GeminiApiResponse)
androidx.compose.ui.input.pointer.PointerInteropFilter$DispatchToViewState: androidx.compose.ui.input.pointer.PointerInteropFilter$DispatchToViewState[] values()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String getBody()
com.example.everytalk.data.DataClass.Message: java.lang.String toString()
com.example.everytalk.data.DataClass.Part$Text: java.lang.String component1()
okhttp3.Protocol: okhttp3.Protocol valueOf(java.lang.String)
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function1 getOnDensityChanged$ui_release()
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: void write$Self$app_release(com.example.everytalk.statecontroller.AppViewModel$ExportedSettings,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.WebSearchResult: void write$Self$app_release(com.example.everytalk.data.DataClass.WebSearchResult,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.material3.DrawerValue: androidx.compose.material3.DrawerValue valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: ApiContentPart$FileUri(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$Companion: ApiContentPart$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction: androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction[] values()
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
com.example.everytalk.data.DataClass.Content: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.DataClass.Candidate$Companion: Candidate$Companion()
androidx.compose.ui.platform.AbstractComposeView: boolean getShowLayoutBounds()
androidx.compose.ui.platform.ViewLayer: float[] getUnderlyingMatrix-sQKQjiQ()
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
kotlinx.serialization.json.DecodeSequenceMode: kotlinx.serialization.json.DecodeSequenceMode[] values()
com.example.everytalk.data.DataClass.Content: boolean equals(java.lang.Object)
androidx.compose.ui.platform.AndroidComposeView: long getMeasureIteration()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setModifier(androidx.compose.ui.Modifier)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer component11()
com.example.everytalk.statecontroller.MainActivity: MainActivity()
androidx.compose.animation.core.PreconditionsKt: void throwIllegalArgumentException(java.lang.String)
androidx.compose.ui.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.Part$InlineData: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Part$Text$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ApiContentPart: void write$Self(com.example.everytalk.data.DataClass.ApiContentPart,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Part$FileUri$Companion: Part$FileUri$Companion()
androidx.core.view.WindowInsetsCompat$Impl: void setSystemUiVisibility(int)
com.example.everytalk.data.DataClass.ThinkingConfig: void getThinkingBudget$annotations()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GeminiApiRequest)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.Autofill getAutofill()
androidx.compose.foundation.layout.Direction: androidx.compose.foundation.layout.Direction valueOf(java.lang.String)
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function0 getReset()
com.example.everytalk.data.DataClass.Message$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Message: java.lang.String component4()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getProvider()
com.example.everytalk.data.DataClass.GenerationConfig: int hashCode()
com.example.everytalk.statecontroller.LRUCache: int size()
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String component1()
com.example.everytalk.data.DataClass.Message: java.lang.String getReasoning()
androidx.appcompat.resources.Compatibility$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.core.view.WindowInsetsCompat$BuilderImpl34: WindowInsetsCompat$BuilderImpl34(androidx.core.view.WindowInsetsCompat)
androidx.compose.ui.platform.actionmodecallback.MenuItemOption: androidx.compose.ui.platform.actionmodecallback.MenuItemOption valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion: GeminiApiRequest$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: GeminiApiResponse$$serializer()
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig(int,java.lang.Boolean,java.lang.Integer,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.graphics.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.Content: void write$Self$app_release(com.example.everytalk.data.DataClass.Content,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String toString()
com.example.everytalk.data.DataClass.ModalityType$Companion: com.example.everytalk.data.DataClass.ModalityType fromDisplayName(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest$Companion: ChatRequest$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.PromptFeedback: com.example.everytalk.data.DataClass.PromptFeedback copy(java.util.List)
kotlin.reflect.KVariance: kotlin.reflect.KVariance[] values()
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
androidx.compose.ui.platform.AndroidComposeView: long getLastMatrixRecalculationAnimationTime$ui_release()
com.example.everytalk.data.DataClass.PartsApiMessage: PartsApiMessage(java.lang.String,java.lang.String,java.util.List,java.lang.String,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.ViewConfiguration getViewConfiguration()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getModel()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AndroidAutofillManager get_autofillManager$ui_release()
androidx.compose.ui.graphics.layer.ViewLayer: void setInvalidated(boolean)
androidx.compose.ui.node.LayoutNode$LayoutState: androidx.compose.ui.node.LayoutNode$LayoutState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: ApiConfig(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Part$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
org.slf4j.helpers.Reporter$Level: org.slf4j.helpers.Reporter$Level[] values()
com.example.everytalk.data.DataClass.AbstractApiMessage: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map getCustomExtraBody()
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List getSafetySettings()
androidx.compose.ui.platform.AndroidComposeView: void getShowLayoutBounds$annotations()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float getTopP()
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setReset(kotlin.jvm.functions.Function0)
com.example.everytalk.data.DataClass.WebSearchResult: WebSearchResult(int,java.lang.String,java.lang.String,java.lang.String)
androidx.compose.ui.platform.AndroidViewsHandler: java.util.HashMap getHolderToLayoutNode()
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Candidate copy$default(com.example.everytalk.data.DataClass.Candidate,com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List,int,java.lang.Object)
com.example.everytalk.data.DataClass.Content$$serializer: Content$$serializer()
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
com.example.everytalk.data.DataClass.Candidate: boolean equals(java.lang.Object)
coil3.size.Precision: coil3.size.Precision valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
com.example.everytalk.data.DataClass.Candidate: int hashCode()
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GeminiApiRequest copy$default(com.example.everytalk.data.DataClass.GeminiApiRequest,java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,int,java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: SimpleTextApiMessage(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Message: boolean getContentStarted()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String component4()
io.ktor.client.plugins.cache.ValidateStatus: io.ktor.client.plugins.cache.ValidateStatus valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
com.example.everytalk.data.DataClass.Message: java.lang.String getCurrentWebSearchStage()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String component2()
androidx.compose.ui.platform.AndroidComposeView: void setConfigurationChangeObserver(kotlin.jvm.functions.Function1)
androidx.compose.runtime.collection.MutableVectorKt: void throwNegativeIndexException(int)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.font.Font$ResourceLoader getFontLoader()
androidx.core.view.WindowInsetsCompat$Impl20: void setSystemUiVisibility(int)
androidx.compose.ui.window.PopupLayout: void setParentLayoutDirection(androidx.compose.ui.unit.LayoutDirection)
com.example.everytalk.data.DataClass.Part$FileUri: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ContentPart: ContentPart(java.lang.String,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Candidate$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.SafetyRating: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.PromptFeedback: PromptFeedback(int,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiContentPart$FileUri)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component2()
com.example.everytalk.data.DataClass.Part$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List component3()
com.example.everytalk.data.DataClass.ThinkingConfig$Companion: kotlinx.serialization.KSerializer serializer()
androidx.lifecycle.ReportFragment: ReportFragment()
com.example.everytalk.data.DataClass.Message$$serializer: com.example.everytalk.data.DataClass.Message deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Message: Message(java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.ContentPart$Code: int hashCode()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ChatRequest: void getProvider$annotations()
kotlinx.serialization.json.internal.WriteMode: kotlinx.serialization.json.internal.WriteMode[] values()
androidx.activity.EdgeToEdgeApi21: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorPeek(long)
com.example.everytalk.data.DataClass.ContentPart: java.lang.String getContentId()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void write$Self$app_release(com.example.everytalk.data.DataClass.SimpleTextApiMessage,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.AbstractApiMessage: void write$Self(com.example.everytalk.data.DataClass.AbstractApiMessage,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Part$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Part$Text)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.Clipboard getClipboard()
com.example.everytalk.data.DataClass.Sender$Companion: Sender$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.foundation.layout.IntrinsicSize: androidx.compose.foundation.layout.IntrinsicSize[] values()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float component15()
androidx.compose.ui.platform.AndroidCompositionLocals_androidKt: androidx.compose.runtime.ProvidableCompositionLocal getLocalLifecycleOwner()
com.example.everytalk.data.DataClass.ApiContentPart: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
androidx.compose.runtime.InvalidationResult: androidx.compose.runtime.InvalidationResult[] values()
com.example.everytalk.data.DataClass.ModalityType: ModalityType(java.lang.String,int,java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease: void getBody$annotations()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: com.example.everytalk.data.DataClass.WebSearchResult deserialize(kotlinx.serialization.encoding.Decoder)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
coil3.decode.DataSource: coil3.decode.DataSource valueOf(java.lang.String)
androidx.compose.ui.window.PopupLayout: boolean getCanCalculatePosition()
androidx.compose.material3.ModalBottomSheetDialogLayout$Api33Impl: android.window.OnBackInvokedCallback createBackCallback(kotlin.jvm.functions.Function0)
androidx.compose.ui.platform.AbstractComposeView: void setTransitionGroup(boolean)
com.example.everytalk.data.DataClass.PartsApiMessage: void write$Self$app_release(com.example.everytalk.data.DataClass.PartsApiMessage,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig(int,java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.GithubRelease$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.focus.FocusStateImpl: androidx.compose.ui.focus.FocusStateImpl valueOf(java.lang.String)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.PromptFeedback)
com.example.everytalk.data.DataClass.Content$Companion: Content$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.PromptFeedback getPromptFeedback()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.unit.LayoutDirection getParentLayoutDirection()
com.example.everytalk.data.DataClass.SafetySetting: int hashCode()
com.example.everytalk.data.DataClass.ModalityType: kotlin.enums.EnumEntries getEntries()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.focus.FocusOwner getFocusOwner()
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String getThreshold()
com.example.everytalk.data.DataClass.ApiConfig$Companion: ApiConfig$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String toString()
com.example.everytalk.data.DataClass.GeminiApiRequest: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.ui.unit.LayoutDirection: androidx.compose.ui.unit.LayoutDirection valueOf(java.lang.String)
kotlin.io.encoding.Base64$PaddingOption: kotlin.io.encoding.Base64$PaddingOption[] values()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setOnRequestDisallowInterceptTouchEvent$ui_release(kotlin.jvm.functions.Function1)
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
io.ktor.client.plugins.cache.ValidateStatus: io.ktor.client.plugins.cache.ValidateStatus[] values()
com.example.everytalk.data.DataClass.PartsApiMessage: void getName$annotations()
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String getText()
com.example.everytalk.data.DataClass.WebSearchResult: int hashCode()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String component1()
androidx.compose.foundation.text.selection.SelectionHandleAnchor: androidx.compose.foundation.text.selection.SelectionHandleAnchor valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.activity.EdgeToEdgeApi26: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getName()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.SafetySetting$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component3()
com.example.everytalk.data.DataClass.PromptFeedback$Companion: PromptFeedback$Companion()
com.example.everytalk.data.DataClass.ChatRequest: void getCustomExtraBody$annotations()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.compose.runtime.ComposerKt: java.lang.Void composeRuntimeError(java.lang.String)
com.example.everytalk.data.DataClass.Candidate$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Sender$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.ViewLayer: long getOwnerViewId()
com.example.everytalk.data.DataClass.GithubRelease: GithubRelease(int,java.lang.String,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.statecontroller.LRUCache: boolean removeEldestEntry(java.util.Map$Entry)
androidx.compose.ui.platform.AndroidComposeView: androidx.collection.MutableIntObjectMap getLayoutNodes()
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ApiContentPart$Companion: ApiContentPart$Companion()
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.savedstate.SavedStateRegistryOwner getSavedStateRegistryOwner()
com.example.everytalk.data.DataClass.ApiConfig: boolean isValid()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.compose.material.ripple.RippleHostView: void setRippleState$lambda$2(androidx.compose.material.ripple.RippleHostView)
com.example.everytalk.data.DataClass.GithubRelease: int hashCode()
com.example.everytalk.data.DataClass.Message: java.lang.String getId()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ApiContentPart$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
io.ktor.util.Platform: io.ktor.util.Platform[] values()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.Part$InlineData: Part$InlineData(java.lang.String,java.lang.String)
kotlinx.serialization.json.internal.WriteMode: kotlinx.serialization.json.internal.WriteMode valueOf(java.lang.String)
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: GithubRelease$$serializer()
com.example.everytalk.data.DataClass.ApiConfig: void write$Self$app_release(com.example.everytalk.data.DataClass.ApiConfig,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.compose.ui.node.OwnerSnapshotObserver getSnapshotObserver()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.platform.AndroidComposeViewTranslationCallbackS: void clearViewTranslationCallback(android.view.View)
com.example.everytalk.data.DataClass.GithubRelease: boolean equals(java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.compose.animation.core.MutatePriority: androidx.compose.animation.core.MutatePriority[] values()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: ThinkingConfig$$serializer()
androidx.compose.ui.platform.ViewLayer: void setCameraDistancePx(float)
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax[] values()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer component14()
com.example.everytalk.data.DataClass.Part: Part()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setOnModifierChanged$ui_release(kotlin.jvm.functions.Function1)
io.ktor.util.Platform: io.ktor.util.Platform valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Candidate$Companion: Candidate$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.network.AppStreamEvent$ToolCall$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
com.example.everytalk.data.DataClass.Message: int hashCode()
androidx.compose.animation.core.RepeatMode: androidx.compose.animation.core.RepeatMode valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: long component8()
androidx.core.view.WindowInsetsCompat$Impl20: boolean systemBarVisibilityEquals(int,int)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax[] values()
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion: GeminiApiResponse$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion: ApiContentPart$FileUri$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.runtime.PreconditionsKt: void throwIllegalStateException(java.lang.String)
androidx.compose.ui.platform.ComposeView: void getShouldCreateCompositionOnAttachedToWindow$annotations()
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.compose.ui.platform.AndroidComposeView: androidx.collection.IntObjectMap getLayoutNodes()
com.example.everytalk.data.DataClass.Message: java.util.List component12()
androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy: androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy[] values()
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String component1()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String getHref()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Content$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.draganddrop.AndroidDragAndDropManager getDragAndDropManager()
androidx.navigation.NavControllerViewModel: NavControllerViewModel()
coil3.request.CachePolicy: coil3.request.CachePolicy[] values()
com.example.everytalk.data.DataClass.ApiConfig: float component9()
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion: ApiContentPart$Text$Companion()
com.example.everytalk.data.DataClass.Part: kotlinx.serialization.KSerializer _init_$_anonymous_()
androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus: androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.network.AppStreamEvent$WebSearchResults$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.material3.internal.InputPhase: androidx.compose.material3.internal.InputPhase[] values()
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.GenerationConfig getGenerationConfig()
androidx.compose.ui.viewinterop.ViewFactoryHolder: kotlin.jvm.functions.Function1 getReleaseBlock()
androidx.compose.ui.platform.AbstractComposeView: void getDisposeViewCompositionStrategy$annotations()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: com.example.everytalk.data.DataClass.SafetyRating deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String toString()
org.slf4j.event.Level: org.slf4j.event.Level valueOf(java.lang.String)
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String toString()
com.example.everytalk.data.DataClass.Content: java.util.List component1()
com.example.everytalk.data.DataClass.SafetySetting: com.example.everytalk.data.DataClass.SafetySetting copy$default(com.example.everytalk.data.DataClass.SafetySetting,java.lang.String,java.lang.String,int,java.lang.Object)
okhttp3.internal.http2.ErrorCode: okhttp3.internal.http2.ErrorCode valueOf(java.lang.String)
kotlinx.serialization.json.JsonElement$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.AbstractComposeView: void setParentContext(androidx.compose.runtime.CompositionContext)
io.ktor.serialization.kotlinx.json.KotlinxSerializationJsonExtensionProvider: KotlinxSerializationJsonExtensionProvider()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Integer getMaxOutputTokens()
com.example.everytalk.data.DataClass.ContentPart$Html: com.example.everytalk.data.DataClass.ContentPart$Html copy$default(com.example.everytalk.data.DataClass.ContentPart$Html,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ContentPart$Html: com.example.everytalk.data.DataClass.ContentPart$Html copy(java.lang.String,java.lang.String)
kotlinx.coroutines.flow.SharingCommand: kotlinx.coroutines.flow.SharingCommand valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.activity.ComponentActivity: void setContentView(android.view.View)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: void getBase64Data$annotations()
okhttp3.TlsVersion: okhttp3.TlsVersion[] values()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component3()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Candidate$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: AppViewModel$ExportedSettings(java.util.List,java.util.Set)
com.example.everytalk.data.DataClass.GenerationConfig: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
com.example.everytalk.data.DataClass.Content: Content(java.util.List,java.lang.String)
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorSize(long)
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.GeminiApiResponse copy(java.util.List,com.example.everytalk.data.DataClass.PromptFeedback)
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String component1()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion: ApiContentPart$InlineData$Companion()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: GenerationConfig$$serializer()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: ApiContentPart$InlineData(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.foundation.MutatePriority: androidx.compose.foundation.MutatePriority[] values()
com.example.everytalk.data.DataClass.SafetyRating: SafetyRating(java.lang.String,java.lang.String)
androidx.compose.ui.viewinterop.ViewFactoryHolder: kotlin.jvm.functions.Function1 getResetBlock()
com.example.everytalk.data.DataClass.SafetySetting$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Candidate$$serializer: com.example.everytalk.data.DataClass.Candidate deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.AndroidComposeView: void setShowLayoutBounds(boolean)
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.layout.LayoutCoordinates getParentLayoutCoordinates()
androidx.compose.ui.graphics.layer.view.DrawChildContainer: int getChildCount()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setLifecycleOwner(androidx.lifecycle.LifecycleOwner)
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String component1()
kotlin.time.DurationUnit: kotlin.time.DurationUnit[] values()
com.example.everytalk.data.DataClass.ChatRequest: void getGenerationConfig$annotations()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String getContentId()
androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy: androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy valueOf(java.lang.String)
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.compose.ui.Modifier getModifier()
androidx.compose.ui.layout.IntrinsicWidthHeight: androidx.compose.ui.layout.IntrinsicWidthHeight[] values()
com.example.everytalk.models.SelectedMediaItem$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setDensity(androidx.compose.ui.unit.Density)
androidx.compose.ui.autofill.AutofillType: androidx.compose.ui.autofill.AutofillType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: ApiContentPart$FileUri(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Content getContent()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setUpdate(kotlin.jvm.functions.Function0)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: com.example.everytalk.data.DataClass.GeminiApiResponse deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.PartsApiMessage: void getId$annotations()
com.example.everytalk.data.DataClass.Part$InlineData: Part$InlineData(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.Message: java.lang.String component7()
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.GenerationConfig component8()
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
com.example.everytalk.statecontroller.LRUCache: java.util.Set entrySet()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String component3()
androidx.compose.ui.platform.MotionEventVerifierApi29: boolean isValidMotionEvent(android.view.MotionEvent,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.platform.AbstractComposeView getSubCompositionView()
com.example.everytalk.ui.screens.MainScreen.AiMessageOption: com.example.everytalk.ui.screens.MainScreen.AiMessageOption valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String component1()
com.example.everytalk.data.DataClass.ContentPart$Code: com.example.everytalk.data.DataClass.ContentPart$Code copy$default(com.example.everytalk.data.DataClass.ContentPart$Code,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getKey()
com.example.everytalk.data.DataClass.PartsApiMessage: com.example.everytalk.data.DataClass.PartsApiMessage copy(java.lang.String,java.lang.String,java.util.List,java.lang.String)
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender[] values()
com.example.everytalk.data.DataClass.Candidate: int getIndex()
com.example.everytalk.data.DataClass.Candidate: java.lang.String toString()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String component1()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.compose.ui.platform.AndroidComposeViewVerificationHelperMethodsO: void focusable(android.view.View,int,boolean)
com.example.everytalk.data.DataClass.PartsApiMessage: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.GenerationConfig: void getTopP$annotations()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Part$Text$Companion: Part$Text$Companion()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
com.example.everytalk.data.DataClass.GeminiApiRequest: java.lang.String toString()
androidx.compose.ui.platform.AndroidComposeView: int getImportantForAutofill()
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.graphics.path.PathIteratorPreApi34Impl: boolean internalPathIteratorHasNext(long)
com.example.everytalk.data.DataClass.ThinkingConfig: void getIncludeThoughts$annotations()
com.example.everytalk.data.DataClass.Content$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Content)
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.network.AppStreamEvent$Text$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: com.example.everytalk.data.DataClass.SimpleTextApiMessage deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
com.example.everytalk.data.DataClass.GithubRelease$Companion: GithubRelease$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Part$Companion: Part$Companion()
com.example.everytalk.data.DataClass.Message: java.util.List getAttachments()
com.example.everytalk.data.DataClass.ChatRequest$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: int hashCode()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getId()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.internal.InlineClassHelperKt: void throwIndexOutOfBoundsException(java.lang.String)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.SimpleTextApiMessage)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component1()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String component2()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidClipboardManager getClipboardManager()
coil3.request.CachePolicy: coil3.request.CachePolicy valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
com.example.everytalk.statecontroller.LRUCache: java.util.Collection values()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getName()
io.ktor.util.date.Month: io.ktor.util.date.Month valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Message copy$default(com.example.everytalk.data.DataClass.Message,java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List,int,java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest: void getForceGoogleReasoningPrompt$annotations()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.layout.Placeable$PlacementScope getPlacementScope()
com.example.everytalk.data.DataClass.GithubRelease: void getHtmlUrl$annotations()
com.example.everytalk.data.DataClass.PromptFeedback$Companion: PromptFeedback$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float getTemperature()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
androidx.compose.ui.input.pointer.PointerInteropFilter$DispatchToViewState: androidx.compose.ui.input.pointer.PointerInteropFilter$DispatchToViewState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.Part$FileUri: void write$Self$app_release(com.example.everytalk.data.DataClass.Part$FileUri,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String component2()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.example.everytalk.data.DataClass.Part$FileUri: Part$FileUri(java.lang.String)
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String component3()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ApiConfig copy(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float)
androidx.compose.ui.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.network.AppStreamEvent$StatusUpdate$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SafetyRating: com.example.everytalk.data.DataClass.SafetyRating copy$default(com.example.everytalk.data.DataClass.SafetyRating,java.lang.String,java.lang.String,int,java.lang.Object)
androidx.compose.ui.unit.ConstraintsKt: java.lang.Void throwInvalidConstraintsSizeException(int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String getData()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String getRole()
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Candidate copy(com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Message: java.lang.String getRole()
androidx.activity.EdgeToEdgeApi23: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
com.example.everytalk.data.DataClass.Message: java.util.List component10()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String component2()
com.example.everytalk.data.DataClass.ContentPart$Code: ContentPart$Code(java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Message: java.util.List getWebSearchResults()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float getGuidanceScale()
com.example.everytalk.data.DataClass.WebSearchResult: int getIndex()
androidx.compose.material3.SheetValue: androidx.compose.material3.SheetValue valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.ui.window.SecureFlagPolicy: androidx.compose.ui.window.SecureFlagPolicy valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.unit.ConstraintsKt: void throwInvalidConstraintException(int,int)
com.example.everytalk.data.DataClass.Sender: kotlin.enums.EnumEntries getEntries()
com.example.everytalk.data.DataClass.SafetyRating$Companion: SafetyRating$Companion()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.List component1()
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String component2()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getName$annotations()
androidx.activity.EdgeToEdgeApi30: void adjustLayoutInDisplayCutoutMode(android.view.Window)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
com.example.everytalk.data.DataClass.Part$Text: java.lang.String toString()
androidx.compose.material3.internal.InputPhase: androidx.compose.material3.internal.InputPhase valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ModalityType getModalityType()
androidx.compose.ui.platform.AbstractComposeView: void setPreviousAttachedWindowToken(android.os.IBinder)
androidx.compose.ui.graphics.AndroidPath_androidKt: void throwIllegalStateException(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.compose.ui.graphics.layer.ViewLayer: androidx.compose.ui.graphics.CanvasHolder getCanvasHolder()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Object component10()
androidx.graphics.path.ConicConverter: int internalConicToQuadratics(float[],int,float[],float,float)
androidx.compose.ui.platform.AndroidComposeView: boolean getScrollCaptureInProgress$ui_release()
com.example.everytalk.data.DataClass.Message: java.lang.String component1()
com.example.everytalk.data.DataClass.Part$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
com.example.everytalk.data.DataClass.ThinkingConfig: void write$Self$app_release(com.example.everytalk.data.DataClass.ThinkingConfig,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.foundation.text.selection.Direction: androidx.compose.foundation.text.selection.Direction[] values()
androidx.compose.ui.input.pointer.PointerEventPass: androidx.compose.ui.input.pointer.PointerEventPass valueOf(java.lang.String)
com.example.everytalk.data.DataClass.WebSearchResult: com.example.everytalk.data.DataClass.WebSearchResult copy(int,java.lang.String,java.lang.String,java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.compose.ui.viewinterop.AndroidViewHolder: java.lang.CharSequence getAccessibilityClassName()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: int hashCode()
com.example.everytalk.data.DataClass.Candidate: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.compose.ui.text.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback: java.util.List component1()
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight valueOf(java.lang.String)
androidx.compose.foundation.internal.InlineClassHelperKt: java.lang.Void throwIllegalArgumentExceptionForNullCheck(java.lang.String)
androidx.compose.animation.core.AnimationEndReason: androidx.compose.animation.core.AnimationEndReason[] values()
androidx.compose.foundation.text.HandleState: androidx.compose.foundation.text.HandleState valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.LayoutNode getRoot()
androidx.compose.ui.platform.AndroidComposeView: void getTextInputService$annotations()
com.example.everytalk.data.DataClass.Sender: kotlinx.serialization.KSerializer _init_$_anonymous_()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: com.example.everytalk.data.DataClass.ApiContentPart$InlineData copy(java.lang.String,java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.input.TextInputService getTextInputService()
com.example.everytalk.data.DataClass.Candidate: java.util.List getSafetyRatings()
androidx.compose.animation.EnterExitState: androidx.compose.animation.EnterExitState[] values()
com.example.everytalk.data.DataClass.WebSearchResult$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiConfig$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Part$Text: Part$Text(java.lang.String)
androidx.compose.ui.node.LookaheadPassDelegate$PlacedState: androidx.compose.ui.node.LookaheadPassDelegate$PlacedState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.layout.IntrinsicMinMax: androidx.compose.ui.layout.IntrinsicMinMax[] values()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: ApiContentPart$InlineData(java.lang.String,java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
com.example.everytalk.util.CorrectionIntensity: com.example.everytalk.util.CorrectionIntensity[] values()
com.example.everytalk.data.DataClass.Content: java.lang.String getRole()
androidx.compose.ui.window.PopupLayout: boolean getShouldCreateCompositionOnAttachedToWindow()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.GeminiApiResponse: boolean equals(java.lang.Object)
androidx.compose.ui.viewinterop.AndroidViewHolder: int getNestedScrollAxes()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Boolean getDefaultUseWebSearch()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.draganddrop.DragAndDropManager getDragAndDropManager()
androidx.core.view.WindowInsetsCompat$Impl20: boolean isTypeVisible(int)
com.example.everytalk.data.DataClass.PartsApiMessage: PartsApiMessage(java.lang.String,java.lang.String,java.util.List,java.lang.String)
androidx.compose.ui.text.AnnotationType: androidx.compose.ui.text.AnnotationType[] values()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.example.everytalk.data.network.AppStreamEvent$Finish$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GenerationConfig getGenerationConfig()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.node.LookaheadPassDelegate$PlacedState: androidx.compose.ui.node.LookaheadPassDelegate$PlacedState[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl30: void setInsets(int,androidx.core.graphics.Insets)
com.example.everytalk.models.ImageSourceOption: com.example.everytalk.models.ImageSourceOption valueOf(java.lang.String)
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
com.example.everytalk.data.network.AppStreamEvent$Reasoning$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Message: boolean component6()
com.example.everytalk.data.DataClass.PartsApiMessage$Companion: PartsApiMessage$Companion()
androidx.core.graphics.drawable.IconCompat: IconCompat()
com.example.everytalk.data.DataClass.ApiContentPart$Text: void write$Self$app_release(com.example.everytalk.data.DataClass.ApiContentPart$Text,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Content$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.ThinkingConfig getThinkingConfig()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float component1()
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
com.example.everytalk.data.DataClass.Part$FileUri: com.example.everytalk.data.DataClass.Part$FileUri copy(java.lang.String)
com.example.everytalk.data.DataClass.SafetySetting$Companion: SafetySetting$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.compose.ui.window.PopupLayout: java.lang.String getTestTag()
androidx.compose.ui.platform.AndroidComposeView: void getLastMatrixRecalculationAnimationTime$ui_release$annotations()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.GenerationConfig copy$default(com.example.everytalk.data.DataClass.GenerationConfig,java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig,int,java.lang.Object)
com.example.everytalk.data.DataClass.Part: Part(int,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String getSnippet()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: com.example.everytalk.data.DataClass.SimpleTextApiMessage copy(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
androidx.compose.foundation.text.selection.CrossStatus: androidx.compose.foundation.text.selection.CrossStatus[] values()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String component2()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.compose.material3.tokens.TypographyKeyTokens: androidx.compose.material3.tokens.TypographyKeyTokens[] values()
androidx.compose.material3.SnackbarDuration: androidx.compose.material3.SnackbarDuration valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: long getTimestamp()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.lang.String toString()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: com.example.everytalk.data.DataClass.ApiContentPart$FileUri copy$default(com.example.everytalk.data.DataClass.ApiContentPart$FileUri,java.lang.String,java.lang.String,int,java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.example.everytalk.statecontroller.LRUCache: java.util.Set getKeys()
com.example.everytalk.data.DataClass.SafetySetting: void write$Self$app_release(com.example.everytalk.data.DataClass.SafetySetting,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ThinkingConfig)
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.statecontroller.LRUCache: LRUCache(int)
androidx.compose.material3.tokens.ColorSchemeKeyTokens: androidx.compose.material3.tokens.ColorSchemeKeyTokens valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String component3()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.compose.ui.state.ToggleableState: androidx.compose.ui.state.ToggleableState valueOf(java.lang.String)
com.example.everytalk.statecontroller.LRUCache: java.util.Set keySet()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: GeminiApiRequest$$serializer()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
com.example.everytalk.data.DataClass.ContentPart$Audio: int hashCode()
com.example.everytalk.data.DataClass.ChatRequest: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: void getMimeType$annotations()
androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction: androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
com.example.everytalk.data.DataClass.GithubRelease: void getTagName$annotations()
com.example.everytalk.data.DataClass.PartsApiMessage: java.util.List component3()
com.example.everytalk.data.DataClass.ChatRequest: ChatRequest(java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean component11()
com.example.everytalk.data.DataClass.Part$Text$Companion: Part$Text$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: AppViewModel$ExportedSettings(int,java.util.List,java.util.Set,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: void write$Self$app_release(com.example.everytalk.data.DataClass.ApiContentPart$FileUri,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.SafetySetting$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ContentPart$Code: com.example.everytalk.data.DataClass.ContentPart$Code copy(java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri$Companion: kotlinx.serialization.KSerializer serializer()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
com.example.everytalk.data.DataClass.IMessage: java.lang.String getId()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
com.example.everytalk.models.SelectedMediaItem$GenericFile$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.ui.window.SecureFlagPolicy: androidx.compose.ui.window.SecureFlagPolicy[] values()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.graphics.GraphicsContext getGraphicsContext()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float getTopP()
androidx.compose.foundation.layout.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease: com.example.everytalk.data.DataClass.GithubRelease copy$default(com.example.everytalk.data.DataClass.GithubRelease,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.GeminiApiRequest: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.graphics.Path getManualClipPath()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$InlineData deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender[] $values()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.foundation.internal.InlineClassHelperKt: java.lang.Void throwIllegalStateExceptionForNullCheck(java.lang.String)
androidx.compose.ui.focus.CustomDestinationResult: androidx.compose.ui.focus.CustomDestinationResult[] values()
androidx.compose.material3.SheetValue: androidx.compose.material3.SheetValue[] values()
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.compose.ui.viewinterop.FocusTargetPropertiesElement: FocusTargetPropertiesElement()
androidx.compose.ui.platform.AndroidComposeView: void setDensity(androidx.compose.ui.unit.Density)
com.example.everytalk.data.DataClass.ApiContentPart$Text: com.example.everytalk.data.DataClass.ApiContentPart$Text copy(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.SoftwareKeyboardController getSoftwareKeyboardController()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
com.example.everytalk.util.IncrementalMarkdownParser$TokenType: com.example.everytalk.util.IncrementalMarkdownParser$TokenType[] values()
com.example.everytalk.data.DataClass.Part$FileUri: int hashCode()
androidx.core.view.WindowInsetsCompat$TypeImpl34: int toPlatformType(int)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: com.example.everytalk.data.DataClass.ChatRequest deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part$InlineData$Companion: Part$InlineData$Companion()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String getData()
com.example.everytalk.util.CorrectionIntensity: com.example.everytalk.util.CorrectionIntensity valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String component3()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
com.example.everytalk.data.DataClass.Content: Content(int,java.util.List,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ContentPart: ContentPart(java.lang.String)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: WebSearchResult$$serializer()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$Text deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Message$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Message)
com.example.everytalk.data.DataClass.SafetySetting$Companion: SafetySetting$Companion()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidClipboard getClipboard()
com.example.everytalk.data.DataClass.GenerationConfig: void getTemperature$annotations()
androidx.compose.material3.tokens.TypographyKeyTokens: androidx.compose.material3.tokens.TypographyKeyTokens valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String getTagName()
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight valueOf(java.lang.String)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String toString()
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ThinkingConfig: boolean equals(java.lang.Object)
androidx.compose.material3.SnackbarResult: androidx.compose.material3.SnackbarResult[] values()
com.example.everytalk.data.DataClass.IMessage: java.lang.String getRole()
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Integer getThinkingBudget()
androidx.compose.ui.platform.AndroidComposeViewTranslationCallbackS: void setViewTranslationCallback(android.view.View)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.compose.ui.window.PopupLayout: void setContent(kotlin.jvm.functions.Function2)
com.example.everytalk.data.DataClass.MessageKt: java.lang.String toRole(com.example.everytalk.data.DataClass.Sender)
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String component1()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getContent$annotations()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.input.pointer.PointerIconService getPointerIconService()
com.example.everytalk.data.DataClass.ContentPart$Html: int hashCode()
com.example.everytalk.data.DataClass.Message: java.lang.String component2()
com.example.everytalk.data.DataClass.SafetyRating$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function1 getOnRequestDisallowInterceptTouchEvent$ui_release()
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion: kotlinx.serialization.KSerializer serializer()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
com.example.everytalk.data.DataClass.Part$InlineData: com.example.everytalk.data.DataClass.Part$InlineData copy(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.statecontroller.LRUCache: java.util.Set getEntries()
org.slf4j.helpers.Reporter$Level: org.slf4j.helpers.Reporter$Level valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Part$Text: boolean equals(java.lang.Object)
androidx.emoji2.text.ConcurrencyHelpers$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
okhttp3.internal.http2.ErrorCode: okhttp3.internal.http2.ErrorCode[] values()
com.example.everytalk.data.DataClass.PartsApiMessage: void getRole$annotations()
androidx.compose.ui.text.internal.InlineClassHelperKt: java.lang.Void throwIllegalArgumentExceptionForNullCheck(java.lang.String)
io.ktor.util.date.WeekDay: io.ktor.util.date.WeekDay[] values()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.WindowInfo getWindowInfo()
com.example.everytalk.data.DataClass.PromptFeedback: void write$Self$app_release(com.example.everytalk.data.DataClass.PromptFeedback,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ApiConfig$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.window.PopupLayout: void getParams$ui_release$annotations()
com.example.everytalk.data.DataClass.Message$$serializer: Message$$serializer()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: int hashCode()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: void getMimeType$annotations()
androidx.compose.ui.platform.AbstractComposeView: void getShowLayoutBounds$annotations()
androidx.compose.ui.platform.ComposeView: void setContent(kotlin.jvm.functions.Function2)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
androidx.compose.foundation.layout.Direction: androidx.compose.foundation.layout.Direction[] values()
com.example.everytalk.data.DataClass.ApiContentPart$Text: com.example.everytalk.data.DataClass.ApiContentPart$Text copy$default(com.example.everytalk.data.DataClass.ApiContentPart$Text,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean component6()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean getUseWebSearch()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getContent()
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
androidx.compose.foundation.text.selection.CrossStatus: androidx.compose.foundation.text.selection.CrossStatus valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.compose.ui.text.style.ResolvedTextDirection: androidx.compose.ui.text.style.ResolvedTextDirection valueOf(java.lang.String)
androidx.compose.material3.tokens.ShapeKeyTokens: androidx.compose.material3.tokens.ShapeKeyTokens valueOf(java.lang.String)
com.example.everytalk.util.IncrementalMarkdownParser$TokenType: com.example.everytalk.util.IncrementalMarkdownParser$TokenType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Part: void write$Self(com.example.everytalk.data.DataClass.Part,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ApiConfig: ApiConfig(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer getNumInferenceSteps()
com.example.everytalk.models.MoreOptionsType: com.example.everytalk.models.MoreOptionsType valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
com.example.everytalk.data.DataClass.Part$InlineData$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ChatRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20(androidx.core.view.WindowInsetsCompat)
kotlinx.serialization.json.JsonObject$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.ViewLayer: void setInvalidated(boolean)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: com.example.everytalk.data.DataClass.Part$FileUri deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.autofill.AutofillType: androidx.compose.ui.autofill.AutofillType[] values()
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
androidx.compose.animation.core.RepeatMode: androidx.compose.animation.core.RepeatMode[] values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
com.example.everytalk.data.DataClass.Sender$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
com.example.everytalk.data.DataClass.Sender: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
androidx.navigation.compose.DialogNavigator: DialogNavigator()
com.example.everytalk.data.DataClass.ChatRequest: void getTools$annotations()
com.example.everytalk.data.DataClass.ContentPart$Audio: com.example.everytalk.data.DataClass.ContentPart$Audio copy(java.lang.String,java.lang.String,java.lang.String)
androidx.compose.ui.viewinterop.AndroidViewHolder: android.view.ViewGroup$LayoutParams getLayoutParams()
androidx.compose.ui.graphics.layer.ViewLayer: void setCanUseCompositingLayer$ui_graphics_release(boolean)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: com.example.everytalk.statecontroller.AppViewModel$ExportedSettings copy(java.util.List,java.util.Set)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: PartsApiMessage$$serializer()
androidx.compose.foundation.text.TextContextMenuItems: androidx.compose.foundation.text.TextContextMenuItems[] values()
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.compose.ui.text.style.ResolvedTextDirection: androidx.compose.ui.text.style.ResolvedTextDirection[] values()
com.example.everytalk.data.DataClass.Message$Companion: Message$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component4()
com.example.everytalk.data.DataClass.PartsApiMessage: PartsApiMessage(int,java.lang.String,java.lang.String,java.util.List,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: com.example.everytalk.data.DataClass.ApiContentPart$FileUri copy(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: void write$Self$app_release(com.example.everytalk.data.DataClass.ApiContentPart$InlineData,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float component2()
androidx.compose.ui.platform.CalculateMatrixToWindowApi29: void calculateMatrixToWindow-EL8BTi8(android.view.View,float[])
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.GeminiApiResponse: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.compose.ui.viewinterop.AndroidViewHolder: android.view.View getInteropView()
com.example.everytalk.data.DataClass.AbstractApiMessage: AbstractApiMessage(int,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.navigation.NavBackStackEntry$SavedStateViewModel: NavBackStackEntry$SavedStateViewModel(androidx.lifecycle.SavedStateHandle)
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
androidx.compose.ui.unit.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorNext(long,float[],int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: com.example.everytalk.data.DataClass.SimpleTextApiMessage copy$default(com.example.everytalk.data.DataClass.SimpleTextApiMessage,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig: ApiConfig(int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.Candidate: java.lang.String component2()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.RootForTest getRootForTest()
com.example.everytalk.data.DataClass.Message: java.util.List component13()
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Boolean getIncludeThoughts()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.WebSearchResult$Companion: WebSearchResult$Companion()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part$Text$$serializer: Part$Text$$serializer()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners get_viewTreeOwners()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.graphics.Path$Direction: androidx.compose.ui.graphics.Path$Direction valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Candidate: java.util.List component4()
com.example.everytalk.data.DataClass.WebSearchResult$Companion: WebSearchResult$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Sender component3()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: Part$InlineData$$serializer()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: PromptFeedback$$serializer()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component6()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
com.example.everytalk.data.DataClass.Part$Text$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GenerationConfig component2()
androidx.compose.ui.layout.IntrinsicMinMax: androidx.compose.ui.layout.IntrinsicMinMax valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion: ApiContentPart$FileUri$Companion()
com.example.everytalk.data.DataClass.ApiConfig: float getTemperature()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String toString()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.compose.ui.unit.LayoutDirection: androidx.compose.ui.unit.LayoutDirection[] values()
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.SafetySetting: SafetySetting(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.SafetyRating)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
androidx.compose.ui.internal.InlineClassHelperKt: java.lang.Void throwIllegalStateExceptionForNullCheck(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ApiConfig copy$default(com.example.everytalk.data.DataClass.ApiConfig,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float,int,java.lang.Object)
com.example.everytalk.data.DataClass.AbstractApiMessage: kotlinx.serialization.KSerializer _init_$_anonymous_()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String component1()
androidx.compose.material3.ColorResourceHelper: long getColor-WaAFU9c(android.content.Context,int)
androidx.appcompat.resources.Compatibility$Api21Impl: int getChangingConfigurations(android.content.res.TypedArray)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String getCode()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.SafetySetting)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getRole()
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String toString()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Part$FileUri)
com.example.everytalk.data.DataClass.Part$FileUri: Part$FileUri(int,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String getContentId()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Content$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
io.ktor.client.engine.android.AndroidEngineContainer: AndroidEngineContainer()
com.example.everytalk.data.DataClass.GenerationConfig$Companion: GenerationConfig$Companion()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Integer component3()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
kotlin.DeprecationLevel: kotlin.DeprecationLevel valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function1 getOnModifierChanged$ui_release()
com.example.everytalk.data.DataClass.Part: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
com.example.everytalk.data.DataClass.SafetySetting: com.example.everytalk.data.DataClass.SafetySetting copy(java.lang.String,java.lang.String)
androidx.compose.material3.ModalBottomSheetDialogLayout$Api33Impl: void maybeUnregisterBackCallback(android.view.View,java.lang.Object)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component5()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.unit.IntSize getPopupContentSize-bOM6tXw()
okhttp3.Protocol: okhttp3.Protocol[] values()
com.example.everytalk.data.DataClass.ThinkingConfig$Companion: ThinkingConfig$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
kotlinx.coroutines.flow.SharingCommand: kotlinx.coroutines.flow.SharingCommand[] values()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.runtime.ComposerKt: void composeImmediateRuntimeError(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
com.example.everytalk.data.DataClass.ApiConfig: boolean component7()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: com.example.everytalk.data.DataClass.ApiConfig deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.ui.platform.AndroidComposeView: void setAccessibilityEventBatchIntervalMillis(long)
androidx.compose.ui.viewinterop.AndroidViewHolder: void setRelease(kotlin.jvm.functions.Function0)
com.example.everytalk.data.DataClass.ChatRequest: int hashCode()
androidx.compose.ui.window.PopupLayout: void setTestTag(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: void write$Self$app_release(com.example.everytalk.data.DataClass.ChatRequest,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.compose.ui.platform.AndroidComposeView: void setCoroutineContext(kotlin.coroutines.CoroutineContext)
com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ThinkingConfig$Companion: ThinkingConfig$Companion()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ThinkingConfig: int hashCode()
androidx.compose.animation.core.MutatePriority: androidx.compose.animation.core.MutatePriority valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidAccessibilityManager getAccessibilityManager()
androidx.core.view.WindowInsetsCompat$Impl20: boolean isVisible(int)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component2()
com.example.everytalk.data.DataClass.ModalityType: java.lang.String getDisplayName()
androidx.graphics.path.PathIteratorPreApi34Impl: long createInternalPathIterator(android.graphics.Path,int,float)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.compose.material.ripple.UnprojectedRipple$MRadiusHelper: void setRadius(android.graphics.drawable.RippleDrawable,int)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean component7()
androidx.compose.ui.graphics.Path$Direction: androidx.compose.ui.graphics.Path$Direction[] values()
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: com.example.everytalk.data.DataClass.GithubRelease deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.foundation.text.selection.Direction: androidx.compose.foundation.text.selection.Direction valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.ContentPart$Code: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Message: java.lang.String getName()
com.example.everytalk.data.DataClass.ChatRequest: java.util.List component1()
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
com.example.everytalk.data.DataClass.Candidate$$serializer: Candidate$$serializer()
com.example.everytalk.data.DataClass.GithubRelease: com.example.everytalk.data.DataClass.GithubRelease copy(java.lang.String,java.lang.String,java.lang.String)
androidx.compose.ui.platform.actionmodecallback.MenuItemOption: androidx.compose.ui.platform.actionmodecallback.MenuItemOption[] values()
com.example.everytalk.data.DataClass.Message: java.lang.String getText()
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation: LegacySavedStateHandleController$OnRecreation()
androidx.compose.runtime.PreconditionsKt: void throwIllegalArgumentException(java.lang.String)
androidx.compose.ui.state.ToggleableState: androidx.compose.ui.state.ToggleableState[] values()
androidx.compose.ui.node.LayoutNode: java.lang.String exceptionMessageForParentingOrOwnership(androidx.compose.ui.node.LayoutNode)
androidx.compose.material3.MinimumInteractiveModifier: MinimumInteractiveModifier()
androidx.compose.ui.platform.TextToolbarStatus: androidx.compose.ui.platform.TextToolbarStatus[] values()
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String getLanguage()
androidx.compose.runtime.SlotTableKt: void throwConcurrentModificationException()
androidx.compose.material3.DrawerValue: androidx.compose.material3.DrawerValue[] values()
com.example.everytalk.data.DataClass.Part$Text: void write$Self$app_release(com.example.everytalk.data.DataClass.Part$Text,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Message: boolean component9()
com.example.everytalk.data.DataClass.Part$Text$$serializer: com.example.everytalk.data.DataClass.Part$Text deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig(java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.material3.ModalBottomSheetDialogLayout$Api34Impl: android.window.OnBackAnimationCallback createBackCallback(kotlin.jvm.functions.Function0,androidx.compose.animation.core.Animatable,kotlinx.coroutines.CoroutineScope)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
com.example.everytalk.data.DataClass.Content$Companion: Content$Companion()
androidx.compose.ui.platform.OutlineVerificationHelper: void setPath(android.graphics.Outline,androidx.compose.ui.graphics.Path)
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Integer component2()
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.ChatRequest copy(java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map)
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getId$annotations()
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map component12()
androidx.compose.ui.text.AnnotationType: androidx.compose.ui.text.AnnotationType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.ChatRequest copy$default(com.example.everytalk.data.DataClass.ChatRequest,java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map,int,java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion: ApiContentPart$InlineData$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.runtime.collection.MutableVectorKt: void throwListIndexOutOfBoundsException(int,int)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidViewsHandler getAndroidViewsHandler$ui_release()
androidx.compose.ui.platform.AndroidComposeView: void setLastMatrixRecalculationAnimationTime$ui_release(long)
com.example.everytalk.data.DataClass.GenerationConfig$Companion: GenerationConfig$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String component1()
androidx.compose.ui.window.PopupLayout: void setPositionProvider(androidx.compose.ui.window.PopupPositionProvider)
androidx.compose.ui.platform.AndroidComposeView: kotlin.jvm.functions.Function1 getConfigurationChangeObserver()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.core.view.WindowInsetsCompat$Impl34: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.font.FontFamily$Resolver getFontFamilyResolver()
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: boolean equals(java.lang.Object)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners getViewTreeOwners()
com.example.everytalk.data.DataClass.ChatRequest: void getQwenEnableSearch$annotations()
androidx.compose.ui.platform.TextToolbarStatus: androidx.compose.ui.platform.TextToolbarStatus valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiConfig)
androidx.compose.ui.platform.ComposeView: boolean getShouldCreateCompositionOnAttachedToWindow()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.compose.ui.input.pointer.PointerInputEventHandler: java.lang.Object invoke(androidx.compose.ui.input.pointer.PointerInputScope,kotlin.coroutines.Continuation)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
androidx.compose.material3.ScaffoldLayoutContent: androidx.compose.material3.ScaffoldLayoutContent[] values()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String getMimeType()
com.example.everytalk.data.DataClass.Candidate: Candidate(com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.viewinterop.ViewFactoryHolder: android.view.View getViewRoot()
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Sender getSender()
androidx.compose.ui.platform.ViewLayer: long getLayerId()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.window.PopupLayout: void setParentLayoutCoordinates(androidx.compose.ui.layout.LayoutCoordinates)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getRole$annotations()
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AutofillManager getAutofillManager()
coil3.decode.DataSource: coil3.decode.DataSource[] values()
com.example.everytalk.data.DataClass.GithubRelease: GithubRelease(java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.ui.platform.AndroidComposeView: boolean getShowLayoutBounds()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: ApiConfig$$serializer()
androidx.compose.ui.node.LayoutNode$UsageByParent: androidx.compose.ui.node.LayoutNode$UsageByParent[] values()
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.GenerationConfig copy(java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig)
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GeminiApiRequest copy(java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ChatRequest)
androidx.compose.material3.tokens.ColorSchemeKeyTokens: androidx.compose.material3.tokens.ColorSchemeKeyTokens[] values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion: kotlinx.serialization.KSerializer serializer()
coil3.size.Scale: coil3.size.Scale valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String component1()
com.example.everytalk.ui.screens.MainScreen.AiMessageOption: com.example.everytalk.ui.screens.MainScreen.AiMessageOption[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
androidx.compose.ui.viewinterop.ViewFactoryHolder: void setResetBlock(kotlin.jvm.functions.Function1)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.List getApiConfigs()
kotlin.text.RegexOption: kotlin.text.RegexOption valueOf(java.lang.String)
com.example.everytalk.data.DataClass.AbstractApiMessage: java.lang.String getRole()
androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus: androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus[] values()
androidx.compose.ui.viewinterop.ViewFactoryHolder: androidx.compose.ui.input.nestedscroll.NestedScrollDispatcher getDispatcher()
org.slf4j.helpers.Reporter$TargetChoice: org.slf4j.helpers.Reporter$TargetChoice valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Object getToolChoice()
com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: com.example.everytalk.data.DataClass.ApiContentPart$InlineData copy$default(com.example.everytalk.data.DataClass.ApiContentPart$InlineData,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.Content: com.example.everytalk.data.DataClass.Content copy$default(com.example.everytalk.data.DataClass.Content,java.util.List,java.lang.String,int,java.lang.Object)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
kotlinx.serialization.json.ClassDiscriminatorMode: kotlinx.serialization.json.ClassDiscriminatorMode valueOf(java.lang.String)
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.core.view.WindowInsetsCompat$Impl: boolean isVisible(int)
com.example.everytalk.data.DataClass.ApiContentPart: ApiContentPart()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AccessibilityManager getAccessibilityManager()
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: AbstractApiMessage$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getId()
com.example.everytalk.data.DataClass.ChatRequest: void getApiAddress$annotations()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: SimpleTextApiMessage(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.WebSearchResult: boolean equals(java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String toString()
com.example.everytalk.models.ImageSourceOption: com.example.everytalk.models.ImageSourceOption[] values()
androidx.compose.foundation.layout.IntrinsicSize: androidx.compose.foundation.layout.IntrinsicSize valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: java.util.List getMessages()
kotlin.time.DurationUnit: kotlin.time.DurationUnit valueOf(java.lang.String)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: com.example.everytalk.data.DataClass.ThinkingConfig deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.foundation.text.selection.SelectionHandleAnchor: androidx.compose.foundation.text.selection.SelectionHandleAnchor[] values()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.String toString()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
com.example.everytalk.data.DataClass.Message: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.compose.ui.text.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.compose.ui.platform.ViewLayer: float getCameraDistancePx()
kotlinx.serialization.json.JsonArray$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String getName()
androidx.compose.ui.platform.AbstractComposeView: void setViewCompositionStrategy(androidx.compose.ui.platform.ViewCompositionStrategy)
androidx.compose.foundation.gestures.Orientation: androidx.compose.foundation.gestures.Orientation valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: void set_viewTreeOwners(androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners)
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.ThinkingConfig component4()
com.example.everytalk.data.DataClass.ChatRequest: java.util.List getTools()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
com.example.everytalk.data.DataClass.ChatRequest: void getToolChoice$annotations()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: com.example.everytalk.data.DataClass.GeminiApiRequest deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.AbstractApiMessage: java.lang.String getName()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setSavedStateRegistryOwner(androidx.savedstate.SavedStateRegistryOwner)
com.example.everytalk.data.DataClass.Part$Text: java.lang.String getText()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AutofillTree getAutofillTree()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer getMaxTokens()
androidx.compose.ui.platform.AndroidComposeView: void setContentCaptureManager$ui_release(androidx.compose.ui.contentcapture.AndroidContentCaptureManager)
androidx.compose.ui.focus.CustomDestinationResult: androidx.compose.ui.focus.CustomDestinationResult valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String toString()
androidx.emoji2.text.EmojiExclusions$EmojiExclusions_Api34: java.util.Set getExclusions()
com.example.everytalk.data.DataClass.PromptFeedback: PromptFeedback(java.util.List)
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.GenerationConfig: void getThinkingConfig$annotations()
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String toString()
com.example.everytalk.data.DataClass.Message: Message(int,java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: Part$FileUri$$serializer()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: com.example.everytalk.data.DataClass.Part$InlineData deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.GeminiApiRequest: GeminiApiRequest(int,java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Boolean component12()
com.example.everytalk.data.DataClass.ChatRequest$Companion: ChatRequest$Companion()
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
com.example.everytalk.data.network.AppStreamEvent$Content$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.hapticfeedback.HapticFeedback getHapticFeedBack()
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.GeminiApiResponse copy$default(com.example.everytalk.data.DataClass.GeminiApiResponse,java.util.List,com.example.everytalk.data.DataClass.PromptFeedback,int,java.lang.Object)
androidx.compose.ui.platform.AndroidComposeView: android.view.View getView()
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse(java.util.List,com.example.everytalk.data.DataClass.PromptFeedback)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.compose.ui.platform.AbstractComposeView: void setParentCompositionContext(androidx.compose.runtime.CompositionContext)
coil3.util.Logger$Level: coil3.util.Logger$Level valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
kotlin.DeprecationLevel: kotlin.DeprecationLevel[] values()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
com.example.everytalk.data.DataClass.Message$Companion: Message$Companion()
androidx.compose.animation.EnterExitState: androidx.compose.animation.EnterExitState valueOf(java.lang.String)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.compose.ui.contentcapture.ContentCaptureEventType: androidx.compose.ui.contentcapture.ContentCaptureEventType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Content: java.lang.String component2()
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
com.example.everytalk.data.DataClass.Message$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Part$FileUri: com.example.everytalk.data.DataClass.Part$FileUri copy$default(com.example.everytalk.data.DataClass.Part$FileUri,java.lang.String,int,java.lang.Object)
coil3.util.Logger$Level: coil3.util.Logger$Level[] values()
kotlin.io.encoding.Base64$PaddingOption: kotlin.io.encoding.Base64$PaddingOption valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse(java.util.List,com.example.everytalk.data.DataClass.PromptFeedback,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.SafetyRating: com.example.everytalk.data.DataClass.SafetyRating copy(java.lang.String,java.lang.String)
okhttp3.TlsVersion: okhttp3.TlsVersion valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String component2()
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.data.DataClass.ApiContentPart$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setOnDensityChanged$ui_release(kotlin.jvm.functions.Function1)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.PartsApiMessage: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String getBase64Data()
androidx.compose.material.ripple.RippleHostView: void setRippleState(boolean)
com.example.everytalk.data.DataClass.Message: Message(java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getModel()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String getHtmlUrl()
com.example.everytalk.data.DataClass.ChatRequest: ChatRequest(int,java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.semantics.SemanticsOwner getSemanticsOwner()
androidx.compose.ui.platform.ViewLayerContainer: void dispatchGetDisplayList()
kotlin.text.RegexOption: kotlin.text.RegexOption[] values()
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion: GeminiApiResponse$Companion()
androidx.compose.ui.graphics.layer.ViewLayer: android.view.View getOwnerView()
androidx.compose.ui.platform.AndroidComposeView: void setOnViewTreeOwnersAvailable(kotlin.jvm.functions.Function1)
com.example.everytalk.data.DataClass.GeminiApiResponse: java.util.List component1()
com.example.everytalk.data.DataClass.ApiContentPart: ApiContentPart(int,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.platform.AndroidComposeViewForceDarkModeQ: void disallowForceDark(android.view.View)
com.example.everytalk.data.DataClass.PromptFeedback$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
androidx.compose.ui.viewinterop.ViewFactoryHolder: void setReleaseBlock(kotlin.jvm.functions.Function1)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float component10()
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String toString()
coil3.network.okhttp.internal.OkHttpNetworkFetcherServiceLoaderTarget: OkHttpNetworkFetcherServiceLoaderTarget()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.GeminiApiResponse: java.lang.String toString()
com.example.everytalk.data.DataClass.Message: boolean isPlaceholderName()
androidx.compose.ui.platform.ComposeView: java.lang.CharSequence getAccessibilityClassName()
com.example.everytalk.data.DataClass.Part: Part(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GenerationConfig)
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function0 getRelease()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
com.example.everytalk.data.DataClass.ApiConfig: int hashCode()
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.compose.ui.node.LayoutNode getLayoutNode()
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String getFileUri()
kotlin.LazyThreadSafetyMode: kotlin.LazyThreadSafetyMode valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: ChatRequest(java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String toString()
com.example.everytalk.data.DataClass.Content: int hashCode()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.compose.ui.viewinterop.ViewFactoryHolder: kotlin.jvm.functions.Function1 getUpdateBlock()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.WebSearchResult)
androidx.compose.ui.graphics.layer.ViewLayer: boolean getCanUseCompositingLayer$ui_graphics_release()
com.example.everytalk.data.DataClass.Sender: Sender(java.lang.String,int)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: ApiContentPart$FileUri$$serializer()
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.compose.foundation.text.KeyCommand: androidx.compose.foundation.text.KeyCommand valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: SimpleTextApiMessage$$serializer()
com.example.everytalk.data.DataClass.PartsApiMessage: void getParts$annotations()
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType[] values()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.TextToolbar getTextToolbar()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: ApiContentPart$InlineData$$serializer()
com.example.everytalk.data.DataClass.ContentPart$Html: ContentPart$Html(java.lang.String,java.lang.String)
androidx.navigation.compose.ComposeNavigator: ComposeNavigator()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.window.PopupPositionProvider getPositionProvider()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
com.example.everytalk.data.DataClass.PartsApiMessage: int hashCode()
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map getCustomModelParameters()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String getUri()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.compose.ui.platform.AbstractComposeView: boolean getShouldCreateCompositionOnAttachedToWindow()
androidx.compose.material3.internal.TextFieldType: androidx.compose.material3.internal.TextFieldType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
com.example.everytalk.data.DataClass.Candidate: java.lang.String getFinishReason()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
com.example.everytalk.data.DataClass.ApiContentPart$Text: int hashCode()
com.example.everytalk.data.DataClass.Candidate: int component3()
androidx.compose.ui.platform.AbstractComposeView: boolean getHasComposition()
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String getMarkdownWithKatex()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.OwnerSnapshotObserver getSnapshotObserver()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getApiAddress()
androidx.compose.ui.platform.AndroidComposeViewStartDragAndDropN: boolean startDragAndDrop(android.view.View,androidx.compose.ui.draganddrop.DragAndDropTransferData,androidx.compose.ui.draganddrop.ComposeDragShadowBuilder)
com.example.everytalk.data.DataClass.ApiConfig$Companion: ApiConfig$Companion()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component5()
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIndexOutOfBoundsException(java.lang.String)
androidx.compose.foundation.text.KeyCommand: androidx.compose.foundation.text.KeyCommand[] values()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
androidx.compose.material3.ModalBottomSheetDialogLayout$Api33Impl: void maybeRegisterBackCallback(android.view.View,java.lang.Object)
com.example.everytalk.data.DataClass.Message: boolean equals(java.lang.Object)
androidx.compose.runtime.Recomposer$State: androidx.compose.runtime.Recomposer$State valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.emoji2.text.EmojiCompatInitializer: EmojiCompatInitializer()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion: SimpleTextApiMessage$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.GeminiApiRequest: GeminiApiRequest(java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setInsets(int,androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.Part$Text: com.example.everytalk.data.DataClass.Part$Text copy$default(com.example.everytalk.data.DataClass.Part$Text,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ModalityType component8()
com.example.everytalk.data.DataClass.ThinkingConfig: com.example.everytalk.data.DataClass.ThinkingConfig copy(java.lang.Boolean,java.lang.Integer)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component3()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String toString()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.compose.ui.layout.IntrinsicWidthHeight: androidx.compose.ui.layout.IntrinsicWidthHeight valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.PartsApiMessage)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.activity.EdgeToEdgeApi28: void adjustLayoutInDisplayCutoutMode(android.view.Window)
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Message copy(java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiContentPart$Text)
com.example.everytalk.data.DataClass.AbstractApiMessage: AbstractApiMessage(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart: ApiContentPart(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.platform.DrawChildContainer getContainer()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.modifier.ModifierLocalManager getModifierLocalManager()
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.Content: com.example.everytalk.data.DataClass.Content copy(java.util.List,java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri$Companion: Part$FileUri$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$BuilderImpl34: void setInsets(int,androidx.core.graphics.Insets)
kotlin.LazyThreadSafetyMode: kotlin.LazyThreadSafetyMode[] values()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SafetyRating$Companion: SafetyRating$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.network.AppStreamEvent$Error$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiContentPart: kotlinx.serialization.KSerializer _init_$_anonymous_()
com.example.everytalk.data.DataClass.Message: void write$Self$app_release(com.example.everytalk.data.DataClass.Message,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
com.example.everytalk.data.DataClass.Part$InlineData: com.example.everytalk.data.DataClass.Part$InlineData copy$default(com.example.everytalk.data.DataClass.Part$InlineData,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: SimpleTextApiMessage(int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.GithubRelease: void write$Self$app_release(com.example.everytalk.data.DataClass.GithubRelease,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.foundation.text.TextContextMenuItems: androidx.compose.foundation.text.TextContextMenuItems valueOf(java.lang.String)
androidx.compose.ui.viewinterop.ViewFactoryHolder: androidx.compose.ui.platform.AbstractComposeView getSubCompositionView()
com.example.everytalk.data.DataClass.PartsApiMessage: java.util.List getParts()
androidx.tracing.TraceApi29Impl: boolean isEnabled()
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String component2()
coil3.size.Precision: coil3.size.Precision[] values()
androidx.appcompat.resources.Compatibility$Api21Impl: android.graphics.drawable.Drawable createFromXmlInner(android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.platform.AndroidComposeViewAssistHelperMethodsO: void setClassName(android.view.ViewStructure,android.view.View)
com.example.everytalk.data.DataClass.Message: boolean isError()
androidx.compose.ui.node.LayoutNode$UsageByParent: androidx.compose.ui.node.LayoutNode$UsageByParent valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
io.ktor.util.date.WeekDay: io.ktor.util.date.WeekDay valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ContentPart$Html: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.SafetyRating: SafetyRating(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
org.slf4j.nop.NOPServiceProvider: NOPServiceProvider()
androidx.startup.InitializationProvider: InitializationProvider()
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String component2()
com.example.everytalk.data.DataClass.PromptFeedback: java.lang.String toString()
com.example.everytalk.data.DataClass.Message: java.lang.String component11()
androidx.compose.foundation.gestures.Orientation: androidx.compose.foundation.gestures.Orientation[] values()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component4()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component1()
com.example.everytalk.data.DataClass.GenerationConfig$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Part$InlineData)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
com.example.everytalk.data.DataClass.IMessage: java.lang.String getName()
androidx.compose.material3.SnackbarDuration: androidx.compose.material3.SnackbarDuration[] values()
com.example.everytalk.models.MoreOptionsType: com.example.everytalk.models.MoreOptionsType[] values()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.unit.Density getDensity()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GithubRelease)
com.example.everytalk.data.DataClass.GeminiApiResponse: java.util.List getCandidates()
com.example.everytalk.data.DataClass.PartsApiMessage: com.example.everytalk.data.DataClass.PartsApiMessage copy$default(com.example.everytalk.data.DataClass.PartsApiMessage,java.lang.String,java.lang.String,java.util.List,java.lang.String,int,java.lang.Object)
androidx.compose.runtime.InvalidationResult: androidx.compose.runtime.InvalidationResult valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getApiKey()
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String component1()
com.example.everytalk.data.DataClass.Candidate: Candidate(com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List)
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
com.example.everytalk.data.DataClass.Part$Text: com.example.everytalk.data.DataClass.Part$Text copy(java.lang.String)
androidx.compose.foundation.MutatePriority: androidx.compose.foundation.MutatePriority valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax valueOf(java.lang.String)
com.example.everytalk.data.network.ApiClient$FileMetadata$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.animation.core.PreconditionsKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ModalityType$Companion: ModalityType$Companion()
com.example.everytalk.data.DataClass.PromptFeedback: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.foundation.text.Handle: androidx.compose.foundation.text.Handle valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.WebSearchResult: WebSearchResult(int,int,java.lang.String,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.google.gson.reflect.TypeToken: TypeToken()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String getCategory()
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.String toString()
com.example.everytalk.data.DataClass.ChatRequest: void getModel$annotations()
com.example.everytalk.data.DataClass.ChatRequest: java.util.List component9()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: com.example.everytalk.data.DataClass.PartsApiMessage deserialize(kotlinx.serialization.encoding.Decoder)
androidx.lifecycle.SavedStateHandlesVM: SavedStateHandlesVM()
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.GeminiApiRequest: void write$Self$app_release(com.example.everytalk.data.DataClass.GeminiApiRequest,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ModalityType$Companion: ModalityType$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.PartsApiMessage$Companion: PartsApiMessage$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.SafetySetting: boolean equals(java.lang.Object)
com.example.everytalk.models.SelectedMediaItem$Audio$Companion: kotlinx.serialization.KSerializer serializer()
