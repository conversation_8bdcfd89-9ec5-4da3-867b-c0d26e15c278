package com.example.everytalk.util

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp

/**
 * 增强的Markdown渲染器 - 优化AI输出格式转换
 */
class SimpleMarkdownRenderer {
    
    private val codeStyle = SpanStyle(
        fontFamily = FontFamily.Monospace,
        background = Color(0xFFF8F8F8),
        fontSize = 13.sp,
        color = Color(0xFF2D3748)
    )
    
    private val mathStyle = SpanStyle(
        fontFamily = FontFamily.Default,
        color = Color(0xFF1A365D),
        fontSize = 15.sp,
        fontWeight = FontWeight.Medium
    )
    
    private val boldStyle = SpanStyle(fontWeight = FontWeight.Bold)
    private val italicStyle = SpanStyle(fontStyle = FontStyle.Italic)
    private val linkStyle = SpanStyle(color = Color(0xFF3182CE))
    private val headerStyle = SpanStyle(fontWeight = FontWeight.Bold, fontSize = 16.sp)
    private val listItemStyle = SpanStyle(color = Color(0xFF4A5568))
    
    fun renderText(
        text: String,
        isStreaming: Boolean = false
    ): AnnotatedString {
        return try {
            if (text.isBlank()) {
                AnnotatedString("")
            } else {
                val cleanedText = deepCleanText(text)
                renderCleanMarkdown(cleanedText)
            }
        } catch (e: Exception) {
            // 出错时返回清理后的纯文本
            AnnotatedString(deepCleanText(text))
        }
    }
    
    fun renderStreamingText(
        text: String,
        messageId: String
    ): AnnotatedString {
        return renderText(text, isStreaming = true)
    }
    
    private fun deepCleanText(text: String): String {
        var cleaned = text
        
        // 1. 预处理 - 标准化换行符和空白字符
        cleaned = cleaned.replace(Regex("\\r\\n?"), "\n")
        cleaned = cleaned.replace(Regex("[ \\t]+"), " ")
        
        // 2. 移除表格相关的符号和格式
        cleaned = cleaned.replace(Regex("\\|\\s*:?\\s*-+\\s*:?\\s*\\|"), "") // 表格分隔行
        cleaned = cleaned.replace(Regex("^\\s*\\|.*\\|\\s*$", RegexOption.MULTILINE), "") // 整行表格
        cleaned = cleaned.replace(Regex("\\|\\s*:\\s*\\|"), "") // 表格对齐符号
        cleaned = cleaned.replace(Regex("\\s*\\|\\s*"), " ") // 剩余的竖线
        
        // 3. 移除水平分割线和装饰性符号
        cleaned = cleaned.replace(Regex("^\\s*[-=_*]{3,}\\s*$", RegexOption.MULTILINE), "")
        cleaned = cleaned.replace(Regex("[-=_*|]{3,}"), "")
        
        // 4. 清理重复的符号和标点
        cleaned = cleaned.replace(Regex("[:]{2,}"), ":")
        cleaned = cleaned.replace(Regex("[|]{2,}"), "")
        cleaned = cleaned.replace(Regex("\\s*[|:]\\s*[|:]\\s*[|:]*"), " ")
        
        // 5. 移除Markdown表格语法残留
        cleaned = cleaned.replace(Regex("\\s*\\|\\s*$", RegexOption.MULTILINE), "")
        cleaned = cleaned.replace(Regex("^\\s*\\|\\s*", RegexOption.MULTILINE), "")
        
        // 6. 优化数学公式格式
        cleaned = cleaned.replace(Regex("\\$\\s+([^$]*?)\\s+\\$"), "$$$1$$")
        cleaned = cleaned.replace(Regex("\\$\\$\\s+([^$]*?)\\s+\\$\\$"), "$$$$1$$$$")
        
        // 7. 优化代码块格式
        cleaned = cleaned.replace(Regex("```\\s*\\n\\s*```"), "") // 移除空代码块
        cleaned = cleaned.replace(Regex("```([a-zA-Z]+)\\s*\\n"), "```$1\n") // 标准化代码块开始
        
        // 8. 保留自然的段落结构 - 只清理过度的空行
        cleaned = cleaned.replace(Regex("\\n{5,}"), "\n\n\n")
        cleaned = cleaned.split('\n')
            .map { it.trim() }
            .filter { line ->
                val trimmed = line.trim()
                // 过滤只包含符号的行和空行，但保留有意义的内容
                !(trimmed.matches(Regex("^[|:_\\-=*\\s]+$")) && trimmed.length < 50) && 
                (trimmed.isNotEmpty() || line.isBlank())
            }
            .joinToString("\n")
        
        // 9. 最终清理
        cleaned = cleaned.trim()
        
        return cleaned
    }
    
    private fun renderCleanMarkdown(text: String): AnnotatedString {
        // 首先检查并处理表格
        if (EnhancedTableRenderer.isTableContent(text)) {
            return EnhancedTableRenderer.renderTable(text)
        }
        
        return buildAnnotatedString {
            var currentIndex = 0
            
            // 增强的模式匹配，支持更多Markdown元素
            val patterns = listOf(
                // 标题
                "HEADER" to Regex("^#{1,6}\\s+(.+)$", RegexOption.MULTILINE),
                // 数学公式
                "MATH_BLOCK" to Regex("\\$\\$([\\s\\S]*?)\\$\\$"),
                "MATH_INLINE" to Regex("\\$([^$\\n]*?)\\$"),
                // 代码
                "CODE_BLOCK" to Regex("```[\\w]*\\n?([\\s\\S]*?)```"),
                "CODE_INLINE" to Regex("`([^`]*?)`"),
                // 文本格式
                "BOLD_ITALIC" to Regex("\\*\\*\\*([^*]*?)\\*\\*\\*"),
                "BOLD" to Regex("\\*\\*([^*]*?)\\*\\*"),
                "ITALIC" to Regex("\\*([^*]*?)\\*"),
                // 列表
                "UNORDERED_LIST" to Regex("^[\\s]*[-*+]\\s+(.+)$", RegexOption.MULTILINE),
                "ORDERED_LIST" to Regex("^[\\s]*\\d+\\.\\s+(.+)$", RegexOption.MULTILINE),
                // 链接
                "LINK" to Regex("\\[([^\\]]*?)\\]\\(([^)]*?)\\)"),
                "AUTO_LINK" to Regex("https?://[^\\s]+"),
                // 引用
                "BLOCKQUOTE" to Regex("^>\\s+(.+)$", RegexOption.MULTILINE)
            )
            
            while (currentIndex < text.length) {
                var foundMatch = false
                var earliestMatch: Triple<String, MatchResult, Int>? = null
                
                // 找到最早的匹配
                for ((type, regex) in patterns) {
                    val match = regex.find(text, currentIndex)
                    if (match != null) {
                        if (earliestMatch == null || match.range.first < earliestMatch.third) {
                            earliestMatch = Triple(type, match, match.range.first)
                        }
                    }
                }
                
                if (earliestMatch != null) {
                    val (type, match, startIndex) = earliestMatch
                    
                    // 添加匹配前的普通文本
                    if (startIndex > currentIndex) {
                        val beforeText = text.substring(currentIndex, startIndex)
                        append(beforeText)
                    }
                    
                    // 处理匹配的内容
                    when (type) {
                        "HEADER" -> {
                            val headerText = match.groupValues[1].trim()
                            withStyle(headerStyle) {
                                append(headerText)
                            }
                        }
                        "MATH_BLOCK", "MATH_INLINE" -> {
                            val mathContent = match.groupValues[1].trim()
                            if (mathContent.isNotEmpty()) {
                                withStyle(mathStyle) {
                                    append(EnhancedLatexToUnicode.convert(mathContent))
                                }
                            }
                        }
                        "CODE_BLOCK" -> {
                            val codeContent = match.groupValues[1].trim()
                            if (codeContent.isNotEmpty()) {
                                val language = EnhancedCodeRenderer.detectLanguage(codeContent)
                                append(EnhancedCodeRenderer.renderCodeBlock(codeContent, language))
                            }
                        }
                        "CODE_INLINE" -> {
                            val codeContent = match.groupValues[1].trim()
                            if (codeContent.isNotEmpty()) {
                                append(EnhancedCodeRenderer.renderInlineCode(codeContent))
                            }
                        }
                        "BOLD_ITALIC" -> {
                            withStyle(boldStyle + italicStyle) {
                                append(match.groupValues[1])
                            }
                        }
                        "BOLD" -> {
                            withStyle(boldStyle) {
                                append(match.groupValues[1])
                            }
                        }
                        "ITALIC" -> {
                            withStyle(italicStyle) {
                                append(match.groupValues[1])
                            }
                        }
                        "UNORDERED_LIST", "ORDERED_LIST" -> {
                            val listContent = match.groupValues[1].trim()
                            withStyle(listItemStyle) {
                                append("• $listContent")
                            }
                        }
                        "LINK" -> {
                            val linkText = match.groupValues[1]
                            val linkUrl = match.groupValues[2]
                            pushStringAnnotation("URL", linkUrl)
                            withStyle(linkStyle) {
                                append(linkText)
                            }
                            pop()
                        }
                        "AUTO_LINK" -> {
                            val url = match.value
                            pushStringAnnotation("URL", url)
                            withStyle(linkStyle) {
                                append(url)
                            }
                            pop()
                        }
                        "BLOCKQUOTE" -> {
                            val quoteContent = match.groupValues[1].trim()
                            withStyle(SpanStyle(fontStyle = FontStyle.Italic, color = Color(0xFF718096))) {
                                append("❝ $quoteContent")
                            }
                        }
                    }
                    
                    currentIndex = match.range.last + 1
                    foundMatch = true
                } else {
                    // 没有找到匹配，添加剩余文本
                    append(text.substring(currentIndex))
                    break
                }
            }
        }
    }
}