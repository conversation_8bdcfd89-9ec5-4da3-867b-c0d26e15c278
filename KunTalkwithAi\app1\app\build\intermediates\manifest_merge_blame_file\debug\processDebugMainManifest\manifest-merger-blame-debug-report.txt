1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.everytalk"
4    android:versionCode="5947"
5    android:versionName="1.3.4" >
6
7    <uses-sdk
8        android:minSdkVersion="27"
9        android:targetSdkVersion="35" /> <!-- 这个 tools:ignore="ExtraText" 通常与IDE lint关于额外文本的警告有关 -->
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:6:5-67
11-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:6:22-64
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:7:5-71
12-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:7:22-68
13    <uses-permission android:name="android.permission.CAMERA" />
13-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:8:5-65
13-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:8:22-62
14
15    <uses-feature
15-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:9:5-85
16        android:name="android.hardware.camera"
16-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:9:19-57
17        android:required="false" />
17-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:9:58-82
18    <!-- 对于 Android 13 (API 33) 及更高版本 -->
19    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
19-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:11:5-76
19-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:11:22-73
20
21    <!-- 对于 Android 14 (API 34) 及更高版本, 声明支持用户选择的媒体访问 -->
22    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
22-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:14:5-90
22-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:14:22-87
23
24    <!-- 对于 Android 12 (API 32) 及更低版本 (如果你的 minSdkVersion 低于 33) -->
25    <uses-permission
25-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:17:5-107
26        android:name="android.permission.READ_EXTERNAL_STORAGE"
26-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:17:22-77
27        android:maxSdkVersion="32" />
27-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:17:78-104
28
29    <permission
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
30        android:name="com.example.everytalk.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.example.everytalk.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
34
35    <application
35-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:19:5-56:19
36        android:allowBackup="true"
36-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:20:9-35
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
38        android:dataExtractionRules="@xml/data_extraction_rules"
38-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:21:9-65
39        android:debuggable="true"
40        android:enableOnBackInvokedCallback="true"
40-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:25:9-51
41        android:extractNativeLibs="false"
42        android:fullBackupContent="@xml/backup_rules"
42-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:22:9-54
43        android:icon="@mipmap/ic_launcher"
43-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:23:9-43
44        android:label="@string/app_name"
44-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:24:9-41
45        android:networkSecurityConfig="@xml/network_security_config"
45-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:27:9-69
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:28:9-54
47        android:supportsRtl="true"
47-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:29:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.App1"
49-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:30:9-42
50        android:usesCleartextTraffic="true" >
50-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:26:9-44
51        <activity
51-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:33:9-44:20
52            android:name="com.example.everytalk.statecontroller.MainActivity"
52-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:34:13-78
53            android:exported="true"
53-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:35:13-36
54            android:theme="@style/Theme.App1"
54-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:36:13-46
55            android:windowSoftInputMode="adjustResize" >
55-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:37:13-55
56            <intent-filter>
56-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:39:13-43:29
57                <action android:name="android.intent.action.MAIN" />
57-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:40:17-69
57-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:40:25-66
58
59                <category android:name="android.intent.category.LAUNCHER" />
59-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:42:17-77
59-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:42:27-74
60            </intent-filter>
61        </activity>
62
63        <provider
64            android:name="androidx.core.content.FileProvider"
64-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:47:13-62
65            android:authorities="com.example.everytalk.provider"
65-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:48:13-60
66            android:exported="false"
66-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:49:13-37
67            android:grantUriPermissions="true" >
67-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:50:13-47
68            <meta-data
68-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:51:13-53:54
69                android:name="android.support.FILE_PROVIDER_PATHS"
69-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:52:17-67
70                android:resource="@xml/file_paths" />
70-->C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\src\main\AndroidManifest.xml:53:17-51
71        </provider>
72
73        <activity
73-->[androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
74            android:name="androidx.compose.ui.tooling.PreviewActivity"
74-->[androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
75            android:exported="true" />
75-->[androidx.compose.ui:ui-tooling-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
76
77        <uses-library
77-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
78            android:name="androidx.window.extensions"
78-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
79            android:required="false" />
79-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
80        <uses-library
80-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
81            android:name="androidx.window.sidecar"
81-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
82            android:required="false" />
82-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
83
84        <provider
84-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
85            android:name="androidx.startup.InitializationProvider"
85-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
86            android:authorities="com.example.everytalk.androidx-startup"
86-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
87            android:exported="false" >
87-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
88            <meta-data
88-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.emoji2.text.EmojiCompatInitializer"
89-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
90                android:value="androidx.startup" />
90-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
91            <meta-data
91-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
92                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
92-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
93                android:value="androidx.startup" />
93-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
94            <meta-data
94-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
95-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
96                android:value="androidx.startup" />
96-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
97        </provider>
98
99        <activity
99-->[androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:23:9-26:79
100            android:name="androidx.activity.ComponentActivity"
100-->[androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:24:13-63
101            android:exported="true"
101-->[androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:25:13-36
102            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
102-->[androidx.compose.ui:ui-test-manifest:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\AndroidManifest.xml:26:13-76
103
104        <receiver
104-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
105            android:name="androidx.profileinstaller.ProfileInstallReceiver"
105-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
106            android:directBootAware="false"
106-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
107            android:enabled="true"
107-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
108            android:exported="true"
108-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
109            android:permission="android.permission.DUMP" >
109-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
111                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
111-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
111-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
114                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
114-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
114-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
115            </intent-filter>
116            <intent-filter>
116-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
117                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
117-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
117-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
118            </intent-filter>
119            <intent-filter>
119-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
120                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
120-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
120-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
121            </intent-filter>
122        </receiver>
123    </application>
124
125</manifest>
