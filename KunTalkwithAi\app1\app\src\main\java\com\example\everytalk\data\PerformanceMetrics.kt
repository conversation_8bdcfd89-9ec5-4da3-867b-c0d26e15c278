package com.example.everytalk.data

/**
 * 性能指标数据类
 * 用于跟踪消息处理的性能统计
 */
data class PerformanceMetrics(
    var totalProcessingTime: Long = 0L,
    var processedMessages: Int = 0,
    var skippedProcessing: Int = 0,
    var cacheHits: Int = 0,
    var cacheMisses: Int = 0,
    var errorCount: Int = 0
) {
    /**
     * 获取跳过处理的比率
     */
    fun getSkipRate(): Double {
        val total = processedMessages + skippedProcessing
        return if (total > 0) skippedProcessing.toDouble() / total else 0.0
    }
    
    /**
     * 获取缓存命中率
     */
    fun getCacheHitRate(): Double {
        val total = cacheHits + cacheMisses
        return if (total > 0) cacheHits.toDouble() / total else 0.0
    }
    
    /**
     * 获取平均处理时间
     */
    fun getAverageProcessingTime(): Double {
        return if (processedMessages > 0) totalProcessingTime.toDouble() / processedMessages else 0.0
    }
    
    /**
     * 重置所有指标
     */
    fun reset() {
        totalProcessingTime = 0L
        processedMessages = 0
        skippedProcessing = 0
        cacheHits = 0
        cacheMisses = 0
        errorCount = 0
    }
    
    /**
     * 记录处理时间
     */
    fun recordProcessingTime(timeMs: Long) {
        totalProcessingTime += timeMs
        processedMessages++
    }
    
    /**
     * 记录缓存命中
     */
    fun recordCacheHit() {
        cacheHits++
    }
    
    /**
     * 记录缓存未命中
     */
    fun recordCacheMiss() {
        cacheMisses++
    }
    
    /**
     * 记录错误
     */
    fun recordError() {
        errorCount++
    }
}