# The proguard configuration file for the following section is C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\build\intermediates\aapt_proguard_file\benchmark\processBenchmarkResources\aapt_rules.txt
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.core.content.FileProvider { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class com.example.everytalk.statecontroller.MainActivity { <init>(); }

# End of content from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\build\intermediates\aapt_proguard_file\benchmark\processBenchmarkResources\aapt_rules.txt
# The proguard configuration file for the following section is C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.11.1
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.11.1
# The proguard configuration file for the following section is C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\proguard-rules.pro
-keep class com.google.gson.reflect.TypeToken
-keep class * extends com.google.gson.reflect.TypeToken
-keep class com.example.everytalk.data.DataClass.** { *; }
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
# End of content from C:\Users\<USER>\Desktop\anyaitotalked\KunTalkwithAi\app1\app\proguard-rules.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\ebf5f12d179771782bf41f123e88642d\transformed\navigation-common-2.7.7\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# NavArgsLazy creates NavArgs instances using reflection
-if public class ** implements androidx.navigation.NavArgs
-keepclassmembers public class <1> {
    ** fromBundle(android.os.Bundle);
}

# Retain the @Navigator.Name annotation on each subclass of Navigator.
# R8 full mode only retains annotations on items matched by a -keep rule,
# hence the extra -keep rule for the subclasses of Navigator.
#
# A -keep rule for the Navigator.Name annotation class is not required
# since the annotation is referenced from the code.
-keepattributes RuntimeVisibleAnnotations
-keep,allowobfuscation,allowshrinking class * extends androidx.navigation.Navigator

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\ebf5f12d179771782bf41f123e88642d\transformed\navigation-common-2.7.7\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\178ff5c54d6bc8a1dbeb66f819bb9e8e\transformed\lifecycle-runtime-compose-release\proguard.txt
# Workaround for https://issuetracker.google.com/issues/346808608
#
# `androidx.lifecycle.compose.LocalLifecycleOwner` will reflectively lookup for
# `androidx.compose.ui.platform.LocalLifecycleOwner` to ensure backward compatibility
# when using Lifecycle 2.8+ with Compose 1.6.
#
# We need to keep the getter if the code using this is included.
#
# We need to suppress `ShrinkerUnresolvedReference` because the `LocalComposition` is in a
# different module.
#
#noinspection ShrinkerUnresolvedReference
-if public class androidx.compose.ui.platform.AndroidCompositionLocals_androidKt {
    public static *** getLocalLifecycleOwner();
}
-keep public class androidx.compose.ui.platform.AndroidCompositionLocals_androidKt {
    public static *** getLocalLifecycleOwner();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\178ff5c54d6bc8a1dbeb66f819bb9e8e\transformed\lifecycle-runtime-compose-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\56673a8b4479bfbc56575321e60b5dc5\transformed\lifecycle-livedata-core-2.9.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\56673a8b4479bfbc56575321e60b5dc5\transformed\lifecycle-livedata-core-2.9.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\20556995fc58a15f50de4a2b21a5edd9\transformed\rules\lib\META-INF\proguard\androidx-lifecycle-lifecycle-common-java8.pro
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\20556995fc58a15f50de4a2b21a5edd9\transformed\rules\lib\META-INF\proguard\androidx-lifecycle-lifecycle-common-java8.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\b6391c467cc8d37a1fceadaf9559aef0\transformed\lifecycle-viewmodel-release\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\b6391c467cc8d37a1fceadaf9559aef0\transformed\lifecycle-viewmodel-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\27eb01bccc439fad4c431fe11744a39c\transformed\savedstate-ktx-1.3.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\27eb01bccc439fad4c431fe11744a39c\transformed\savedstate-ktx-1.3.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\f9050601dae5b1dbcce96a44cee8f8ed\transformed\savedstate-release\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\f9050601dae5b1dbcce96a44cee8f8ed\transformed\savedstate-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\08f0224dd2f3ac7f7ec5781649a0439f\transformed\lifecycle-viewmodel-savedstate-release\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\08f0224dd2f3ac7f7ec5781649a0439f\transformed\lifecycle-viewmodel-savedstate-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\8855094b50844372810db01026290202\transformed\coil-core-release\proguard.txt
-dontwarn coil3.PlatformContext

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\8855094b50844372810db01026290202\transformed\coil-core-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\32301300fe1d8bb38b9b0d036470e825\transformed\core-ktx-1.16.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\32301300fe1d8bb38b9b0d036470e825\transformed\core-ktx-1.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A rule that will keep classes that implement SidecarInterface$SidecarCallback if Sidecar seems
# be used. See b/157286362 and b/165268619 for details.
# TODO(b/208543178) investigate how to pass header jar to R8 so we don't need this rule
-if class androidx.window.layout.SidecarCompat {
  public setExtensionCallback(androidx.window.layout.ExtensionInterfaceCompat$ExtensionCallbackInterface);
}
-keep class androidx.window.layout.SidecarCompat$TranslatingCallback,
 androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback {
  public onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState);
  public onWindowLayoutChanged(android.os.IBinder, androidx.window.sidecar.SidecarWindowLayoutInfo);
}
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4f436669c1c0aa951dc18c727ff120d4\transformed\vectordrawable-animated-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4f436669c1c0aa951dc18c727ff120d4\transformed\vectordrawable-animated-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\5d8d6e1aab7e26ebc2fe0647a99d7826\transformed\animation-core-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\5d8d6e1aab7e26ebc2fe0647a99d7826\transformed\animation-core-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\366abc712e29383e40ce99631f098d2f\transformed\foundation-layout-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\366abc712e29383e40ce99631f098d2f\transformed\foundation-layout-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\78e087dd1cdd09d6432d260deb7bf0a8\transformed\foundation-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
}


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\78e087dd1cdd09d6432d260deb7bf0a8\transformed\foundation-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\10707ed4b16513a5d686adbcdd93d18f\transformed\ui-unit-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\10707ed4b16513a5d686adbcdd93d18f\transformed\ui-unit-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\39ccfaa4b46a364def42306f3c4848c5\transformed\ui-graphics-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\39ccfaa4b46a364def42306f3c4848c5\transformed\ui-graphics-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\df891fc547f4739d128cc2de1961a0ac\transformed\ui-util-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\df891fc547f4739d128cc2de1961a0ac\transformed\ui-util-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8e9ee106f64909a48113e968a603c5\transformed\ui-text-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8e9ee106f64909a48113e968a603c5\transformed\ui-text-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\7012401d2baa5baca522f517fb27a57e\transformed\graphics-path-1.0.1\proguard.txt
-keepclasseswithmembers class androidx.graphics.path.** {
    native <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\7012401d2baa5baca522f517fb27a57e\transformed\graphics-path-1.0.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\0c42c46107fd561085aaed561c4cf813\transformed\lifecycle-runtime-release\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# The deprecated `android.app.Fragment` creates `Fragment` instances using reflection.
# See: b/338958225, b/341537875
-keepclasseswithmembers,allowobfuscation public class androidx.lifecycle.ReportFragment {
    public <init>();
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\0c42c46107fd561085aaed561c4cf813\transformed\lifecycle-runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\db62b0c2343926056a7d6419eca539f7\transformed\lifecycle-viewmodel-ktx-2.9.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\db62b0c2343926056a7d6419eca539f7\transformed\lifecycle-viewmodel-ktx-2.9.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\416368b85810fee672d1adc3adb76197\transformed\lifecycle-viewmodel-compose-release\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\416368b85810fee672d1adc3adb76197\transformed\lifecycle-viewmodel-compose-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e5d97d34d84112940f02348422c3753d\transformed\ui-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# We supply these as stubs and are able to link to them at runtime
# because they are hidden public classes in Android. We don't want
# R8 to complain about them not being there during optimization.
-dontwarn android.view.RenderNode
-dontwarn android.view.DisplayListCanvas
-dontwarn android.view.HardwareCanvas

-keepclassmembers class androidx.compose.ui.platform.ViewLayerContainer {
    protected void dispatchGetDisplayList();
}

-keepclassmembers class androidx.compose.ui.platform.AndroidComposeView {
    android.view.View findViewByAccessibilityIdTraversal(int);
}

# Users can create Modifier.Node instances that implement multiple Modifier.Node interfaces,
# so we cannot tell whether two modifier.node instances are of the same type without using
# reflection to determine the class type. See b/265188224 for more context.
-keep,allowshrinking class * extends androidx.compose.ui.node.ModifierNodeElement

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.**.* {
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    # For methods returning Nothing
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);
    # For functions generating error messages
    static java.lang.String exceptionMessage*(...);
    java.lang.String exceptionMessage*(...);
}

# When pointer input modifier nodes are added dynamically and have the same keys (common when
# developers `Unit` for their keys), we need a way to differentiate them and using a
# functional interface and comparing classes allows us to do that.
-keepnames class androidx.compose.ui.input.pointer.PointerInputEventHandler {
    *;
}


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e5d97d34d84112940f02348422c3753d\transformed\ui-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\ac027c6e0e0240bf4974a08c7329219d\transformed\runtime-saveable-release\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\ac027c6e0e0240bf4974a08c7329219d\transformed\runtime-saveable-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\44e7bd1b86c32aef1e8297894b9d3141\transformed\runtime-release\proguard.txt
-assumenosideeffects public class androidx.compose.runtime.ComposerKt {
    void sourceInformation(androidx.compose.runtime.Composer,java.lang.String);
    void sourceInformationMarkerStart(androidx.compose.runtime.Composer,int,java.lang.String);
    void sourceInformationMarkerEnd(androidx.compose.runtime.Composer);
}

# Composer's class initializer doesn't do anything but create an EMPTY object. Marking the
# initializers as having no side effects can help encourage shrinkers to merge/devirtualize Composer
# with ComposerImpl.
-assumenosideeffects public class androidx.compose.runtime.Composer {
    void <clinit>();
}
-assumenosideeffects public class androidx.compose.runtime.ComposerImpl {
    void <clinit>();
}

# Keep all the functions created to throw an exception. We don't want these functions to be
# inlined in any way, which R8 will do by default. The whole point of these functions is to
# reduce the amount of code generated at the call site.
-keepclassmembers,allowshrinking,allowobfuscation class androidx.compose.runtime.** {
    # java.lang.Void == methods that return Nothing
    static void throw*Exception(...);
    static void throw*ExceptionForNullCheck(...);
    static java.lang.Void throw*Exception(...);
    static java.lang.Void throw*ExceptionForNullCheck(...);

    # For functions generating error messages
    static java.lang.String exceptionMessage*(...);
    java.lang.String exceptionMessage*(...);

    static void compose*RuntimeError(...);
    static java.lang.Void compose*RuntimeError(...);
}


# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\44e7bd1b86c32aef1e8297894b9d3141\transformed\runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ff346b9db1ba5563c0e48712d638e7\transformed\core-viewtree-1.0.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ff346b9db1ba5563c0e48712d638e7\transformed\core-viewtree-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\3935089e55b737620b1b17993f559d52\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt and other security providers are available.
-dontwarn okhttp3.internal.platform.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\3935089e55b737620b1b17993f559d52\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\54e40bed184b4a8c7d7a633ca40eec07\transformed\rules\lib\META-INF\proguard\ktor.pro
# Most of volatile fields are updated with AtomicFU and should not be mangled/removed
-keepclassmembers class io.ktor.** {
    volatile <fields>;
}

-keepclassmembernames class io.ktor.** {
    volatile <fields>;
}

# client engines are loaded using ServiceLoader so we need to keep them
-keep class io.ktor.client.engine.** implements io.ktor.client.HttpClientEngineContainer


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\54e40bed184b4a8c7d7a633ca40eec07\transformed\rules\lib\META-INF\proguard\ktor.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\9486d2f4e3a66d11ed1a7acdac202043\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-common.pro
# Keep `Companion` object fields of serializable classes.
# This avoids serializer lookup through `getDeclaredClasses` as done for named companion objects.
-if @kotlinx.serialization.Serializable class **
-keepclassmembers class <1> {
    static <1>$Companion Companion;
}

# Keep `serializer()` on companion objects (both default and named) of serializable classes.
-if @kotlinx.serialization.Serializable class ** {
    static **$* *;
}
-keepclassmembers class <2>$<3> {
    kotlinx.serialization.KSerializer serializer(...);
}

# Keep `INSTANCE.serializer()` of serializable objects.
-if @kotlinx.serialization.Serializable class ** {
    public static ** INSTANCE;
}
-keepclassmembers class <1> {
    public static <1> INSTANCE;
    kotlinx.serialization.KSerializer serializer(...);
}

# @Serializable and @Polymorphic are used at runtime for polymorphic serialization.
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault

# Don't print notes about potential mistakes or omissions in the configuration for kotlinx-serialization classes
# See also https://github.com/Kotlin/kotlinx.serialization/issues/1900
-dontnote kotlinx.serialization.**

# Serialization core uses `java.lang.ClassValue` for caching inside these specified classes.
# If there is no `java.lang.ClassValue` (for example, in Android), then R8/ProGuard will print a warning.
# However, since in this case they will not be used, we can disable these warnings
-dontwarn kotlinx.serialization.internal.ClassValueReferences

# disable optimisation for descriptor field because in some versions of ProGuard, optimization generates incorrect bytecode that causes a verification error
# see https://github.com/Kotlin/kotlinx.serialization/issues/2719
-keepclassmembers public class **$$serializer {
    private ** descriptor;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\9486d2f4e3a66d11ed1a7acdac202043\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-common.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\9486d2f4e3a66d11ed1a7acdac202043\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-r8.pro
# Rule to save runtime annotations on serializable class.
# If the R8 full mode is used, annotations are removed from classes-files.
#
# For the annotation serializer, it is necessary to read the `Serializable` annotation inside the serializer<T>() function - if it is present,
# then `SealedClassSerializer` is used, if absent, then `PolymorphicSerializer'.
#
# When using R8 full mode, all interfaces will be serialized using `PolymorphicSerializer`.
#
# see https://github.com/Kotlin/kotlinx.serialization/issues/2050

 -if @kotlinx.serialization.Serializable class **
 -keep, allowshrinking, allowoptimization, allowobfuscation, allowaccessmodification class <1>

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\9486d2f4e3a66d11ed1a7acdac202043\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-r8.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\3b38f12568d7a54212d16bceef07ffa3\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.internal.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\3b38f12568d7a54212d16bceef07ffa3\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\a28ba29aeaca867c5b6270f55007c528\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\a28ba29aeaca867c5b6270f55007c528\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\0a0f98d4ba2f333920fc08d582ef6821\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\0a0f98d4ba2f333920fc08d582ef6821\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\b027bcfc2bbe3124197624f8b76ed37c\transformed\exifinterface-1.4.1\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\b027bcfc2bbe3124197624f8b76ed37c\transformed\exifinterface-1.4.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b542c3590f99dc7a0df41dbadf7aad\transformed\rules\lib\META-INF\proguard\androidx-collection-collection-ktx.pro
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\c6b542c3590f99dc7a0df41dbadf7aad\transformed\rules\lib\META-INF\proguard\androidx-collection-collection-ktx.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4463651ffab1963a673ab044c56d8b9a\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4463651ffab1963a673ab044c56d8b9a\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>