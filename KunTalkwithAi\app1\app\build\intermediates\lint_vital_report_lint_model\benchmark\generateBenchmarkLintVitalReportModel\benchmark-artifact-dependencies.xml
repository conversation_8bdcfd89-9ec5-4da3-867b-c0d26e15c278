<dependencies>
  <compile
      roots="androidx.navigation:navigation-common:2.7.7@aar,androidx.navigation:navigation-runtime:2.7.7@aar,androidx.navigation:navigation-common-ktx:2.7.7@aar,androidx.navigation:navigation-runtime-ktx:2.7.7@aar,androidx.navigation:navigation-compose:2.7.7@aar,androidx.activity:activity:1.10.1@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.activity:activity-compose:1.10.1@aar,androidx.core:core:1.16.0@aar,androidx.compose.material3:material3-window-size-class-android:1.3.1@aar,androidx.compose.material3:material3-android:1.3.1@aar,androidx.compose.material:material-android:1.7.6@aar,io.coil-kt.coil3:coil-compose-android:3.2.0@aar,io.coil-kt.coil3:coil-compose-core-android:3.2.0@aar,androidx.compose.foundation:foundation-layout-android:1.8.0@aar,androidx.compose.material:material-ripple-android:1.7.6@aar,androidx.compose.foundation:foundation-android:1.8.0@aar,androidx.compose.animation:animation-core-android:1.8.0@aar,androidx.compose.animation:animation-android:1.8.0@aar,androidx.compose.ui:ui-util-android:1.8.0@aar,androidx.compose.ui:ui-unit-android:1.8.0@aar,androidx.compose.ui:ui-text-android:1.8.0@aar,androidx.compose.ui:ui-graphics-android:1.8.0@aar,androidx.compose.ui:ui-geometry-android:1.8.0@aar,androidx.compose.ui:ui-tooling-preview-android:1.8.0@aar,androidx.compose.material:material-icons-extended-android:1.7.6@aar,androidx.compose.material:material-icons-core-android:1.7.6@aar,androidx.compose.ui:ui-android:1.8.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar,androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar,io.coil-kt.coil3:coil-network-okhttp-jvm:3.2.0@jar,io.coil-kt.coil3:coil-android:3.2.0@aar,io.coil-kt.coil3:coil-network-core-android:3.2.0@aar,io.coil-kt.coil3:coil-core-android:3.2.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar,androidx.lifecycle:lifecycle-process:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar,androidx.core:core-ktx:1.16.0@aar,io.ktor:ktor-client-android-jvm:2.3.11@jar,io.ktor:ktor-client-content-negotiation-jvm:2.3.11@jar,io.ktor:ktor-client-logging-jvm:2.3.11@jar,io.ktor:ktor-client-core-jvm:2.3.11@jar,io.ktor:ktor-serialization-kotlinx-json-jvm:2.3.11@jar,io.ktor:ktor-events-jvm:2.3.11@jar,io.ktor:ktor-websocket-serialization-jvm:2.3.11@jar,io.ktor:ktor-serialization-kotlinx-jvm:2.3.11@jar,io.ktor:ktor-serialization-jvm:2.3.11@jar,io.ktor:ktor-websockets-jvm:2.3.11@jar,io.ktor:ktor-http-jvm:2.3.11@jar,io.ktor:ktor-utils-jvm:2.3.11@jar,io.ktor:ktor-io-jvm:2.3.11@jar,org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.10.2@jar,androidx.compose.runtime:runtime-saveable-android:1.8.0@aar,androidx.compose.runtime:runtime-android:1.8.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.7.3@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-jvm:1.5.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.core:core-viewtree:1.0.0@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,com.squareup.okio:okio-jvm:3.11.0@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.20@jar,androidx.profileinstaller:profileinstaller:1.4.1@aar,org.slf4j:slf4j-nop:2.0.12@jar,org.commonmark:commonmark-ext-gfm-tables:0.24.0@jar,org.commonmark:commonmark-ext-gfm-strikethrough:0.24.0@jar,org.commonmark:commonmark-ext-autolink:0.24.0@jar,org.commonmark:commonmark:0.24.0@jar,org.jsoup:jsoup:1.17.2@jar,com.google.code.gson:gson:2.10.1@jar,org.jetbrains:annotations:23.0.0@jar,org.slf4j:slf4j-api:2.0.12@jar,com.google.guava:listenablefuture:1.0@jar,org.jspecify:jspecify:1.0.0@jar,androidx.startup:startup-runtime:1.1.1@aar,org.nibor.autolink:autolink:0.11.0@jar">
    <dependency
        name="androidx.navigation:navigation-common:2.7.7@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.7@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.7.7@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.7.7@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.7.7@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.10.1@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.compose.material3:material3-window-size-class-android:1.3.1@aar"
        simpleName="androidx.compose.material3:material3-window-size-class-android"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.1@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.material:material-android:1.7.6@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="io.coil-kt.coil3:coil-compose-android:3.2.0@aar"
        simpleName="io.coil-kt.coil3:coil-compose-android"/>
    <dependency
        name="io.coil-kt.coil3:coil-compose-core-android:3.2.0@aar"
        simpleName="io.coil-kt.coil3:coil-compose-core-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.8.0@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.6@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.8.0@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.8.0@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.8.0@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.7.6@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.6@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="io.coil-kt.coil3:coil-network-okhttp-jvm:3.2.0@jar"
        simpleName="io.coil-kt.coil3:coil-network-okhttp-jvm"/>
    <dependency
        name="io.coil-kt.coil3:coil-android:3.2.0@aar"
        simpleName="io.coil-kt.coil3:coil-android"/>
    <dependency
        name="io.coil-kt.coil3:coil-network-core-android:3.2.0@aar"
        simpleName="io.coil-kt.coil3:coil-network-core-android"/>
    <dependency
        name="io.coil-kt.coil3:coil-core-android:3.2.0@aar"
        simpleName="io.coil-kt.coil3:coil-core-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="io.ktor:ktor-client-android-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-client-android-jvm"/>
    <dependency
        name="io.ktor:ktor-client-content-negotiation-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-client-content-negotiation-jvm"/>
    <dependency
        name="io.ktor:ktor-client-logging-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-client-logging-jvm"/>
    <dependency
        name="io.ktor:ktor-client-core-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-client-core-jvm"/>
    <dependency
        name="io.ktor:ktor-serialization-kotlinx-json-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-serialization-kotlinx-json-jvm"/>
    <dependency
        name="io.ktor:ktor-events-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-events-jvm"/>
    <dependency
        name="io.ktor:ktor-websocket-serialization-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-websocket-serialization-jvm"/>
    <dependency
        name="io.ktor:ktor-serialization-kotlinx-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-serialization-kotlinx-jvm"/>
    <dependency
        name="io.ktor:ktor-serialization-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-serialization-jvm"/>
    <dependency
        name="io.ktor:ktor-websockets-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-websockets-jvm"/>
    <dependency
        name="io.ktor:ktor-http-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-http-jvm"/>
    <dependency
        name="io.ktor:ktor-utils-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-utils-jvm"/>
    <dependency
        name="io.ktor:ktor-io-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-io-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-jdk8"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.8.0@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.8.0@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-jvm:1.5.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.11.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="org.slf4j:slf4j-nop:2.0.12@jar"
        simpleName="org.slf4j:slf4j-nop"/>
    <dependency
        name="org.commonmark:commonmark-ext-gfm-tables:0.24.0@jar"
        simpleName="org.commonmark:commonmark-ext-gfm-tables"/>
    <dependency
        name="org.commonmark:commonmark-ext-gfm-strikethrough:0.24.0@jar"
        simpleName="org.commonmark:commonmark-ext-gfm-strikethrough"/>
    <dependency
        name="org.commonmark:commonmark-ext-autolink:0.24.0@jar"
        simpleName="org.commonmark:commonmark-ext-autolink"/>
    <dependency
        name="org.commonmark:commonmark:0.24.0@jar"
        simpleName="org.commonmark:commonmark"/>
    <dependency
        name="org.jsoup:jsoup:1.17.2@jar"
        simpleName="org.jsoup:jsoup"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="org.slf4j:slf4j-api:2.0.12@jar"
        simpleName="org.slf4j:slf4j-api"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="org.nibor.autolink:autolink:0.11.0@jar"
        simpleName="org.nibor.autolink:autolink"/>
  </compile>
  <package
      roots="androidx.navigation:navigation-common:2.7.7@aar,androidx.navigation:navigation-runtime:2.7.7@aar,androidx.navigation:navigation-common-ktx:2.7.7@aar,androidx.navigation:navigation-runtime-ktx:2.7.7@aar,androidx.navigation:navigation-compose:2.7.7@aar,androidx.activity:activity:1.10.1@aar,androidx.compose.material:material-android:1.7.6@aar,androidx.compose.material3:material3-window-size-class-android:1.3.1@aar,androidx.compose.material3:material3-android:1.3.1@aar,io.coil-kt.coil3:coil-compose-android:3.2.0@aar,io.coil-kt.coil3:coil-compose-core-android:3.2.0@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar,androidx.lifecycle:lifecycle-common-java8:2.9.0@jar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar,io.coil-kt.coil3:coil-network-okhttp-jvm:3.2.0@jar,io.coil-kt.coil3:coil-android:3.2.0@aar,io.coil-kt.coil3:coil-network-core-android:3.2.0@aar,io.coil-kt.coil3:coil-core-android:3.2.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.16.0@aar,androidx.window:window:1.0.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.compose.material:material-ripple-android:1.7.6@aar,androidx.compose.animation:animation-core-android:1.8.0@aar,androidx.compose.animation:animation-android:1.8.0@aar,androidx.compose.foundation:foundation-layout-android:1.8.0@aar,androidx.compose.foundation:foundation-android:1.8.0@aar,androidx.compose.ui:ui-tooling-preview-android:1.8.0@aar,androidx.compose.ui:ui-unit-android:1.8.0@aar,androidx.compose.ui:ui-graphics-android:1.8.0@aar,androidx.compose.ui:ui-geometry-android:1.8.0@aar,androidx.compose.ui:ui-util-android:1.8.0@aar,androidx.compose.ui:ui-text-android:1.8.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.emoji2:emoji2:1.4.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.core:core:1.16.0@aar,androidx.core:core:1.16.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-process:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar,androidx.compose.material:material-icons-extended-android:1.7.6@aar,androidx.compose.material:material-icons-core-android:1.7.6@aar,com.google.accompanist:accompanist-drawablepainter:0.37.3@aar,androidx.compose.ui:ui-android:1.8.0@aar,androidx.activity:activity-ktx:1.10.1@aar,androidx.activity:activity-compose:1.10.1@aar,androidx.compose.runtime:runtime-saveable-android:1.8.0@aar,androidx.compose.runtime:runtime-android:1.8.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.core:core-viewtree:1.0.0@aar,io.ktor:ktor-client-android-jvm:2.3.11@jar,io.ktor:ktor-client-content-negotiation-jvm:2.3.11@jar,io.ktor:ktor-client-logging-jvm:2.3.11@jar,io.ktor:ktor-client-core-jvm:2.3.11@jar,io.ktor:ktor-serialization-kotlinx-json-jvm:2.3.11@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,io.ktor:ktor-events-jvm:2.3.11@jar,io.ktor:ktor-websocket-serialization-jvm:2.3.11@jar,io.ktor:ktor-serialization-kotlinx-jvm:2.3.11@jar,io.ktor:ktor-serialization-jvm:2.3.11@jar,io.ktor:ktor-websockets-jvm:2.3.11@jar,io.ktor:ktor-http-jvm:2.3.11@jar,io.ktor:ktor-utils-jvm:2.3.11@jar,io.ktor:ktor-io-jvm:2.3.11@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar,androidx.profileinstaller:profileinstaller:1.4.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.tracing:tracing:1.2.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.exifinterface:exifinterface:1.4.1@aar,androidx.collection:collection-ktx:1.5.0@jar,androidx.collection:collection-jvm:1.5.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,com.squareup.okio:okio-jvm:3.11.0@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.20@jar,org.slf4j:slf4j-nop:2.0.12@jar,org.commonmark:commonmark-ext-gfm-tables:0.24.0@jar,org.commonmark:commonmark-ext-gfm-strikethrough:0.24.0@jar,org.commonmark:commonmark-ext-autolink:0.24.0@jar,org.commonmark:commonmark:0.24.0@jar,org.jsoup:jsoup:1.17.2@jar,com.google.code.gson:gson:2.10.1@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:1.0@jar,org.slf4j:slf4j-api:2.0.12@jar,org.nibor.autolink:autolink:0.11.0@jar,org.jspecify:jspecify:1.0.0@jar">
    <dependency
        name="androidx.navigation:navigation-common:2.7.7@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.7@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.7.7@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.7.7@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.7.7@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.compose.material:material-android:1.7.6@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.material3:material3-window-size-class-android:1.3.1@aar"
        simpleName="androidx.compose.material3:material3-window-size-class-android"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.1@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="io.coil-kt.coil3:coil-compose-android:3.2.0@aar"
        simpleName="io.coil-kt.coil3:coil-compose-android"/>
    <dependency
        name="io.coil-kt.coil3:coil-compose-core-android:3.2.0@aar"
        simpleName="io.coil-kt.coil3:coil-compose-core-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="io.coil-kt.coil3:coil-network-okhttp-jvm:3.2.0@jar"
        simpleName="io.coil-kt.coil3:coil-network-okhttp-jvm"/>
    <dependency
        name="io.coil-kt.coil3:coil-android:3.2.0@aar"
        simpleName="io.coil-kt.coil3:coil-android"/>
    <dependency
        name="io.coil-kt.coil3:coil-network-core-android:3.2.0@aar"
        simpleName="io.coil-kt.coil3:coil-network-core-android"/>
    <dependency
        name="io.coil-kt.coil3:coil-core-android:3.2.0@aar"
        simpleName="io.coil-kt.coil3:coil-core-android"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.16.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.window:window:1.0.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.6@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.8.0@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.8.0@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.8.0@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.8.0@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.emoji2:emoji2:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.core:core:1.16.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.7.6@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.6@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="com.google.accompanist:accompanist-drawablepainter:0.37.3@aar"
        simpleName="com.google.accompanist:accompanist-drawablepainter"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.8.0@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.activity:activity-ktx:1.10.1@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.10.1@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.8.0@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.8.0@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="io.ktor:ktor-client-android-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-client-android-jvm"/>
    <dependency
        name="io.ktor:ktor-client-content-negotiation-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-client-content-negotiation-jvm"/>
    <dependency
        name="io.ktor:ktor-client-logging-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-client-logging-jvm"/>
    <dependency
        name="io.ktor:ktor-client-core-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-client-core-jvm"/>
    <dependency
        name="io.ktor:ktor-serialization-kotlinx-json-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-serialization-kotlinx-json-jvm"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="io.ktor:ktor-events-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-events-jvm"/>
    <dependency
        name="io.ktor:ktor-websocket-serialization-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-websocket-serialization-jvm"/>
    <dependency
        name="io.ktor:ktor-serialization-kotlinx-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-serialization-kotlinx-jvm"/>
    <dependency
        name="io.ktor:ktor-serialization-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-serialization-jvm"/>
    <dependency
        name="io.ktor:ktor-websockets-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-websockets-jvm"/>
    <dependency
        name="io.ktor:ktor-http-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-http-jvm"/>
    <dependency
        name="io.ktor:ktor-utils-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-utils-jvm"/>
    <dependency
        name="io.ktor:ktor-io-jvm:2.3.11@jar"
        simpleName="io.ktor:ktor-io-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-jdk8"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-slf4j"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.4.1@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.collection:collection-ktx:1.5.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.5.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.11.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.slf4j:slf4j-nop:2.0.12@jar"
        simpleName="org.slf4j:slf4j-nop"/>
    <dependency
        name="org.commonmark:commonmark-ext-gfm-tables:0.24.0@jar"
        simpleName="org.commonmark:commonmark-ext-gfm-tables"/>
    <dependency
        name="org.commonmark:commonmark-ext-gfm-strikethrough:0.24.0@jar"
        simpleName="org.commonmark:commonmark-ext-gfm-strikethrough"/>
    <dependency
        name="org.commonmark:commonmark-ext-autolink:0.24.0@jar"
        simpleName="org.commonmark:commonmark-ext-autolink"/>
    <dependency
        name="org.commonmark:commonmark:0.24.0@jar"
        simpleName="org.commonmark:commonmark"/>
    <dependency
        name="org.jsoup:jsoup:1.17.2@jar"
        simpleName="org.jsoup:jsoup"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.slf4j:slf4j-api:2.0.12@jar"
        simpleName="org.slf4j:slf4j-api"/>
    <dependency
        name="org.nibor.autolink:autolink:0.11.0@jar"
        simpleName="org.nibor.autolink:autolink"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
  </package>
</dependencies>
