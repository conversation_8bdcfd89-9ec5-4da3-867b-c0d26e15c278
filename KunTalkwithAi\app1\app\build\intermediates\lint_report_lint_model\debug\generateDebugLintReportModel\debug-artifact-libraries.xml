<libraries>
  <library
      name="androidx.navigation:navigation-common:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ebf5f12d179771782bf41f123e88642d\transformed\navigation-common-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ebf5f12d179771782bf41f123e88642d\transformed\navigation-common-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\55a18c243f4ac882d93d3a7cd9a80296\transformed\navigation-runtime-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\55a18c243f4ac882d93d3a7cd9a80296\transformed\navigation-runtime-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-ktx:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\69f7b49adaa0dc41678f40a22d832f89\transformed\navigation-common-ktx-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-ktx:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\69f7b49adaa0dc41678f40a22d832f89\transformed\navigation-common-ktx-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-ktx:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4fc72a0b32a789589bb4bfb0ac14697c\transformed\navigation-runtime-ktx-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-ktx:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4fc72a0b32a789589bb4bfb0ac14697c\transformed\navigation-runtime-ktx-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d916986582d5b8a927bbf5cebcb93f32\transformed\navigation-compose-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d916986582d5b8a927bbf5cebcb93f32\transformed\navigation-compose-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-window-size-class-android:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f563ddd6287c157039d74db0ad1cb0e5\transformed\material3-window-size-class-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-window-size-class-android:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f563ddd6287c157039d74db0ad1cb0e5\transformed\material3-window-size-class-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\39e186d1df9a6e64a80555ea95166efd\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\39e186d1df9a6e64a80555ea95166efd\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\01e4e2faf1698c602461ff28c59f0275\transformed\material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\01e4e2faf1698c602461ff28c59f0275\transformed\material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt.coil3:coil-compose-android:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1523b0e2843b8c3b2206eec75aaa7434\transformed\coil-compose-release\jars\classes.jar"
      resolved="io.coil-kt.coil3:coil-compose-android:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1523b0e2843b8c3b2206eec75aaa7434\transformed\coil-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt.coil3:coil-compose-core-android:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4d83c8787f6bcf4edb35d0eda8a1527d\transformed\coil-compose-core-release\jars\classes.jar"
      resolved="io.coil-kt.coil3:coil-compose-core-android:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4d83c8787f6bcf4edb35d0eda8a1527d\transformed\coil-compose-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\366abc712e29383e40ce99631f098d2f\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\366abc712e29383e40ce99631f098d2f\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd20e835c8bf1213f25bc00fd020fc0\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd20e835c8bf1213f25bc00fd020fc0\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\78e087dd1cdd09d6432d260deb7bf0a8\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\78e087dd1cdd09d6432d260deb7bf0a8\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5d8d6e1aab7e26ebc2fe0647a99d7826\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5d8d6e1aab7e26ebc2fe0647a99d7826\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\aaf586443dc0b3325f20e0aca29bad67\transformed\animation\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\aaf586443dc0b3325f20e0aca29bad67\transformed\animation"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\df891fc547f4739d128cc2de1961a0ac\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\df891fc547f4739d128cc2de1961a0ac\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\10707ed4b16513a5d686adbcdd93d18f\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\10707ed4b16513a5d686adbcdd93d18f\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8e9ee106f64909a48113e968a603c5\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb8e9ee106f64909a48113e968a603c5\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\39ccfaa4b46a364def42306f3c4848c5\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\39ccfaa4b46a364def42306f3c4848c5\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6842abce5f5da049e25d4c5e979128a3\transformed\ui-geometry\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6842abce5f5da049e25d4c5e979128a3\transformed\ui-geometry"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\dce33176b030ff3f432a03fbfc15cda5\transformed\ui-tooling-data\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\dce33176b030ff3f432a03fbfc15cda5\transformed\ui-tooling-data"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a8d092bd3583207106842a78f0ce5e3e\transformed\ui-tooling-preview\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a8d092bd3583207106842a78f0ce5e3e\transformed\ui-tooling-preview"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-extended-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b64d6959e2736c03e82d34c3fee8f775\transformed\material-icons-extended-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-extended-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b64d6959e2736c03e82d34c3fee8f775\transformed\material-icons-extended-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\57c8256c9aa49c73e70860005aae44e1\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\57c8256c9aa49c73e70860005aae44e1\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9cb287a452abedb26e4c003e4e8fe9db\transformed\activity-ktx-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9cb287a452abedb26e4c003e4e8fe9db\transformed\activity-ktx-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0\jars\classes.jar"
      resolved="androidx.core:core:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e5127fbf4aa18facdfc5d332b39b8229\transformed\core-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\56673a8b4479bfbc56575321e60b5dc5\transformed\lifecycle-livedata-core-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\56673a8b4479bfbc56575321e60b5dc5\transformed\lifecycle-livedata-core-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\178ff5c54d6bc8a1dbeb66f819bb9e8e\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\178ff5c54d6bc8a1dbeb66f819bb9e8e\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\08f0224dd2f3ac7f7ec5781649a0439f\transformed\lifecycle-viewmodel-savedstate-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\08f0224dd2f3ac7f7ec5781649a0439f\transformed\lifecycle-viewmodel-savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\27eb01bccc439fad4c431fe11744a39c\transformed\savedstate-ktx-1.3.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\27eb01bccc439fad4c431fe11744a39c\transformed\savedstate-ktx-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f9050601dae5b1dbcce96a44cee8f8ed\transformed\savedstate-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f9050601dae5b1dbcce96a44cee8f8ed\transformed\savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.9.0\ab739bccdb3541983385af169565c7c035d897e7\lifecycle-common-jvm-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.9.0"/>
  <library
      name="io.coil-kt.coil3:coil-network-okhttp-jvm:3.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.coil-kt.coil3\coil-network-okhttp-jvm\3.2.0\e7dbaa82df49fa511e99631c41beb0f5c5d10eab\coil-network-okhttp-jvm-3.2.0.jar"
      resolved="io.coil-kt.coil3:coil-network-okhttp-jvm:3.2.0"/>
  <library
      name="io.coil-kt.coil3:coil-android:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a8e8e35becc915fe2154275224e2d22d\transformed\coil-release\jars\classes.jar"
      resolved="io.coil-kt.coil3:coil-android:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a8e8e35becc915fe2154275224e2d22d\transformed\coil-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt.coil3:coil-network-core-android:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a36a82e5ed0ac26ab8c79744fe18751c\transformed\coil-network-core-release\jars\classes.jar"
      resolved="io.coil-kt.coil3:coil-network-core-android:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a36a82e5ed0ac26ab8c79744fe18751c\transformed\coil-network-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt.coil3:coil-core-android:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8855094b50844372810db01026290202\transformed\coil-core-release\jars\classes.jar"
      resolved="io.coil-kt.coil3:coil-core-android:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8855094b50844372810db01026290202\transformed\coil-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0c42c46107fd561085aaed561c4cf813\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0c42c46107fd561085aaed561c4cf813\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b6391c467cc8d37a1fceadaf9559aef0\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b6391c467cc8d37a1fceadaf9559aef0\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\db62b0c2343926056a7d6419eca539f7\transformed\lifecycle-viewmodel-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\db62b0c2343926056a7d6419eca539f7\transformed\lifecycle-viewmodel-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f834221be1c62a3e2d79d5a8d6b727f9\transformed\lifecycle-runtime-ktx\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f834221be1c62a3e2d79d5a8d6b727f9\transformed\lifecycle-runtime-ktx"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\58eeb28f86695ac6888d7afb81f7d4a5\transformed\lifecycle-process-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\416368b85810fee672d1adc3adb76197\transformed\lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\416368b85810fee672d1adc3adb76197\transformed\lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e5d97d34d84112940f02348422c3753d\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e5d97d34d84112940f02348422c3753d\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\77f3324e26b0fc7ae8260a8c77bc3ce8\transformed\ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\54a6656068f8f5c44960de6cc4931323\transformed\ui-test-manifest-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\eae94b96c36613b201ce2c6cc3fa87e3\transformed\activity-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\eae94b96c36613b201ce2c6cc3fa87e3\transformed\activity-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2d93cf781d3eb9d67a9f1152cdf6b002\transformed\activity-compose-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2d93cf781d3eb9d67a9f1152cdf6b002\transformed\activity-compose-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\32301300fe1d8bb38b9b0d036470e825\transformed\core-ktx-1.16.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\32301300fe1d8bb38b9b0d036470e825\transformed\core-ktx-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.ktor:ktor-client-android-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-client-android-jvm\2.3.11\cfd5bb2d52b06cd8d8a9a51d786b7f3bdd2781eb\ktor-client-android-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-client-android-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-client-content-negotiation-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-client-content-negotiation-jvm\2.3.11\2f739fb5e47be0158b174fefd1bb5a827fbc3243\ktor-client-content-negotiation-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-client-content-negotiation-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-client-logging-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-client-logging-jvm\2.3.11\20dfd9892569f4c4f36ed957dac14abf1b14a960\ktor-client-logging-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-client-logging-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-client-core-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-client-core-jvm\2.3.11\1ead4100b66a375ccc808bede2e2c1e480c015f\ktor-client-core-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-client-core-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-serialization-kotlinx-json-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-serialization-kotlinx-json-jvm\2.3.11\af0bc6f7ce125f8d3ddb0bc95273c3154447b618\ktor-serialization-kotlinx-json-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-serialization-kotlinx-json-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-events-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-events-jvm\2.3.11\e6a6d901927636a1fad5617397f5d40d2fc1d54b\ktor-events-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-events-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-websocket-serialization-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-websocket-serialization-jvm\2.3.11\195426b52d1647738a12022d1e0728326940a358\ktor-websocket-serialization-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-websocket-serialization-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-serialization-kotlinx-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-serialization-kotlinx-jvm\2.3.11\cfb1cc8db157f49fca372d4cf860b5dc7fe42623\ktor-serialization-kotlinx-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-serialization-kotlinx-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-serialization-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-serialization-jvm\2.3.11\5fd3c07683c145e49ff26d22af898178f20d813d\ktor-serialization-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-serialization-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-websockets-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-websockets-jvm\2.3.11\23bd6a7d3d1fed4ffb56a08f93e72eadb9818a1b\ktor-websockets-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-websockets-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-http-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-http-jvm\2.3.11\80eaa88b1c5f966ef87ca205bb05beb0e50cf99f\ktor-http-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-http-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-utils-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-utils-jvm\2.3.11\db227e84b9b155891262f7376ad22b6fb9e4ae6\ktor-utils-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-utils-jvm:2.3.11"/>
  <library
      name="io.ktor:ktor-io-jvm:2.3.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-io-jvm\2.3.11\a129eccdd57a710f402661c89498abdd52107e6a\ktor-io-jvm-2.3.11.jar"
      resolved="io.ktor:ktor-io-jvm:2.3.11"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.10.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-jdk8\1.10.2\809f71bf722bad7e78ebe8ec710c0327c23b7a31\kotlinx-coroutines-jdk8-1.10.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.10.2"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ac027c6e0e0240bf4974a08c7329219d\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ac027c6e0e0240bf4974a08c7329219d\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\44e7bd1b86c32aef1e8297894b9d3141\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\44e7bd1b86c32aef1e8297894b9d3141\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.10.2\4a9f78ef49483748e2c129f3d124b8fa249dafbf\kotlinx-coroutines-core-jvm-1.10.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.10.2\c8705d275d81f19e8afaf3ff9b5bf7a4b6b6c19b\kotlinx-coroutines-android-1.10.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.7.3\1f226780b845ff9206474c05159245d861556249\kotlinx-serialization-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-json-jvm\1.7.3\6701e8c68d9e82387ce72ee96e8ddf058208d58f\kotlinx-serialization-json-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.7.3"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ff346b9db1ba5563c0e48712d638e7\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ff346b9db1ba5563c0e48712d638e7\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\44dfff852f8446f00028c43f19613595\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\44dfff852f8446f00028c43f19613595\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0a0f98d4ba2f333920fc08d582ef6821\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0a0f98d4ba2f333920fc08d582ef6821\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-jvm:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.5.0\7ba2c69414d46ebc2dd76598bdd0a75c54281a57\collection-jvm-1.5.0.jar"
      resolved="androidx.collection:collection-jvm:1.5.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="com.squareup.okio:okio-jvm:3.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.11.0\e19f243ee46335ab089613a25f3ea1f65df285b1\okio-jvm-3.11.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.11.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.1.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.1.20\aa8ca79cd50578314f6d1180c47cbe14c0fee567\kotlin-stdlib-2.1.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.1.20"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6aab0203995ed8c83282f13742e3c95f\transformed\profileinstaller-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.slf4j:slf4j-nop:2.0.12@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-nop\2.0.12\501ba46a9952a13641d15b7fc5f72bd3cf3177cb\slf4j-nop-2.0.12.jar"
      resolved="org.slf4j:slf4j-nop:2.0.12"/>
  <library
      name="org.commonmark:commonmark-ext-gfm-tables:0.24.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.commonmark\commonmark-ext-gfm-tables\0.24.0\8a30c4e89ce33450c47604325751bec613bce541\commonmark-ext-gfm-tables-0.24.0.jar"
      resolved="org.commonmark:commonmark-ext-gfm-tables:0.24.0"/>
  <library
      name="org.commonmark:commonmark-ext-gfm-strikethrough:0.24.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.commonmark\commonmark-ext-gfm-strikethrough\0.24.0\9e9c1e5b50340643099d52c6b841f60fb6f54c27\commonmark-ext-gfm-strikethrough-0.24.0.jar"
      resolved="org.commonmark:commonmark-ext-gfm-strikethrough:0.24.0"/>
  <library
      name="org.commonmark:commonmark-ext-autolink:0.24.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.commonmark\commonmark-ext-autolink\0.24.0\703e28852088ff1b4b3a06622416fd807147bd84\commonmark-ext-autolink-0.24.0.jar"
      resolved="org.commonmark:commonmark-ext-autolink:0.24.0"/>
  <library
      name="org.commonmark:commonmark:0.24.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.commonmark\commonmark\0.24.0\59af01016ece382b55b4acb6a5190b08879c637c\commonmark-0.24.0.jar"
      resolved="org.commonmark:commonmark:0.24.0"/>
  <library
      name="org.jsoup:jsoup:1.17.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jsoup\jsoup\1.17.2\1e75b08d7019546a954f1e359477f916f537a34d\jsoup-1.17.2.jar"
      resolved="org.jsoup:jsoup:1.17.2"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="org.slf4j:slf4j-api:2.0.12@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\2.0.12\48f109a2a6d8f446c794f3e3fa0d86df0cdfa312\slf4j-api-2.0.12.jar"
      resolved="org.slf4j:slf4j-api:2.0.12"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6d19ceabcd00b3e0d82ee2aacc706da1\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.nibor.autolink:autolink:0.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.nibor.autolink\autolink\0.11.0\32abc7854d5801d19ff16be92362fa4c511d9a70\autolink-0.11.0.jar"
      resolved="org.nibor.autolink:autolink:0.11.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.9.0\86a8e6b9d0ba0c964fdc7223a38b6ce74bcb24dd\lifecycle-common-java8-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.9.0"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0f5cff30a180d0faaad1f67045ce7477\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0f5cff30a180d0faaad1f67045ce7477\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0\jars\classes.jar"
      resolved="androidx.window:window:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c2fe2863a9c50634a9253e6514925e3b\transformed\window-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\33314a664413fc190d8af7aeef943451\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\33314a664413fc190d8af7aeef943451\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4f436669c1c0aa951dc18c727ff120d4\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4f436669c1c0aa951dc18c727ff120d4\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bea41d8575f5b2c9f043a620a60b22ea\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bea41d8575f5b2c9f043a620a60b22ea\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a893d44f769554c05f2f9042f5c4ba3e\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a893d44f769554c05f2f9042f5c4ba3e\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8d9770e3d771c462ed39db07884dbd01\transformed\emoji2-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7012401d2baa5baca522f517fb27a57e\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7012401d2baa5baca522f517fb27a57e\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.accompanist:accompanist-drawablepainter:0.37.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4295243598a600d6392f4746f1b5c8dd\transformed\accompanist-drawablepainter-0.37.3\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-drawablepainter:0.37.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4295243598a600d6392f4746f1b5c8dd\transformed\accompanist-drawablepainter-0.37.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.10.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-slf4j\1.10.2\1271f9d4a929150bb87ab8d1dc1e86d4bcf039f3\kotlinx-coroutines-slf4j-1.10.2.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.10.2"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bbc5f0111a2902138f99cccb9f2b8de1\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bbc5f0111a2902138f99cccb9f2b8de1\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\13a3bbc9656c8aa176373f0021fcd2e2\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\13a3bbc9656c8aa176373f0021fcd2e2\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\aae976a0f67053f6b2d2ba71dceadfc4\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\aae976a0f67053f6b2d2ba71dceadfc4\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b027bcfc2bbe3124197624f8b76ed37c\transformed\exifinterface-1.4.1\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b027bcfc2bbe3124197624f8b76ed37c\transformed\exifinterface-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.5.0\27c78a926a16a1bf792b2285cf2834e8caae4a07\collection-ktx-1.5.0.jar"
      resolved="androidx.collection:collection-ktx:1.5.0"/>
</libraries>
