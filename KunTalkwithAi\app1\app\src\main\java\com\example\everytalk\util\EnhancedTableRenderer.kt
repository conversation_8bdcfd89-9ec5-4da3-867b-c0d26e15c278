package com.example.everytalk.util

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp

/**
 * 增强的表格渲染器
 * 专门优化AI输出中的表格显示
 */
object EnhancedTableRenderer {
    
    private val headerStyle = SpanStyle(
        fontWeight = FontWeight.Bold,
        color = Color(0xFF2196F3),
        fontSize = 14.sp
    )
    
    private val cellStyle = SpanStyle(
        color = Color(0xFF333333),
        fontSize = 13.sp
    )
    
    private val borderStyle = SpanStyle(
        color = Color(0xFF666666),
        fontFamily = FontFamily.Monospace
    )
    
    /**
     * 渲染Markdown表格为AnnotatedString
     */
    fun renderTable(tableText: String): AnnotatedString {
        val lines = tableText.trim().split('\n').filter { it.trim().isNotEmpty() }
        if (lines.isEmpty()) return AnnotatedString("")
        
        return buildAnnotatedString {
            var isFirstRow = true
            
            for (line in lines) {
                val trimmedLine = line.trim()
                
                // 跳过分隔行 (如 |---|---|)
                if (trimmedLine.matches(Regex("^\\|?\\s*:?-+:?\\s*(\\|\\s*:?-+:?\\s*)*\\|?$"))) {
                    continue
                }
                
                // 解析表格行
                val cells = parseTableRow(trimmedLine)
                if (cells.isNotEmpty()) {
                    renderTableRow(cells, isFirstRow)
                    append("\n")
                    isFirstRow = false
                }
            }
        }
    }
    
    /**
     * 解析表格行
     */
    private fun parseTableRow(line: String): List<String> {
        var cleanLine = line.trim()
        
        // 移除首尾的 |
        if (cleanLine.startsWith("|")) cleanLine = cleanLine.substring(1)
        if (cleanLine.endsWith("|")) cleanLine = cleanLine.substring(0, cleanLine.length - 1)
        
        // 分割单元格
        return cleanLine.split("|").map { it.trim() }
    }
    
    /**
     * 渲染表格行
     */
    private fun AnnotatedString.Builder.renderTableRow(cells: List<String>, isHeader: Boolean) {
        val style = if (isHeader) headerStyle else cellStyle
        
        // 左边框
        withStyle(borderStyle) {
            append("│ ")
        }
        
        for (i in cells.indices) {
            val cell = cells[i]
            
            // 渲染单元格内容
            withStyle(style) {
                append(formatCellContent(cell))
            }
            
            // 单元格间分隔符
            if (i < cells.size - 1) {
                withStyle(borderStyle) {
                    append(" │ ")
                }
            }
        }
        
        // 右边框
        withStyle(borderStyle) {
            append(" │")
        }
        
        // 如果是表头，添加分隔线
        if (isHeader) {
            append("\n")
            withStyle(borderStyle) {
                val totalWidth = cells.sumOf { it.length + 3 } + 1
                append("├" + "─".repeat(totalWidth - 2) + "┤")
            }
        }
    }
    
    /**
     * 格式化单元格内容
     */
    private fun formatCellContent(content: String): String {
        var formatted = content.trim()
        
        // 处理空单元格
        if (formatted.isEmpty()) {
            formatted = " "
        }
        
        // 限制单元格宽度，避免过长
        if (formatted.length > 20) {
            formatted = formatted.take(17) + "..."
        }
        
        return formatted
    }
    
    /**
     * 检测是否为表格内容
     */
    fun isTableContent(text: String): Boolean {
        val lines = text.trim().split('\n')
        
        // 至少需要2行
        if (lines.size < 2) return false
        
        // 检查是否有表格分隔符
        val hasTableSeparator = lines.any { line ->
            line.trim().matches(Regex("^\\|?\\s*:?-+:?\\s*(\\|\\s*:?-+:?\\s*)*\\|?$"))
        }
        
        // 检查是否有管道符
        val hasPipeSymbols = lines.count { line ->
            line.contains("|") && line.split("|").size >= 3
        } >= 2
        
        return hasTableSeparator || hasPipeSymbols
    }
    
    /**
     * 从文本中提取表格
     */
    fun extractTables(text: String): List<Pair<IntRange, String>> {
        val lines = text.split('\n')
        val tables = mutableListOf<Pair<IntRange, String>>()
        var currentTableStart = -1
        var currentTableLines = mutableListOf<String>()
        
        for (i in lines.indices) {
            val line = lines[i].trim()
            
            // 检查是否为表格行
            if (line.contains("|") && (line.split("|").size >= 3 || 
                line.matches(Regex("^\\|?\\s*:?-+:?\\s*(\\|\\s*:?-+:?\\s*)*\\|?$")))) {
                
                if (currentTableStart == -1) {
                    currentTableStart = i
                }
                currentTableLines.add(lines[i])
            } else {
                // 结束当前表格
                if (currentTableStart != -1 && currentTableLines.size >= 2) {
                    val tableText = currentTableLines.joinToString("\n")
                    val startPos = lines.take(currentTableStart).sumOf { it.length + 1 }
                    val endPos = startPos + tableText.length
                    tables.add(IntRange(startPos, endPos) to tableText)
                }
                
                currentTableStart = -1
                currentTableLines.clear()
            }
        }
        
        // 处理文本末尾的表格
        if (currentTableStart != -1 && currentTableLines.size >= 2) {
            val tableText = currentTableLines.joinToString("\n")
            val startPos = lines.take(currentTableStart).sumOf { it.length + 1 }
            val endPos = startPos + tableText.length
            tables.add(IntRange(startPos, endPos) to tableText)
        }
        
        return tables
    }
}