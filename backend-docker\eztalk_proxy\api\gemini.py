import logging
import httpx
import orj<PERSON>
import asyncio
import base64
import io
from typing import List

import google.generativeai as genai
import docx
from fastapi import Request, UploadFile
from fastapi.responses import StreamingResponse

from ..models.api_models import (
    ChatRequestModel,
    AppStreamEventPy,
    PartsApiMessagePy,
    AbstractApiMessagePy,
    SimpleTextApiMessagePy,
    PyTextContentPart,
    PyInlineDataContentPart,
    IncomingApiContentPart,
    PyFileUriContentPart,
    WebSearchResult
)
from ..core.config import (
    API_TIMEOUT,
    GOOGLE_API_KEY_ENV,
    MAX_DOCUMENT_UPLOAD_SIZE_MB
)
from ..utils.helpers import (
    get_current_time_iso,
    orjson_dumps_bytes_wrapper
)
from ..services.request_builder import prepare_gemini_rest_api_request
from ..services.stream_processor import (
    process_openai_like_sse_stream,
    handle_stream_error,
    handle_stream_cleanup,
    should_apply_custom_separator_logic
)
from ..services.markdown_processor import process_content_for_model
from ..services.web_search import perform_web_search, generate_search_context_message_content

logger = logging.getLogger("EzTalkProxy.Handlers.Gemini")

IMAGE_MIME_TYPES = ["image/png", "image/jpeg", "image/webp", "image/heic", "image/heif"]
DOCUMENT_MIME_TYPES = [
    "application/pdf",
    "application/x-javascript", "text/javascript",
    "application/x-python", "text/x-python",
    "text/plain",
    "text/html",
    "text/css",
    "text/md",
    "text/markdown",
    "text/csv",
    "text/xml",
    "text/rtf"
]
VIDEO_MIME_TYPES = [
    "video/mp4", "video/mpeg", "video/quicktime", "video/x-msvideo", "video/x-flv",
    "video/x-matroska", "video/webm", "video/x-ms-wmv", "video/3gpp", "video/x-m4v"
]
AUDIO_MIME_TYPES = [
    "audio/wav", "audio/mpeg", "audio/aac", "audio/ogg", "audio/opus", "audio/flac", "audio/3gpp"
]

import re

def cleanup_dirty_markdown(text: str) -> str:
    """
    使用统一的markdown处理器清理Gemini产生的内容
    特别针对联网搜索结果产生的大量空白段落进行优化
    """
    if not isinstance(text, str):
        logger.debug("cleanup_dirty_markdown: Input is not string, returning empty")
        return ""
    
    # 使用统一的markdown处理器，指定为Gemini模型类型
    return process_content_for_model(text, "", "gemini")

def fix_math_formulas(text: str) -> str:
    """Fix common LaTeX math formula issues in Gemini output"""
    if not text or '$' not in text:
        return text
    
    # Fix common LaTeX syntax errors
    fixes = {
        # Fix fraction formatting
        r'\\frac\s+(\w+)\s+(\w+)': r'\\frac{\1}{\2}',
        r'\\frac\s*\{\s*([^}]+)\s*\}\s*\{\s*([^}]+)\s*\}': r'\\frac{\1}{\2}',
        
        # Fix square root formatting
        r'\\sqrt\s+(\w+)': r'\\sqrt{\1}',
        r'\\sqrt\s*\{\s*([^}]+)\s*\}': r'\\sqrt{\1}',
        
        # Fix sum and integral formatting
        r'\\sum\s*_\s*(\w+)\s*\^\s*(\w+)': r'\\sum_{\1}^{\2}',
        r'\\int\s*_\s*(\w+)\s*\^\s*(\w+)': r'\\int_{\1}^{\2}',
        
        # Fix spaces around math delimiters
        r'\$\s+': r'$',
        r'\s+\$': r'$',
        r'\$\$\s+': r'$$',
        r'\s+\$\$': r'$$',
        
        # Fix braces spacing
        r'\{\s+': r'{',
        r'\s+\}': r'}',
    }
    
    for pattern, replacement in fixes.items():
        text = re.sub(pattern, replacement, text)
    
    # Ensure math delimiters are balanced
    text = ensure_math_delimiters_balanced(text)
    
    return text

def ensure_math_delimiters_balanced(text: str) -> str:
    """Ensure math delimiters are properly balanced"""
    # Fix unmatched single dollar signs
    single_dollar_count = 0
    result = []
    i = 0
    
    while i < len(text):
        if i < len(text) - 1 and text[i:i+2] == '$$':
            result.append('$$')
            i += 2
        elif text[i] == '$':
            single_dollar_count += 1
            result.append('$')
            i += 1
        else:
            result.append(text[i])
            i += 1
    
    # Add missing closing dollar if needed
    if single_dollar_count % 2 == 1:
        result.append('$')
    
    return ''.join(result)

def fix_table_formatting(text: str) -> str:
    """Fix table formatting issues in Gemini output"""
    if '|' not in text:
        return text
    
    lines = text.split('\n')
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        if '|' in line and line.strip():
            # Detect table start
            table_lines = []
            j = i
            
            # Collect all table lines
            while j < len(lines) and '|' in lines[j] and lines[j].strip():
                table_lines.append(lines[j])
                j += 1
            
            if table_lines:
                # Fix table formatting
                fixed_table = fix_table_lines(table_lines)
                fixed_lines.extend(fixed_table)
            
            i = j
        else:
            fixed_lines.append(line)
            i += 1
    
    return '\n'.join(fixed_lines)

def fix_table_lines(table_lines: list) -> list:
    """Fix individual table lines"""
    fixed = []
    
    for idx, line in enumerate(table_lines):
        # Clean and format table row
        cells = [cell.strip() for cell in line.split('|')]
        
        # Remove empty cells at start/end
        if cells and cells[0] == '':
            cells = cells[1:]
        if cells and cells[-1] == '':
            cells = cells[:-1]
        
        if cells:
            formatted_line = '| ' + ' | '.join(cells) + ' |'
            fixed.append(formatted_line)
            
            # Add separator after header if missing
            if idx == 0 and len(table_lines) > 1:
                next_line = table_lines[1] if idx + 1 < len(table_lines) else ""
                if not is_table_separator(next_line):
                    separator = '| ' + ' | '.join(['---'] * len(cells)) + ' |'
                    fixed.append(separator)
    
    return fixed

def is_table_separator(line: str) -> bool:
    """Check if line is a table separator"""
    return bool(re.match(r'^\s*\|[\s\-:]+\|\s*$', line))

def fix_duplicate_list_markers(text: str) -> str:
    """Fix duplicate list markers like '1. 1. 1. content' -> '1. content' and renumber ordered lists"""
    if not text:
        return text
    
    lines = text.split('\n')
    
    # First, clean up duplicate markers
    cleaned_lines = []
    for line in lines:
        cleaned_line = cleanup_duplicate_markers_in_line(line)
        cleaned_lines.append(cleaned_line)
    
    # Then, renumber ordered lists
    renumbered_lines = renumber_ordered_lists(cleaned_lines)
    
    return '\n'.join(renumbered_lines)

def renumber_ordered_lists(lines: list) -> list:
    """Renumber ordered lists to ensure correct sequential numbering"""
    result = []
    # Track numbering for different indent levels
    indent_counters = {}
    
    # 在流式传输中，避免频繁重新编号导致的编号混乱
    # 只有当检测到明显的重复编号时才进行重新编号
    has_duplicate_numbering = False
    for line in lines:
        stripped_line = line.strip()
        if re.match(r'^1\.\s+.*', stripped_line):
            # 检查是否有多个连续的"1."开头的列表项
            count_1_items = sum(1 for l in lines if re.match(r'^1\.\s+.*', l.strip()))
            if count_1_items > 1:
                has_duplicate_numbering = True
                break
    
    # 如果没有发现重复编号问题，直接返回原文本
    if not has_duplicate_numbering:
        return lines
    
    for line in lines:
        stripped_line = line.strip()
        
        # Check if this is an ordered list item
        ordered_list_match = re.match(r'^(\d+)\.\s+(.*)$', stripped_line)
        
        if ordered_list_match:
            content = ordered_list_match.group(2)
            current_indent = len(line) - len(line.lstrip())
            
            # Initialize or increment counter for this indent level
            if current_indent not in indent_counters:
                indent_counters[current_indent] = 1
            else:
                indent_counters[current_indent] += 1
            
            # Reset counters for deeper indent levels
            keys_to_remove = [k for k in indent_counters.keys() if k > current_indent]
            for k in keys_to_remove:
                del indent_counters[k]
            
            result.append(' ' * current_indent + f"{indent_counters[current_indent]}. {content}")
        else:
            # Non-ordered list item
            if stripped_line and not re.match(r'^[*+-]\s+.*', stripped_line):
                # 遇到非列表内容时，检查是否应该重置计数器
                # 避免在段落中间重置导致编号混乱
                if not any(re.match(r'^\d+\.\s+.*', l.strip()) for l in lines[lines.index(line)+1:lines.index(line)+3] if lines.index(line)+1 < len(lines)):
                    indent_counters.clear()
            result.append(line)
    
    return result

def fix_code_blocks(text: str) -> str:
    """Fix unclosed code blocks and other code block formatting issues"""
    if not text or '```' not in text:
        return text
    
    logger.debug(f"Fixing code blocks in text: {text[:100]}...")
    
    lines = text.split('\n')
    fixed_lines = []
    code_block_count = 0
    in_code_block = False
    current_language = ""
    
    for i, line in enumerate(lines):
        stripped_line = line.strip()
        
        # Check if this line contains code block markers
        if stripped_line.startswith('```'):
            code_block_count += 1
            if not in_code_block:
                # Starting a code block
                in_code_block = True
                # Extract language if specified
                current_language = stripped_line[3:].strip()
                fixed_lines.append(f"```{current_language}")
                logger.debug(f"Started code block with language: {current_language}")
            else:
                # Ending a code block
                in_code_block = False
                current_language = ""
                fixed_lines.append("```")
                logger.debug("Ended code block")
        else:
            # Regular line
            fixed_lines.append(line)
    
    # If we end with an unclosed code block, close it
    if in_code_block:
        fixed_lines.append("```")
        logger.info("Fixed unclosed code block by adding closing marker")
    
    # Handle cases where code blocks are unbalanced
    result_text = '\n'.join(fixed_lines)
    
    # Count opening and closing markers
    opening_markers = result_text.count('```')
    if opening_markers % 2 != 0:
        # Odd number of markers means unbalanced
        result_text += '\n```'
        logger.info(f"Added missing code block closing marker. Total markers: {opening_markers + 1}")
    
    # Fix code blocks that appear in the middle of sentences
    # This handles cases where ``` appears inline but should be a block
    lines = result_text.split('\n')
    fixed_lines = []
    
    for line in lines:
        if '```' in line and not line.strip().startswith('```'):
            # Code block marker in the middle of a line
            parts = line.split('```')
            if len(parts) >= 2:
                # Ensure proper line breaks around code blocks
                new_line = parts[0].rstrip()
                if new_line:
                    fixed_lines.append(new_line)
                
                for j in range(1, len(parts)):
                    if j % 2 == 1:  # Opening marker
                        lang = parts[j].split()[0] if parts[j].strip() else ""
                        fixed_lines.append(f"```{lang}")
                        if len(parts[j].split()) > 1:
                            fixed_lines.append(' '.join(parts[j].split()[1:]))
                    else:  # Closing marker
                        if parts[j].strip():
                            fixed_lines.append(parts[j].lstrip())
                        fixed_lines.append("```")
                logger.debug("Fixed inline code block markers")
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    final_result = '\n'.join(fixed_lines)
    logger.debug(f"Code block fixing completed. Original: {len(text)} chars, Fixed: {len(final_result)} chars")
    return final_result

def cleanup_duplicate_markers_in_line(line: str) -> str:
    """Clean up duplicate markers in a single line"""
    if not line.strip():
        return line
    
    # Skip table lines (containing |)
    if '|' in line:
        return line
    
    # Skip lines that look like table separators
    if re.match(r'^\s*[\-:]+\s*$', line):
        return line
    
    # Handle duplicate numbered list markers (e.g., "1. 1. 1. content")
    duplicate_number_pattern = r'^(\s*)(\d+\.\s+)+(\d+\.\s+)(.*)$'
    match = re.match(duplicate_number_pattern, line)
    if match:
        indent = match.group(1)
        last_number = match.group(3)
        content = match.group(4)
        logger.debug(f"Fixed duplicate numbered markers in line: {line.strip()}")
        return f"{indent}{last_number}{content}"
    
    # Handle duplicate bullet markers (e.g., "- - - content")
    duplicate_bullet_pattern = r'^(\s*)([*+-]\s+)+([*+-]\s+)(.*)$'
    match = re.match(duplicate_bullet_pattern, line)
    if match:
        indent = match.group(1)
        bullet = match.group(3)
        content = match.group(4)
        logger.debug(f"Fixed duplicate bullet markers in line: {line.strip()}")
        return f"{indent}{bullet}{content}"
    
    # Handle mixed duplicate markers (e.g., "1. - 1. content")
    mixed_pattern = r'^(\s*)([\d+\.\s+|[*+-]\s+]+)(\d+\.\s+|[*+-]\s+)(.*)$'
    match = re.match(mixed_pattern, line)
    if match:
        indent = match.group(1)
        last_marker = match.group(3)
        content = match.group(4)
        logger.debug(f"Fixed mixed duplicate markers in line: {line.strip()}")
        return f"{indent}{last_marker}{content}"
    
    return line

def is_google_official_api(api_address: str) -> bool:
    """Check if the API address is Google's official Gemini API"""
    if not api_address:
        return True  # Default to Google official if no address specified
    
    google_domains = [
        "generativelanguage.googleapis.com",
        "ai.google.dev",
        "googleapis.com"
    ]
    
    api_address_lower = api_address.lower()
    return any(domain in api_address_lower for domain in google_domains)

async def sse_event_serializer_rest(event_data: AppStreamEventPy) -> bytes:
    return orjson_dumps_bytes_wrapper(event_data.model_dump(by_alias=True, exclude_none=True))

async def handle_gemini_request(
    gemini_chat_input: ChatRequestModel,
    uploaded_files: List[UploadFile],
    fastapi_request_obj: Request,
    http_client: httpx.AsyncClient,
    request_id: str,
):
    log_prefix = f"RID-{request_id}"
    active_messages_for_llm: List[AbstractApiMessagePy] = [msg.model_copy(deep=True) for msg in gemini_chat_input.messages]
    newly_created_multimodal_parts: List[IncomingApiContentPart] = []

    # Only use user-provided API key, no fallback to environment variable
    if not gemini_chat_input.api_key:
        logger.error(f"{log_prefix}: No user-provided API key for Gemini")
        async def error_gen():
            yield await sse_event_serializer_rest(AppStreamEventPy(type="error", message="No API key provided by user", timestamp=get_current_time_iso()))
            yield await sse_event_serializer_rest(AppStreamEventPy(type="finish", reason="no_api_key", timestamp=get_current_time_iso()))
        return StreamingResponse(error_gen(), media_type="text/event-stream")
    
    # Check if this is a Google official API address
    api_address = gemini_chat_input.api_address or ""
    is_google_official = is_google_official_api(api_address)
    
    if is_google_official:
        # Use Gemini native format for Google official API
        genai.configure(api_key=gemini_chat_input.api_key)
        logger.info(f"{log_prefix}: Using Gemini native format for Google official API")
    else:
        # Use OpenAI compatible format for non-Google APIs
        logger.info(f"{log_prefix}: Using OpenAI compatible format for non-Google API: {api_address}")
        # Redirect to OpenAI compatible handler
        from . import openai
        return await openai.handle_openai_compatible_request(
            chat_input=gemini_chat_input,
            uploaded_documents=uploaded_files,
            fastapi_request_obj=fastapi_request_obj,
            http_client=http_client,
            request_id=request_id,
        )

    # Process uploaded files
    if uploaded_files:
        for uploaded_file in uploaded_files:
            mime_type = uploaded_file.content_type.lower() if uploaded_file.content_type else ""
            filename = uploaded_file.filename or "unknown"
            
            try:
                if mime_type in IMAGE_MIME_TYPES:
                    await uploaded_file.seek(0)
                    file_bytes = await uploaded_file.read()
                    base64_data = base64.b64encode(file_bytes).decode('utf-8')
                    newly_created_multimodal_parts.append(PyInlineDataContentPart(
                        type="inline_data_content", mimeType=mime_type, base64Data=base64_data
                    ))
                elif mime_type in DOCUMENT_MIME_TYPES:
                    logger.info(f"{log_prefix}: Processing document for Gemini: {filename} ({mime_type})")
                    await uploaded_file.seek(0)
                    file_bytes = await uploaded_file.read()
                    base64_data = base64.b64encode(file_bytes).decode('utf-8')
                    newly_created_multimodal_parts.append(PyInlineDataContentPart(
                        type="inline_data_content", mimeType=mime_type, base64Data=base64_data
                    ))
                elif mime_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                    logger.info(f"{log_prefix}: Extracting text from DOCX file for Gemini: {filename}")
                    await uploaded_file.seek(0)
                    file_bytes = await uploaded_file.read()
                    try:
                        doc_stream = io.BytesIO(file_bytes)
                        document = docx.Document(doc_stream)
                        full_text = "\n".join([para.text for para in document.paragraphs])
                        
                        extracted_text_content = f"\n\n--- START OF DOCUMENT: {filename} ---\n\n{full_text}\n\n--- END OF DOCUMENT: {filename} ---\n"
                        
                        newly_created_multimodal_parts.append(PyTextContentPart(
                            type="text_content", text=extracted_text_content
                        ))
                    except Exception as docx_e:
                        logger.error(f"{log_prefix}: Failed to extract text from DOCX file {filename}: {docx_e}", exc_info=True)

                elif mime_type in AUDIO_MIME_TYPES:
                    logger.info(f"{log_prefix}: Processing audio for Gemini: {filename} ({mime_type})")
                    await uploaded_file.seek(0)
                    file_bytes = await uploaded_file.read()
                    base64_data = base64.b64encode(file_bytes).decode('utf-8')
                    newly_created_multimodal_parts.append(PyInlineDataContentPart(
                        type="inline_data_content", mimeType=mime_type, base64Data=base64_data
                    ))
                elif mime_type in VIDEO_MIME_TYPES:
                    logger.info(f"{log_prefix}: Processing video for Gemini: {filename} ({mime_type})")
                    await uploaded_file.seek(0)
                    file_bytes = await uploaded_file.read()
                    file_size = len(file_bytes)
                    
                    # Use File API for large files as recommended by Google
                    if file_size > (MAX_DOCUMENT_UPLOAD_SIZE_MB * 1024 * 1024):
                        logger.info(f"{log_prefix}: Uploading large video '{filename}' ({file_size / 1024 / 1024:.2f} MB) to Gemini File API.")
                        try:
                            # We need to run this in a separate thread as the SDK is synchronous
                            loop = asyncio.get_running_loop()
                            video_file = await loop.run_in_executor(
                                None,
                                lambda: genai.upload_file(
                                    path=io.BytesIO(file_bytes),
                                    display_name=filename,
                                    mime_type=mime_type
                                )
                            )
                            logger.info(f"{log_prefix}: Uploaded '{filename}', waiting for processing. URI: {video_file.uri}")
                            
                            # Wait for the file to be processed
                            while video_file.state.name == "PROCESSING":
                                await asyncio.sleep(5) # Non-blocking sleep
                                video_file = await loop.run_in_executor(None, lambda: genai.get_file(video_file.name))
                                logger.info(f"{log_prefix}: File '{filename}' state: {video_file.state.name}")

                            if video_file.state.name == "ACTIVE":
                                newly_created_multimodal_parts.append(PyFileUriContentPart(
                                    type="file_uri_content", fileUri=video_file.uri, mimeType=mime_type
                                ))
                                logger.info(f"{log_prefix}: File '{filename}' is active and ready to use.")
                            else:
                                logger.error(f"{log_prefix}: File '{filename}' failed to process. State: {video_file.state.name}")

                        except Exception as file_api_e:
                            logger.error(f"{log_prefix}: Gemini File API upload failed for {filename}: {file_api_e}", exc_info=True)
                    else:
                        base64_data = base64.b64encode(file_bytes).decode('utf-8')
                        newly_created_multimodal_parts.append(PyInlineDataContentPart(
                            type="inline_data_content", mimeType=mime_type, base64Data=base64_data
                        ))
                else:
                    logger.warning(f"{log_prefix}: Skipping unsupported file type for Gemini: {filename} ({mime_type})")
            except Exception as e:
                logger.error(f"{log_prefix}: Error processing file {filename} for Gemini: {e}", exc_info=True)

    if newly_created_multimodal_parts:
        last_user_message_idx = -1
        for i in range(len(active_messages_for_llm) - 1, -1, -1):
            if active_messages_for_llm[i].role == "user":
                last_user_message_idx = i
                break
        
        if last_user_message_idx != -1:
            user_msg = active_messages_for_llm[last_user_message_idx]
            if isinstance(user_msg, PartsApiMessagePy):
                user_msg.parts.extend(newly_created_multimodal_parts)
            elif isinstance(user_msg, SimpleTextApiMessagePy):
                initial_text_part = [PyTextContentPart(type="text_content", text=user_msg.content)] if user_msg.content else []
                active_messages_for_llm[last_user_message_idx] = PartsApiMessagePy(
                    role="user", parts=initial_text_part + newly_created_multimodal_parts
                )
        else:
            active_messages_for_llm.append(PartsApiMessagePy(role="user", parts=newly_created_multimodal_parts))

    try:
        target_url, headers, json_payload = prepare_gemini_rest_api_request(
            chat_input=gemini_chat_input.model_copy(update={'messages': active_messages_for_llm}),
            request_id=request_id
        )
    except Exception as prep_error:
        logger.error(f"{log_prefix}: Request preparation error: {prep_error}", exc_info=True)
        async def error_gen():
            yield await sse_event_serializer_rest(AppStreamEventPy(type="error", message=f"请求准备错误: {str(prep_error)}", timestamp=get_current_time_iso()))
            yield await sse_event_serializer_rest(AppStreamEventPy(type="finish", reason="request_error", timestamp=get_current_time_iso()))
        return StreamingResponse(error_gen(), media_type="text/event-stream")

    async def stream_generator():
        processing_state = {}
        upstream_ok = False
        first_chunk_received = False
        full_text = ""
        original_full_text = ""  # Store original text for comparison
        grounding_supports = []
        grounding_chunks_storage = []
        
        try:
            async with http_client.stream("POST", target_url, headers=headers, json=json_payload, timeout=API_TIMEOUT) as response:
                upstream_ok = response.is_success
                if not upstream_ok:
                    error_body = await response.aread()
                    error_text = error_body.decode(errors='ignore')
                    logger.error(f"{log_prefix}: Gemini upstream error {response.status_code}: {error_text}")
                    
                    # 提供友好的错误信息
                    if response.status_code == 400:
                        error_message = f"Gemini API请求错误 (400): 请检查模型名称和参数是否正确"
                    elif response.status_code == 401:
                        error_message = f"Gemini API密钥无效 (401): 请检查您的API密钥是否正确"
                    elif response.status_code == 403:
                        error_message = f"Gemini API访问被拒绝 (403): 请检查API密钥权限或配额"
                    elif response.status_code == 404:
                        error_message = f"Gemini API端点未找到 (404): 请检查模型名称是否正确"
                    elif response.status_code == 429:
                        error_message = f"Gemini API请求频率过高 (429): 请稍后重试"
                    elif response.status_code >= 500:
                        error_message = f"Gemini服务器内部错误 ({response.status_code}): 请稍后重试"
                    else:
                        error_message = f"Gemini API错误 ({response.status_code}): {error_text[:200]}"
                    
                    # 发送友好的错误信息给用户
                    yield await sse_event_serializer_rest(AppStreamEventPy(
                        type="error",
                        message=error_message,
                        timestamp=get_current_time_iso()
                    ))
                    yield await sse_event_serializer_rest(AppStreamEventPy(
                        type="finish",
                        reason="api_error",
                        timestamp=get_current_time_iso()
                    ))
                    return

                async for line in response.aiter_lines():
                    if not first_chunk_received:
                        first_chunk_received = True
                    
                    if line.startswith("data:"):
                        json_str = line[len("data:"):].strip()
                        try:
                            logger.debug(f"Received from Gemini: {json_str}")
                            sse_data = orjson.loads(json_str)
                            openai_like_sse = {"id": f"gemini-{request_id}", "choices": []}
                            
                            for candidate in sse_data.get("candidates", []):
                                grounding_metadata = candidate.get("groundingMetadata")
                                if grounding_metadata:
                                    search_queries = grounding_metadata.get("webSearchQueries", [])
                                    if "groundingChunks" in grounding_metadata:
                                        grounding_chunks_storage.extend(grounding_metadata["groundingChunks"])
                                    
                                    if "groundingSupports" in grounding_metadata:
                                        grounding_supports.extend(grounding_metadata["groundingSupports"])

                                    if search_queries:
                                        logger.info(f"{log_prefix}: Gemini used web search with queries: {search_queries}")

                                    if grounding_chunks_storage:
                                        web_results = [
                                            WebSearchResult(
                                                title=chunk.get("web", {}).get("title", "Unknown Source"),
                                                url=chunk.get("web", {}).get("uri", "#"),
                                                snippet=f"Source: {chunk.get('web', {}).get('title', 'N/A')}"
                                            )
                                            for chunk in grounding_chunks_storage if chunk.get("web")
                                        ]
                                        if web_results:
                                            yield await sse_event_serializer_rest(AppStreamEventPy(
                                                type="web_search_results",
                                                web_search_results=web_results
                                            ))

                                content_parts = candidate.get("content", {}).get("parts", [])
                                is_thought_part = any("thought" in part for part in content_parts)

                                if is_thought_part:
                                    for part in content_parts:
                                        if "thought" in part and part.get("text"):
                                            reasoning_text = part["text"]
                                            yield await sse_event_serializer_rest(AppStreamEventPy(type="reasoning", text=reasoning_text))
                                else:
                                    delta = {}
                                    for part in content_parts:
                                        if "text" in part:
                                            text_chunk = part["text"]
                                            logger.debug(f"{log_prefix}: Processing text chunk of length {len(text_chunk)}")
                                            logger.debug(f"{log_prefix}: Text chunk preview: {repr(text_chunk[:100])}")
                                            
                                            # Store original text chunk for full_text accumulation
                                            original_full_text += text_chunk
                                            
                                            # Apply cleanup to the chunk for immediate streaming
                                            cleaned_chunk = process_content_for_model(text_chunk, request_id, "gemini")
                                            
                                            # 检查清理是否产生了变化
                                            if cleaned_chunk != text_chunk:
                                                logger.info(f"{log_prefix}: Text chunk was modified during cleanup")
                                                logger.debug(f"{log_prefix}: Original chunk: {repr(text_chunk)}")
                                                logger.debug(f"{log_prefix}: Cleaned chunk: {repr(cleaned_chunk)}")
                                            
                                            full_text += cleaned_chunk
                                            delta["content"] = cleaned_chunk
                                    
                                    if delta:
                                        logger.debug(f"{log_prefix}: Streaming delta with content length: {len(delta.get('content', ''))}")
                                        choice = {
                                            "delta": delta,
                                            "finish_reason": candidate.get("finishReason")
                                        }
                                        openai_like_sse = {"id": f"gemini-{request_id}", "choices": [choice]}
                                        async for event in process_openai_like_sse_stream(openai_like_sse, processing_state, request_id):
                                            yield await sse_event_serializer_rest(AppStreamEventPy(**event))

                        except orjson.JSONDecodeError:
                            logger.warning(f"{log_prefix}: Skipping non-JSON line in Gemini stream: {line}")

        except Exception as e:
            logger.error(f"{log_prefix}: An error occurred during the Gemini stream: {e}", exc_info=True)
            async for error_event in handle_stream_error(e, request_id, upstream_ok, first_chunk_received):
                yield error_event
        finally:
            # Apply final cleanup to the complete text for better markdown and math formula processing
            if original_full_text:
                logger.info(f"{log_prefix}: Starting final cleanup process")
                logger.info(f"{log_prefix}: Original full text length: {len(original_full_text)}")
                logger.info(f"{log_prefix}: Streamed full text length: {len(full_text)}")
                
                # 检查是否是联网搜索结果（通常包含更多换行符和格式化问题）
                newline_count = original_full_text.count('\n')
                if newline_count > 20:
                    logger.info(f"{log_prefix}: High newline count detected ({newline_count}), likely web search result")
                
                final_cleaned_text = process_content_for_model(original_full_text, request_id, "gemini")
                
                # Check if the final cleaned text is significantly different from the streamed version
                streamed_stripped = full_text.strip()
                final_stripped = final_cleaned_text.strip()
                
                logger.info(f"{log_prefix}: Final cleaned text length: {len(final_cleaned_text)}")
                
                if final_stripped != streamed_stripped:
                    length_diff = len(final_stripped) - len(streamed_stripped)
                    logger.info(f"{log_prefix}: Final cleanup produced different result (length diff: {length_diff})")
                    logger.info(f"{log_prefix}: Sending __GEMINI_FINAL_CLEANUP__ event")
                    
                    # 对比关键差异
                    streamed_lines = streamed_stripped.count('\n')
                    final_lines = final_stripped.count('\n')
                    logger.info(f"{log_prefix}: Line count difference: streamed={streamed_lines}, final={final_lines}")
                    
                    # Send the properly cleaned complete text as a content event
                    yield await sse_event_serializer_rest(AppStreamEventPy(
                        type="content",
                        text=f"__GEMINI_FINAL_CLEANUP__\n{final_cleaned_text}",
                        timestamp=get_current_time_iso()
                    ))
                else:
                    logger.debug(f"{log_prefix}: Final cleanup produced identical result, no correction needed")
            
            is_native_thinking = bool(gemini_chat_input.generation_config and gemini_chat_input.generation_config.thinking_config)
            use_custom_sep = should_apply_custom_separator_logic(gemini_chat_input, request_id, is_google_like_path=True, is_native_thinking_active=is_native_thinking)
            if grounding_supports and grounding_chunks_storage:
                logger.info(f"Processing citations for full text. Supports: {len(grounding_supports)}, Chunks: {len(grounding_chunks_storage)}")
                
                sorted_supports = sorted(grounding_supports, key=lambda s: s.get("segment", {}).get("endIndex", 0), reverse=True)

                for support in sorted_supports:
                    segment = support.get("segment", {})
                    end_index = segment.get("endIndex")
                    if end_index is None:
                        continue

                    chunk_indices = support.get("groundingChunkIndices", [])
                    if chunk_indices:
                        citation_links = []
                        for i in chunk_indices:
                            if i < len(grounding_chunks_storage):
                                uri = grounding_chunks_storage[i].get("web", {}).get("uri")
                                if uri:
                                    citation_links.append(f"[{i + 1}]({uri})")

                        if citation_links:
                            citation_string = " " + ", ".join(citation_links)
                            final_cleaned_text = final_cleaned_text[:end_index] + citation_string + final_cleaned_text[end_index:]
                
                # Since we cannot re-stream, we will log the result for now.
                logger.info(f"Text with citations: {final_cleaned_text}")


            async for final_event in handle_stream_cleanup(processing_state, request_id, upstream_ok, use_custom_sep, gemini_chat_input.provider):
                yield final_event

    return StreamingResponse(stream_generator(), media_type="text/event-stream")