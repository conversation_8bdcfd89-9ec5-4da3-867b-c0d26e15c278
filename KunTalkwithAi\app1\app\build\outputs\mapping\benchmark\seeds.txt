com.example.everytalk.data.DataClass.Content$Companion
androidx.profileinstaller.ProfileInstallerInitializer
androidx.compose.foundation.layout.PaddingValuesElement
androidx.compose.ui.input.pointer.SuspendPointerInputElement
androidx.compose.foundation.ClickableElement
androidx.navigation.compose.ComposeNavigator
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion
androidx.compose.ui.draw.DrawWithContentElement
androidx.compose.ui.input.pointer.StylusHoverIconModifierElement
androidx.compose.foundation.gestures.ScrollableElement
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer
androidx.compose.foundation.layout.LayoutWeightElement
androidx.core.app.RemoteActionCompat
com.example.everytalk.data.DataClass.Message
com.example.everytalk.data.DataClass.Sender$Companion
androidx.compose.material3.MinimumInteractiveModifier
com.example.everytalk.data.DataClass.PromptFeedback$Companion
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer
coil3.compose.internal.ContentPainterElement
androidx.navigation.compose.ComposeNavGraphNavigator
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer
androidx.compose.foundation.IndicationModifierElement
okhttp3.internal.publicsuffix.PublicSuffixDatabase
androidx.emoji2.text.EmojiCompatInitializer
com.example.everytalk.data.DataClass.ContentPart$Audio
androidx.graphics.path.PathIteratorPreApi34Impl
com.google.gson.reflect.TypeToken
androidx.compose.animation.SizeAnimationModifierElement
com.example.everytalk.data.DataClass.Candidate$$serializer
androidx.core.app.RemoteActionCompatParcelizer
com.example.everytalk.data.DataClass.GenerationConfig$Companion
androidx.compose.foundation.layout.VerticalAlignElement
androidx.annotation.Keep
androidx.navigation.NavBackStackEntry$SavedStateViewModel
com.example.everytalk.data.DataClass.PartsApiMessage
androidx.compose.ui.input.key.KeyInputElement
com.example.everytalk.data.DataClass.GithubRelease
androidx.compose.ui.input.pointer.PointerInputEventHandler
androidx.compose.material3.internal.DraggableAnchorsElement
com.example.everytalk.data.DataClass.SafetySetting
com.example.everytalk.data.DataClass.Content
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion
androidx.compose.foundation.FocusableElement
com.example.everytalk.data.DataClass.Part$InlineData$$serializer
com.example.everytalk.data.DataClass.Part$InlineData
androidx.compose.animation.AnimatedContentTransitionScopeImpl$SizeModifierElement
io.ktor.client.engine.android.AndroidEngineContainer
com.example.everytalk.data.DataClass.GenerationConfig
com.example.everytalk.data.DataClass.Content$$serializer
com.example.everytalk.data.DataClass.IMessage
com.example.everytalk.data.DataClass.ApiConfig$$serializer
androidx.compose.foundation.ScrollingLayoutElement
androidx.compose.foundation.relocation.BringIntoViewRequesterElement
com.example.everytalk.data.DataClass.GeminiApiResponse
com.example.everytalk.data.DataClass.ContentPart
androidx.lifecycle.ProcessLifecycleInitializer
com.example.everytalk.data.DataClass.SafetySetting$Companion
com.example.everytalk.data.DataClass.GithubRelease$Companion
androidx.compose.ui.graphics.GraphicsLayerElement
com.example.everytalk.data.DataClass.Sender
com.example.everytalk.data.DataClass.AbstractApiMessage
androidx.navigation.NavControllerViewModel
androidx.compose.ui.draw.ShadowGraphicsLayerElement
androidx.compose.foundation.BackgroundElement
androidx.compose.foundation.layout.PaddingElement
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion
com.example.everytalk.data.DataClass.Part$FileUri$$serializer
androidx.profileinstaller.ProfileInstallReceiver
androidx.compose.foundation.BorderModifierNodeElement
com.example.everytalk.data.DataClass.PromptFeedback
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer
com.example.everytalk.data.DataClass.ApiContentPart$Companion
androidx.compose.foundation.lazy.layout.LazyLayoutBeyondBoundsModifierElement
androidx.compose.ui.semantics.AppendedSemanticsElement
androidx.compose.ui.layout.LayoutIdElement
com.example.everytalk.data.DataClass.ContentPart$Code
androidx.lifecycle.SavedStateHandlesVM
androidx.lifecycle.ProcessLifecycleOwner$attach$1
com.example.everytalk.data.DataClass.WebSearchResult
com.example.everytalk.statecontroller.MainActivity
com.example.everytalk.data.DataClass.SimpleTextApiMessage
kotlin.coroutines.jvm.internal.BaseContinuationImpl
androidx.compose.foundation.CombinedClickableElement
androidx.compose.foundation.layout.BoxChildDataElement
androidx.compose.foundation.MagnifierElement
com.example.everytalk.data.DataClass.ChatRequest$$serializer
com.example.everytalk.data.DataClass.SafetyRating$Companion
com.example.everytalk.data.DataClass.WebSearchResult$Companion
androidx.compose.foundation.gestures.DraggableElement
com.example.everytalk.data.DataClass.GithubRelease$$serializer
androidx.compose.foundation.layout.IntrinsicWidthElement
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer
androidx.compose.foundation.text.modifiers.SelectableTextAnnotatedStringElement
com.example.everytalk.data.DataClass.Message$Companion
androidx.compose.ui.graphics.BlockGraphicsLayerElement
com.example.everytalk.data.DataClass.ChatRequest
androidx.compose.foundation.lazy.layout.LazyLayoutSemanticsModifier
com.example.everytalk.data.DataClass.Part$Text$Companion
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion
androidx.compose.ui.focus.FocusRequesterElement
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer
com.example.everytalk.data.DataClass.Part$Companion
androidx.compose.ui.platform.AndroidComposeView$bringIntoViewNode$1
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation
androidx.compose.foundation.layout.WrapContentElement
androidx.compose.foundation.layout.HorizontalAlignElement
androidx.compose.foundation.layout.OffsetElement
com.example.everytalk.data.DataClass.GenerationConfig$$serializer
androidx.compose.foundation.text.modifiers.TextStringSimpleElement
androidx.compose.ui.input.pointer.PointerHoverIconModifierElement
com.example.everytalk.data.DataClass.Part$Text
androidx.compose.foundation.ScrollingContainerElement
com.example.everytalk.data.DataClass.ApiContentPart$InlineData
androidx.compose.foundation.lazy.layout.TraversablePrefetchStateModifierElement
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings
androidx.compose.foundation.HoverableElement
com.example.everytalk.data.DataClass.Message$$serializer
androidx.compose.foundation.layout.UnspecifiedConstraintsElement
androidx.compose.ui.draw.PainterElement
androidx.lifecycle.ReportFragment
com.example.everytalk.data.DataClass.ModalityType
androidx.compose.ui.viewinterop.FocusGroupPropertiesElement
com.example.everytalk.data.DataClass.Candidate
androidx.compose.ui.layout.LayoutElement
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback
androidx.compose.ui.focus.FocusOwnerImpl$modifier$1
com.example.everytalk.data.DataClass.ApiContentPart$Text
androidx.core.graphics.drawable.IconCompat
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion
androidx.compose.foundation.lazy.layout.LazyLayoutItemAnimator$DisplayingDisappearingItemsElement
androidx.compose.ui.focus.FocusTargetNode$FocusTargetElement
androidx.versionedparcelable.ParcelImpl
com.example.everytalk.data.DataClass.Part$FileUri
com.example.everytalk.data.DataClass.Candidate$Companion
com.example.everytalk.data.DataClass.MessageKt
androidx.compose.foundation.text.input.internal.CoreTextFieldSemanticsModifier
com.example.everytalk.data.DataClass.ApiContentPart$FileUri
com.example.everytalk.statecontroller.LRUCache
com.example.everytalk.data.DataClass.ChatRequest$Companion
androidx.compose.ui.input.nestedscroll.NestedScrollElement
androidx.compose.ui.input.rotary.RotaryInputElement
androidx.compose.animation.EnterExitTransitionElement
androidx.lifecycle.ReportFragment$LifecycleCallbacks
androidx.compose.foundation.text.input.internal.LegacyAdaptingPlatformTextInputModifier
androidx.compose.ui.draganddrop.AndroidDragAndDropManager$modifier$1
androidx.compose.ui.layout.OnGloballyPositionedElement
android.support.v4.graphics.drawable.IconCompatParcelizer
com.example.everytalk.data.DataClass.Part
androidx.core.app.CoreComponentFactory
kotlinx.coroutines.internal.StackTraceRecoveryKt
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion
com.example.everytalk.data.DataClass.ApiConfig$Companion
androidx.compose.ui.semantics.ClearAndSetSemanticsElement
com.example.everytalk.data.DataClass.Part$Text$$serializer
com.example.everytalk.data.DataClass.ContentPart$Html
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer
com.example.everytalk.data.DataClass.Part$FileUri$Companion
androidx.compose.ui.focus.FocusChangedElement
kotlinx.coroutines.android.AndroidDispatcherFactory
com.example.everytalk.data.DataClass.PromptFeedback$$serializer
androidx.versionedparcelable.CustomVersionedParcelable
androidx.core.content.FileProvider
com.example.everytalk.data.DataClass.GeminiApiRequest
androidx.compose.foundation.layout.FillElement
com.example.everytalk.data.DataClass.WebSearchResult$$serializer
com.example.everytalk.data.DataClass.ApiConfig
androidx.startup.InitializationProvider
androidx.compose.foundation.text.handwriting.StylusHandwritingElement
com.example.everytalk.data.DataClass.SafetySetting$$serializer
androidx.compose.ui.platform.AndroidCompositionLocals_androidKt
androidx.navigation.NavGraphNavigator
androidx.compose.foundation.layout.SizeElement
androidx.navigation.compose.DialogNavigator
android.support.v4.app.RemoteActionCompatParcelizer
androidx.navigation.ActivityNavigator
com.example.everytalk.data.DataClass.ThinkingConfig
androidx.navigation.compose.BackStackEntryIdViewModel
androidx.graphics.path.ConicConverter
com.example.everytalk.data.DataClass.SafetyRating
androidx.compose.ui.draw.DrawBehindElement
androidx.compose.foundation.text.modifiers.TextAnnotatedStringElement
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion
androidx.compose.foundation.layout.AlignmentLineOffsetDpElement
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1
com.example.everytalk.data.DataClass.PartsApiMessage$Companion
com.example.everytalk.data.DataClass.ThinkingConfig$Companion
com.example.everytalk.data.DataClass.ModalityType$Companion
com.example.everytalk.data.DataClass.SafetyRating$$serializer
com.example.everytalk.data.DataClass.Part$InlineData$Companion
androidx.compose.ui.draw.DrawWithCacheElement
androidx.compose.ui.layout.OnSizeChangedModifier
com.example.everytalk.data.DataClass.MessageKt$WhenMappings
androidx.compose.ui.semantics.EmptySemanticsElement
androidx.compose.ui.viewinterop.FocusTargetPropertiesElement
androidx.core.graphics.drawable.IconCompatParcelizer
com.example.everytalk.data.DataClass.ApiContentPart
com.example.everytalk.data.DataClass.ApiConfig: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: com.example.everytalk.data.DataClass.PartsApiMessage$$serializer INSTANCE
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle$volatile
com.example.everytalk.data.DataClass.Content: int $stable
com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Message$$serializer: com.example.everytalk.data.DataClass.Message$$serializer INSTANCE
com.example.everytalk.data.network.AppStreamEvent$ToolCall$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String provider
io.ktor.client.HttpClient: int closed
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String model
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ApiConfig$Companion Companion
io.ktor.utils.io.ByteBufferChannel: long totalBytesRead
com.example.everytalk.data.DataClass.Message: long timestamp
com.example.everytalk.data.DataClass.ApiContentPart$Text: int $stable
com.example.everytalk.data.DataClass.ApiConfig$$serializer: com.example.everytalk.data.DataClass.ApiConfig$$serializer INSTANCE
com.example.everytalk.data.DataClass.ModalityType: java.lang.String displayName
com.example.everytalk.data.DataClass.Message: java.lang.String name
androidx.navigation.NavBackStackEntryState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.Part$Text: int $stable
kotlinx.coroutines.CompletedExceptionally: int _handled$volatile
com.example.everytalk.data.DataClass.Message$$serializer: int $stable
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String base64Data
kotlin.SafePublicationLazyImpl: java.lang.Object _value
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ModalityType modalityType
io.ktor.utils.io.pool.DefaultPool: long top
com.example.everytalk.data.network.AppStreamEvent$StatusUpdate: com.example.everytalk.data.network.AppStreamEvent$StatusUpdate$Companion Companion
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.ChatRequest$Companion Companion
com.example.everytalk.models.SelectedMediaItem$GenericFile: com.example.everytalk.models.SelectedMediaItem$GenericFile$Companion Companion
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: com.example.everytalk.data.DataClass.Part$InlineData$$serializer INSTANCE
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: com.example.everytalk.data.DataClass.Part$FileUri$$serializer INSTANCE
kotlinx.coroutines.EventLoopImplBase$DelayedTask: java.lang.Object _heap
com.example.everytalk.data.DataClass.SafetySetting: int $stable
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
com.example.everytalk.data.DataClass.ChatRequest: java.util.List tools
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed$volatile
com.example.everytalk.data.DataClass.ApiConfig$$serializer: int $stable
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String contentId
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType MULTIMODAL
com.example.everytalk.data.network.AppStreamEvent$Content$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.GeminiApiResponse: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.Message: int $stable
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String title
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: int $stable
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer INSTANCE
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: kotlinx.serialization.KSerializer[] $childSerializers
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd$volatile
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String category
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: int $stable
kotlinx.coroutines.internal.Segment: int cleanedAndPointers$volatile
com.example.everytalk.models.SelectedMediaItem$Audio$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause$volatile
io.ktor.utils.io.core.internal.ChunkBuffer: int refCount
com.example.everytalk.data.DataClass.GeminiApiRequest: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Part: com.example.everytalk.data.DataClass.Part$Companion Companion
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer numInferenceSteps
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Part$InlineData: int $stable
kotlinx.coroutines.JobSupport: java.lang.Object _state$volatile
com.example.everytalk.data.DataClass.Message: boolean isPlaceholderName
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String href
com.example.everytalk.data.DataClass.Message: boolean isError
com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus: com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus$Companion Companion
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur$volatile
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state$volatile
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType VIDEO
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean forceGoogleReasoningPrompt
com.example.everytalk.data.DataClass.Candidate: int index
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: int $stable
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: long enqIdx$volatile
io.ktor.client.engine.HttpClientEngineBase: int closed
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String address
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String htmlUrl
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: int _availablePermits$volatile
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Message: boolean contentStarted
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: int $stable
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation$volatile
com.example.everytalk.data.DataClass.Part$InlineData: com.example.everytalk.data.DataClass.Part$InlineData$Companion Companion
com.example.everytalk.data.DataClass.Candidate: java.lang.String finishReason
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String id
com.example.everytalk.data.DataClass.AbstractApiMessage: int $stable
com.example.everytalk.data.DataClass.ContentPart: int $stable
com.example.everytalk.data.DataClass.Part: kotlin.Lazy $cachedSerializer$delegate
com.example.everytalk.data.DataClass.Message: java.lang.String currentWebSearchStage
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler$volatile
com.example.everytalk.statecontroller.LRUCache: int maxSize
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer$volatile
io.ktor.utils.io.pool.SingleInstancePool: java.lang.Object instance
kotlinx.coroutines.DefaultExecutor: int debugStatus
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String data
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next$volatile
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: int $stable
com.example.everytalk.data.DataClass.ApiContentPart: int $stable
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String key
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String apiAddress
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String name
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated$volatile
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting$volatile
io.ktor.utils.io.ByteBufferChannel: io.ktor.utils.io.internal.JoiningState joining
com.example.everytalk.data.network.AppStreamEvent$ToolCall: com.example.everytalk.data.network.AppStreamEvent$ToolCall$Companion Companion
kotlinx.serialization.json.JsonElement: kotlinx.serialization.json.JsonElement$Companion Companion
kotlinx.coroutines.ThreadState: int _state$volatile
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: com.example.everytalk.statecontroller.AppViewModel$ExportedSettings$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$Error: com.example.everytalk.data.network.AppStreamEvent$Error$Companion Companion
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String id
io.ktor.utils.io.pool.SingleInstancePool: int disposed
com.example.everytalk.data.DataClass.Candidate: kotlinx.serialization.KSerializer[] $childSerializers
androidx.activity.result.ActivityResult: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map customModelParameters
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers$volatile
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex$volatile
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Object toolChoice
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String mimeType
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.ThinkingConfig thinkingConfig
com.example.everytalk.data.DataClass.PromptFeedback: com.example.everytalk.data.DataClass.PromptFeedback$Companion Companion
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String provider
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment$volatile
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _state
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
androidx.compose.runtime.ParcelableSnapshotMutableIntState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state$volatile
coil3.compose.internal.DeferredDispatchCoroutineDispatcher: int _unconfined$volatile
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state$volatile
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender Tool
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Sender sender
com.example.everytalk.data.DataClass.ApiContentPart: kotlin.Lazy $cachedSerializer$delegate
com.example.everytalk.data.DataClass.PartsApiMessage: java.util.List parts
com.example.everytalk.data.DataClass.Sender: kotlin.enums.EnumEntries $ENTRIES
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType[] $VALUES
com.example.everytalk.data.DataClass.Sender: kotlin.Lazy $cachedSerializer$delegate
io.ktor.utils.io.internal.CancellableReusableContinuation: java.lang.Object jobCancellationHandler
com.example.everytalk.data.DataClass.Content$$serializer: int $stable
kotlinx.coroutines.DispatchedCoroutine: int _decision$volatile
com.example.everytalk.data.DataClass.ChatRequest: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.WebSearchResult: int $stable
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: long deqIdx$volatile
com.example.everytalk.data.DataClass.PromptFeedback: int $stable
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack$volatile
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: com.example.everytalk.data.DataClass.WebSearchResult$$serializer INSTANCE
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Message$Companion Companion
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GeminiApiRequest$Companion Companion
com.example.everytalk.data.DataClass.Message: java.lang.String text
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
io.ktor.util.pipeline.Pipeline: java.lang.Object _interceptors
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.List apiConfigs
androidx.compose.runtime.ParcelableSnapshotMutableFloatState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String threshold
com.example.everytalk.data.DataClass.SafetyRating: int $stable
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Boolean defaultUseWebSearch
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
com.example.everytalk.data.DataClass.Content: com.example.everytalk.data.DataClass.Content$Companion Companion
com.example.everytalk.data.DataClass.ChatRequest$$serializer: int $stable
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String model
com.example.everytalk.data.DataClass.GithubRelease: com.example.everytalk.data.DataClass.GithubRelease$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: int $stable
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: int $stable
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.GenerationConfig$Companion Companion
com.example.everytalk.data.DataClass.ChatRequest: int $stable
com.example.everytalk.data.DataClass.Content: java.lang.String role
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
com.example.everytalk.data.network.AppStreamEvent$WebSearchResults$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String name
androidx.compose.foundation.lazy.layout.DefaultLazyKey: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.ApiContentPart: com.example.everytalk.data.DataClass.ApiContentPart$Companion Companion
com.example.everytalk.data.DataClass.Part$FileUri: com.example.everytalk.data.DataClass.Part$FileUri$Companion Companion
com.example.everytalk.data.DataClass.Part$FileUri: int $stable
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.GenerationConfig generationConfig
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String id
io.ktor.utils.io.ByteBufferChannel: int writeSuspensionSize
com.example.everytalk.data.network.AppStreamEvent$Text: com.example.everytalk.data.network.AppStreamEvent$Text$Companion Companion
com.example.everytalk.data.DataClass.ChatRequest: java.util.List messages
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner$volatile
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
com.example.everytalk.data.DataClass.Part: int $stable
io.ktor.utils.io.ByteBufferChannel: kotlinx.coroutines.Job attachedJob
io.ktor.utils.io.pool.SingleInstancePool: int borrowed
com.example.everytalk.data.network.AppStreamEvent$WebSearchResults: com.example.everytalk.data.network.AppStreamEvent$WebSearchResults$Companion Companion
com.example.everytalk.models.SelectedMediaItem$ImageFromUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ModalityType: kotlin.enums.EnumEntries $ENTRIES
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String apiKey
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String text
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: int $stable
com.example.everytalk.data.DataClass.Content: java.util.List parts
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment$volatile
com.example.everytalk.data.DataClass.GithubRelease$$serializer: com.example.everytalk.data.DataClass.GithubRelease$$serializer INSTANCE
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String imageSize
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String content
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.WebSearchResult: com.example.everytalk.data.DataClass.WebSearchResult$Companion Companion
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String snippet
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next$volatile
kotlinx.serialization.json.JsonObject: kotlinx.serialization.json.JsonObject$Companion Companion
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float temperature
com.example.everytalk.data.network.AppStreamEvent: com.example.everytalk.data.network.AppStreamEvent$Companion Companion
io.ktor.utils.io.internal.RingBufferCapacity: int _pendingToFlush
com.example.everytalk.data.DataClass.Part$Text: java.lang.String text
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean qwenEnableSearch
com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse: com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse$Companion Companion
com.example.everytalk.data.DataClass.Candidate: int $stable
com.example.everytalk.data.DataClass.ChatRequest$$serializer: com.example.everytalk.data.DataClass.ChatRequest$$serializer INSTANCE
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl$volatile
com.example.everytalk.data.DataClass.GeminiApiRequest: int $stable
com.example.everytalk.data.network.AppStreamEvent$Finish$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.InvokeOnCancelling: int _invoked$volatile
com.example.everytalk.data.network.AppStreamEvent$StatusUpdate$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
androidx.lifecycle.Lifecycle$Event: kotlin.enums.EnumEntries $ENTRIES
com.example.everytalk.data.network.AppStreamEvent$Content: com.example.everytalk.data.network.AppStreamEvent$Content$Companion Companion
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String code
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType IMAGE
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List contents
androidx.activity.result.IntentSenderRequest: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: com.example.everytalk.data.DataClass.GenerationConfig$$serializer INSTANCE
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _closed
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float topP
com.example.everytalk.data.DataClass.Candidate$$serializer: com.example.everytalk.data.DataClass.Candidate$$serializer INSTANCE
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiConfig: boolean isValid
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender AI
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
com.example.everytalk.data.network.ApiClient$FileMetadata: com.example.everytalk.data.network.ApiClient$FileMetadata$Companion Companion
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GenerationConfig generationConfig
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Candidate$Companion Companion
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer INSTANCE
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType TEXT
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer maxTokens
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: java.lang.Object head$volatile
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment$volatile
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause$volatile
com.example.everytalk.data.DataClass.Part$Text$$serializer: com.example.everytalk.data.DataClass.Part$Text$$serializer INSTANCE
com.example.everytalk.data.DataClass.PromptFeedback: java.util.List safetyRatings
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ContentPart$Audio: int $stable
kotlinx.serialization.json.JsonPrimitive: kotlinx.serialization.json.JsonPrimitive$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$Text: com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$Finish: com.example.everytalk.data.network.AppStreamEvent$Finish$Companion Companion
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender User
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
com.example.everytalk.models.SelectedMediaItem: com.example.everytalk.models.SelectedMediaItem$Companion Companion
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String mimeType
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder$volatile
com.example.everytalk.data.DataClass.Message: java.util.List imageUrls
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef$volatile
com.example.everytalk.data.DataClass.ContentPart$Code: int $stable
kotlin.coroutines.SafeContinuation: java.lang.Object result
kotlinx.serialization.json.JsonArray: kotlinx.serialization.json.JsonArray$Companion Companion
androidx.compose.runtime.ParcelableSnapshotMutableState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.SafetySetting$$serializer: com.example.everytalk.data.DataClass.SafetySetting$$serializer INSTANCE
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState$volatile
kotlinx.coroutines.internal.ThreadSafeHeap: int _size$volatile
com.example.everytalk.data.DataClass.SimpleTextApiMessage: com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion Companion
com.example.everytalk.data.DataClass.Candidate$$serializer: int $stable
io.ktor.client.call.HttpClientCall: int received
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: int $stable
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Integer maxOutputTokens
com.example.everytalk.data.DataClass.SafetyRating$$serializer: com.example.everytalk.data.DataClass.SafetyRating$$serializer INSTANCE
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex$volatile
kotlinx.coroutines.EventLoopImplBase: int _isCompleted$volatile
io.ktor.utils.io.core.internal.ChunkBuffer: java.lang.Object nextRef
androidx.compose.runtime.ParcelableSnapshotMutableLongState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.models.SelectedMediaItem$GenericFile$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: com.example.everytalk.data.DataClass.PromptFeedback$$serializer INSTANCE
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex$volatile
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue$volatile
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: int $stable
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: int $stable
com.example.everytalk.data.DataClass.SafetyRating$$serializer: int $stable
com.example.everytalk.data.DataClass.Message: java.util.List attachments
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag$volatile
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: int $stable
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: com.example.everytalk.data.DataClass.ThinkingConfig$$serializer INSTANCE
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String markdownWithKatex
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String category
com.example.everytalk.data.DataClass.SafetySetting$$serializer: int $stable
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String language
io.ktor.util.collections.CopyOnWriteHashMap: java.lang.Object current
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$Reasoning: com.example.everytalk.data.network.AppStreamEvent$Reasoning$Companion Companion
com.example.everytalk.data.DataClass.Candidate: java.util.List safetyRatings
com.example.everytalk.data.DataClass.GeminiApiResponse: java.util.List candidates
kotlinx.coroutines.CancelledContinuation: int _resumed$volatile
com.example.everytalk.data.network.ApiClient$FileMetadata$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Message: java.lang.String reasoning
io.ktor.utils.io.internal.RingBufferCapacity: int _availableForRead$internal
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType AUDIO
com.example.everytalk.statecontroller.LRUCache: int $stable
com.example.everytalk.data.DataClass.MessageKt$WhenMappings: int[] $EnumSwitchMapping$0
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String probability
com.example.everytalk.data.DataClass.SimpleTextApiMessage: int $stable
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map customExtraBody
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev$volatile
com.example.everytalk.models.SelectedMediaItem$ImageFromUri: com.example.everytalk.models.SelectedMediaItem$ImageFromUri$Companion Companion
com.example.everytalk.data.DataClass.ThinkingConfig: int $stable
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String role
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ContentPart: java.lang.String contentId
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: int $stable
com.example.everytalk.data.network.AppStreamEvent$StreamEnd: com.example.everytalk.data.network.AppStreamEvent$StreamEnd$Companion Companion
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer INSTANCE
com.example.everytalk.data.network.AppStreamEvent$Reasoning$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Content content
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev$volatile
coil3.RealImageLoader: int shutdown$volatile
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion Companion
kotlinx.coroutines.channels.BufferedChannel: long receivers$volatile
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float topP
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus$volatile
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String contentId
com.example.everytalk.data.DataClass.AbstractApiMessage: com.example.everytalk.data.DataClass.AbstractApiMessage$Companion Companion
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer INSTANCE
com.example.everytalk.data.DataClass.PartsApiMessage: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.PromptFeedback promptFeedback
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
com.example.everytalk.data.network.AppStreamEvent$StreamEnd$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.AbstractApiMessage: kotlin.Lazy $cachedSerializer$delegate
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List safetySettings
com.example.everytalk.data.DataClass.GithubRelease: int $stable
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String uri
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String mimeType
com.example.everytalk.data.network.AppStreamEvent$Error$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.flow.ChannelAsFlow: int consumed$volatile
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.GithubRelease$$serializer: int $stable
com.example.everytalk.data.DataClass.GenerationConfig: int $stable
com.example.everytalk.data.DataClass.Content: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.GeminiApiResponse: int $stable
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Integer thinkingBudget
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.Set customProviders
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask$volatile
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float guidanceScale
com.example.everytalk.data.DataClass.PromptFeedback: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean useWebSearch
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Boolean includeThoughts
io.ktor.utils.io.jvm.javaio.BlockingAdapter: java.lang.Object state
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String mimeType
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: java.lang.Object tail$volatile
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.GeminiApiResponse$Companion Companion
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String data
com.example.everytalk.data.network.AppStreamEvent$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer INSTANCE
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender System
com.example.everytalk.data.DataClass.Message: java.lang.String id
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
com.example.everytalk.data.DataClass.WebSearchResult: int index
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle$volatile
com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap: com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap$Companion Companion
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String name
io.ktor.utils.io.ByteBufferChannel: long totalBytesWritten
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String body
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.ApiConfig: float temperature
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: int $stable
com.example.everytalk.data.DataClass.SafetySetting: com.example.everytalk.data.DataClass.SafetySetting$Companion Companion
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender$Companion Companion
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _writeOp
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType$Companion Companion
com.example.everytalk.data.DataClass.Part$Text: com.example.everytalk.data.DataClass.Part$Text$Companion Companion
com.example.everytalk.models.SelectedMediaItem$Audio: com.example.everytalk.models.SelectedMediaItem$Audio$Companion Companion
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next$volatile
io.ktor.utils.io.jvm.javaio.BlockingAdapter: int result
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String contentId
io.ktor.utils.io.internal.CancellableReusableContinuation: java.lang.Object state
com.example.everytalk.data.DataClass.Part$Text$$serializer: int $stable
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer INSTANCE
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
com.example.everytalk.data.DataClass.ContentPart$Html: int $stable
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String fileUri
com.example.everytalk.data.DataClass.Message: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.ThinkingConfig: com.example.everytalk.data.DataClass.ThinkingConfig$Companion Companion
com.example.everytalk.data.DataClass.PartsApiMessage: com.example.everytalk.data.DataClass.PartsApiMessage$Companion Companion
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
com.example.everytalk.data.DataClass.PartsApiMessage: int $stable
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String tagName
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String role
com.example.everytalk.data.DataClass.Content$$serializer: com.example.everytalk.data.DataClass.Content$$serializer INSTANCE
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Message: java.util.List webSearchResults
com.example.everytalk.data.DataClass.ApiConfig: int $stable
io.ktor.utils.io.internal.RingBufferCapacity: int _availableForWrite$internal
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _readOp
kotlinx.serialization.json.JsonNull: kotlinx.serialization.json.JsonNull INSTANCE
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: int $stable
com.example.everytalk.data.DataClass.SafetyRating: com.example.everytalk.data.DataClass.SafetyRating$Companion Companion
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender[] $VALUES
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig(java.lang.Boolean,java.lang.Integer,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.Message: long getTimestamp()
androidx.core.view.WindowInsetsCompat$Impl34: WindowInsetsCompat$Impl34(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.ModalityType$Companion: ModalityType$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.activity.EdgeToEdgeApi28: void adjustLayoutInDisplayCutoutMode(android.view.Window)
com.example.everytalk.data.DataClass.PartsApiMessage: java.util.List component3()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.unit.Density getDensity()
androidx.compose.ui.layout.IntrinsicWidthHeight: androidx.compose.ui.layout.IntrinsicWidthHeight valueOf(java.lang.String)
kotlinx.coroutines.flow.SharingCommand: kotlinx.coroutines.flow.SharingCommand valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest: void getToolChoice$annotations()
com.example.everytalk.data.DataClass.Part$Companion: Part$Companion()
com.example.everytalk.data.DataClass.GenerationConfig: int hashCode()
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.compose.ui.autofill.AutofillType: androidx.compose.ui.autofill.AutofillType valueOf(java.lang.String)
androidx.compose.foundation.text.selection.Direction: androidx.compose.foundation.text.selection.Direction valueOf(java.lang.String)
androidx.compose.ui.layout.IntrinsicMinMax: androidx.compose.ui.layout.IntrinsicMinMax[] values()
androidx.activity.EdgeToEdgeApi26: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.compose.foundation.text.HandleState: androidx.compose.foundation.text.HandleState[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map component12()
okhttp3.internal.http2.ErrorCode: okhttp3.internal.http2.ErrorCode valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.PartsApiMessage: PartsApiMessage(java.lang.String,java.lang.String,java.util.List,java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidClipboard getClipboard()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.navigation.NavControllerViewModel: NavControllerViewModel()
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Sender component3()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.foundation.layout.IntrinsicSize: androidx.compose.foundation.layout.IntrinsicSize valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease: void getTagName$annotations()
com.example.everytalk.data.DataClass.Content: Content(java.util.List,java.lang.String)
androidx.emoji2.text.ConcurrencyHelpers$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String getContentId()
com.example.everytalk.data.DataClass.Part$InlineData: int hashCode()
com.example.everytalk.data.DataClass.Message$Companion: Message$Companion()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float getTemperature()
com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getProvider()
androidx.compose.ui.graphics.layer.ViewLayer: boolean getCanUseCompositingLayer$ui_graphics_release()
androidx.compose.material.ripple.RippleHostView: void setRippleState$lambda$2(androidx.compose.material.ripple.RippleHostView)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.compose.ui.state.ToggleableState: androidx.compose.ui.state.ToggleableState[] values()
com.example.everytalk.data.DataClass.Content: java.util.List component1()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.runtime.ComposerKt: java.lang.Void composeRuntimeError(java.lang.String)
com.example.everytalk.data.DataClass.Part$Text$$serializer: com.example.everytalk.data.DataClass.Part$Text deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.statecontroller.LRUCache: java.util.Collection values()
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: com.example.everytalk.data.DataClass.SafetySetting deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus: androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Candidate$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.PartsApiMessage: void getParts$annotations()
com.example.everytalk.data.DataClass.Candidate: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Content$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String component1()
androidx.compose.foundation.text.Handle: androidx.compose.foundation.text.Handle valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidClipboardManager getClipboardManager()
com.example.everytalk.data.DataClass.GeminiApiResponse: java.util.List getCandidates()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String getHref()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.input.pointer.PointerIconService getPointerIconService()
com.example.everytalk.data.DataClass.Content: java.lang.String toString()
com.example.everytalk.statecontroller.LRUCache: java.util.Set getEntries()
com.example.everytalk.data.DataClass.Sender: Sender(java.lang.String,int)
kotlinx.serialization.json.DecodeSequenceMode: kotlinx.serialization.json.DecodeSequenceMode valueOf(java.lang.String)
androidx.compose.material3.tokens.TypographyKeyTokens: androidx.compose.material3.tokens.TypographyKeyTokens valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.input.InputModeManager getInputModeManager()
com.example.everytalk.data.DataClass.Content$$serializer: Content$$serializer()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
io.ktor.client.engine.android.AndroidEngineContainer: AndroidEngineContainer()
androidx.navigation.compose.DialogNavigator: DialogNavigator()
com.google.gson.reflect.TypeToken: TypeToken()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
androidx.compose.ui.platform.AndroidComposeView: void setAccessibilityEventBatchIntervalMillis(long)
androidx.compose.ui.window.PopupLayout: boolean getCanCalculatePosition()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
com.example.everytalk.data.DataClass.AbstractApiMessage: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.compose.foundation.text.KeyCommand: androidx.compose.foundation.text.KeyCommand[] values()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Boolean component12()
com.example.everytalk.data.DataClass.ApiConfig: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.compose.ui.platform.AndroidComposeView: androidx.collection.MutableIntObjectMap getLayoutNodes()
androidx.compose.ui.state.ToggleableState: androidx.compose.ui.state.ToggleableState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: void getQwenEnableSearch$annotations()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.font.Font$ResourceLoader getFontLoader()
com.example.everytalk.data.DataClass.Candidate: java.lang.String component2()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
androidx.compose.ui.platform.AndroidCompositionLocals_androidKt: androidx.compose.runtime.ProvidableCompositionLocal getLocalLifecycleOwner()
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.compose.ui.Modifier getModifier()
com.example.everytalk.statecontroller.LRUCache: LRUCache(int)
androidx.compose.material3.ModalBottomSheetDialogLayout$Api33Impl: void maybeRegisterBackCallback(android.view.View,java.lang.Object)
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
coil3.size.Precision: coil3.size.Precision[] values()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.input.TextInputService getTextInputService()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.focus.FocusOwner getFocusOwner()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.String toString()
com.example.everytalk.data.DataClass.GeminiApiResponse: int hashCode()
androidx.compose.foundation.text.selection.CrossStatus: androidx.compose.foundation.text.selection.CrossStatus[] values()
androidx.compose.material3.DrawerValue: androidx.compose.material3.DrawerValue[] values()
com.example.everytalk.data.network.AppStreamEvent$Text$Companion: kotlinx.serialization.KSerializer serializer()
kotlinx.serialization.json.ClassDiscriminatorMode: kotlinx.serialization.json.ClassDiscriminatorMode[] values()
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight[] values()
com.example.everytalk.data.DataClass.SafetySetting: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.SafetySetting,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: SafetyRating$$serializer()
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Sender getSender()
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ApiContentPart$FileUri,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.WebSearchResult: boolean equals(java.lang.Object)
androidx.compose.ui.unit.ConstraintsKt: void throwInvalidConstraintException(int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: SimpleTextApiMessage$$serializer()
androidx.compose.foundation.layout.Direction: androidx.compose.foundation.layout.Direction valueOf(java.lang.String)
androidx.compose.ui.window.PopupLayout: void setTestTag(java.lang.String)
androidx.appcompat.resources.Compatibility$Api21Impl: int getChangingConfigurations(android.content.res.TypedArray)
androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand: androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand valueOf(java.lang.String)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.Set component2()
com.example.everytalk.data.DataClass.WebSearchResult$Companion: WebSearchResult$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.modifier.ModifierLocalManager getModifierLocalManager()
com.example.everytalk.data.DataClass.ThinkingConfig: int hashCode()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ChatRequest)
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.AbstractApiMessage toApiMessage()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer getMaxTokens()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getName()
androidx.compose.foundation.text.selection.SelectionHandleAnchor: androidx.compose.foundation.text.selection.SelectionHandleAnchor[] values()
androidx.compose.ui.platform.ComposeView: boolean getShouldCreateCompositionOnAttachedToWindow()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String getUri()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
com.example.everytalk.data.DataClass.GeminiApiResponse: java.lang.String toString()
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map getCustomExtraBody()
com.example.everytalk.data.DataClass.Part$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.util.IncrementalMarkdownParser$TokenType: com.example.everytalk.util.IncrementalMarkdownParser$TokenType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease: com.example.everytalk.data.DataClass.GithubRelease copy(java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Candidate copy(com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
androidx.compose.ui.viewinterop.ViewFactoryHolder: void setUpdateBlock(kotlin.jvm.functions.Function1)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isVisible(int)
com.example.everytalk.data.DataClass.Part$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String getLanguage()
androidx.activity.ComponentActivity: void setContentView(android.view.View)
androidx.compose.ui.window.PopupLayout: void setPositionProvider(androidx.compose.ui.window.PopupPositionProvider)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String component3()
com.example.everytalk.data.network.AppStreamEvent$StreamEnd$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GithubRelease$Companion: GithubRelease$Companion()
com.example.everytalk.data.DataClass.PartsApiMessage: com.example.everytalk.data.DataClass.PartsApiMessage copy$default(com.example.everytalk.data.DataClass.PartsApiMessage,java.lang.String,java.lang.String,java.util.List,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ThinkingConfig: void getThinkingBudget$annotations()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer component11()
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String getThreshold()
androidx.appcompat.resources.Compatibility$Api21Impl: android.graphics.drawable.Drawable createFromXmlInner(android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
io.ktor.serialization.kotlinx.json.KotlinxSerializationJsonExtensionProvider: KotlinxSerializationJsonExtensionProvider()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$Text deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part$Text: com.example.everytalk.data.DataClass.Part$Text copy$default(com.example.everytalk.data.DataClass.Part$Text,java.lang.String,int,java.lang.Object)
androidx.compose.ui.node.LayoutNode: java.lang.String exceptionMessageForParentingOrOwnership(androidx.compose.ui.node.LayoutNode)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component1()
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.List component1()
com.example.everytalk.data.DataClass.Content$$serializer: com.example.everytalk.data.DataClass.Content deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: void getMimeType$annotations()
com.example.everytalk.data.DataClass.Part$Text: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.PartsApiMessage: PartsApiMessage(int,java.lang.String,java.lang.String,java.util.List,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getRole$annotations()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ModalityType component8()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.ClipboardManager getClipboardManager()
com.example.everytalk.data.DataClass.Message: java.lang.String component2()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
com.example.everytalk.statecontroller.LRUCache: boolean removeEldestEntry(java.util.Map$Entry)
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String component2()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Part$InlineData)
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: ApiContentPart$InlineData$$serializer()
androidx.compose.ui.graphics.layer.view.DrawChildContainer: int getChildCount()
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion: ApiContentPart$Text$Companion()
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion: GeminiApiRequest$Companion()
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String toString()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String component2()
androidx.compose.material.ripple.RippleHostView: void setRippleState(boolean)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: com.example.everytalk.data.DataClass.WebSearchResult deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Content$Companion: Content$Companion()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse(int,java.util.List,com.example.everytalk.data.DataClass.PromptFeedback,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.PromptFeedback: java.util.List component1()
kotlinx.serialization.json.JsonElement$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.AbstractApiMessage: java.lang.String getName()
androidx.compose.ui.platform.AndroidComposeView: kotlin.coroutines.CoroutineContext getCoroutineContext()
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GenerationConfig component2()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: boolean equals(java.lang.Object)
androidx.compose.ui.platform.AbstractComposeView: void setTransitionGroup(boolean)
com.example.everytalk.data.DataClass.ApiContentPart$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GenerationConfig)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.graphics.GraphicsContext getGraphicsContext()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: ApiConfig$$serializer()
androidx.compose.ui.graphics.layer.ViewLayer: androidx.compose.ui.graphics.CanvasHolder getCanvasHolder()
com.example.everytalk.data.DataClass.ModalityType$Companion: com.example.everytalk.data.DataClass.ModalityType fromDisplayName(java.lang.String)
com.example.everytalk.data.DataClass.Message: java.util.List component10()
coil3.size.Precision: coil3.size.Precision valueOf(java.lang.String)
com.example.everytalk.data.DataClass.IMessage: java.lang.String getId()
com.example.everytalk.data.DataClass.PartsApiMessage: void getRole$annotations()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: SafetySetting$$serializer()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float component2()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.GenerationConfig: void getThinkingConfig$annotations()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.PromptFeedback)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function1 getOnRequestDisallowInterceptTouchEvent$ui_release()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.WebSearchResult)
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function0 getRelease()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String getMimeType()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.foundation.MutatePriority: androidx.compose.foundation.MutatePriority[] values()
com.example.everytalk.data.DataClass.GenerationConfig: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.GenerationConfig,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Audio: int hashCode()
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String component1()
com.example.everytalk.data.DataClass.GeminiApiResponse: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.GeminiApiResponse,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component2()
androidx.compose.material3.internal.TextFieldType: androidx.compose.material3.internal.TextFieldType[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
androidx.compose.material3.ScaffoldLayoutContent: androidx.compose.material3.ScaffoldLayoutContent valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Part: Part(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.MessageKt: java.lang.String toRole(com.example.everytalk.data.DataClass.Sender)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ChatRequest: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
com.example.everytalk.data.network.ApiClient$FileMetadata$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.SafetySetting)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.Candidate: int component3()
androidx.compose.runtime.collection.MutableVectorKt: void throwReversedIndicesException(int,int)
com.example.everytalk.data.DataClass.Content: java.lang.String getRole()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.AbstractApiMessage: AbstractApiMessage(int,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.window.PopupLayout: void setParentLayoutCoordinates(androidx.compose.ui.layout.LayoutCoordinates)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.Part: Part()
androidx.compose.material3.tokens.ShapeKeyTokens: androidx.compose.material3.tokens.ShapeKeyTokens[] values()
okhttp3.TlsVersion: okhttp3.TlsVersion valueOf(java.lang.String)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$Text: ApiContentPart$Text(java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: com.example.everytalk.data.DataClass.PartsApiMessage deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.compose.ui.text.style.ResolvedTextDirection: androidx.compose.ui.text.style.ResolvedTextDirection valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: com.example.everytalk.data.DataClass.ApiContentPart$InlineData copy(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig()
com.example.everytalk.data.DataClass.Message: java.util.List getImageUrls()
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ApiConfig copy(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: PartsApiMessage$$serializer()
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion: kotlinx.serialization.KSerializer serializer()
androidx.graphics.path.ConicConverter: int internalConicToQuadratics(float[],int,float[],float,float)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.SimpleTextApiMessage)
androidx.compose.ui.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String toString()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.GithubRelease: GithubRelease(int,java.lang.String,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: AppViewModel$ExportedSettings(java.util.List,java.util.Set)
androidx.core.view.WindowInsetsCompat$Impl34: WindowInsetsCompat$Impl34(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl34)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion: SimpleTextApiMessage$Companion()
com.example.everytalk.data.network.AppStreamEvent$Finish$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ApiConfig: int hashCode()
com.example.everytalk.data.DataClass.Content: Content(int,java.util.List,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.viewinterop.ViewFactoryHolder: void setResetBlock(kotlin.jvm.functions.Function1)
androidx.compose.ui.viewinterop.ViewFactoryHolder: android.view.View getViewRoot()
okhttp3.internal.http2.ErrorCode: okhttp3.internal.http2.ErrorCode[] values()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String getTitle()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.SimpleTextApiMessage,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.IMessage: java.lang.String getName()
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.example.everytalk.data.DataClass.ApiConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.compose.ui.unit.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse(java.util.List,com.example.everytalk.data.DataClass.PromptFeedback)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: GithubRelease$$serializer()
okhttp3.TlsVersion: okhttp3.TlsVersion[] values()
com.example.everytalk.data.DataClass.Part$Text$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.data.DataClass.Part$InlineData$Companion: Part$InlineData$Companion()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiContentPart$Text)
com.example.everytalk.data.DataClass.ChatRequest: void getUseWebSearch$annotations()
androidx.compose.material3.tokens.TypographyKeyTokens: androidx.compose.material3.tokens.TypographyKeyTokens[] values()
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String getCategory()
org.slf4j.nop.NOPServiceProvider: NOPServiceProvider()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer component14()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
com.example.everytalk.data.network.AppStreamEvent$Reasoning$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Message: java.lang.String getCurrentWebSearchStage()
com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GeminiApiRequest)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.compose.ui.unit.LayoutDirection: androidx.compose.ui.unit.LayoutDirection[] values()
androidx.activity.EdgeToEdgeApi30: void adjustLayoutInDisplayCutoutMode(android.view.Window)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getAddress()
com.example.everytalk.data.DataClass.Message: boolean isError()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.platform.AbstractComposeView getSubCompositionView()
androidx.compose.ui.viewinterop.FocusGroupPropertiesElement: FocusGroupPropertiesElement()
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GeminiApiRequest copy$default(com.example.everytalk.data.DataClass.GeminiApiRequest,java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,int,java.lang.Object)
com.example.everytalk.data.DataClass.SafetyRating: com.example.everytalk.data.DataClass.SafetyRating copy$default(com.example.everytalk.data.DataClass.SafetyRating,java.lang.String,java.lang.String,int,java.lang.Object)
androidx.compose.ui.platform.AndroidComposeViewVerificationHelperMethodsO: void focusable(android.view.View,int,boolean)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
com.example.everytalk.data.DataClass.Candidate: Candidate(com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String toString()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.SafetyRating: SafetyRating(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
kotlin.DeprecationLevel: kotlin.DeprecationLevel valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float component1()
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String component2()
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Message copy$default(com.example.everytalk.data.DataClass.Message,java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List,int,java.lang.Object)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.compose.ui.viewinterop.ViewFactoryHolder: void setSavableRegistryEntry(androidx.compose.runtime.saveable.SaveableStateRegistry$Entry)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ContentPart$Audio: ContentPart$Audio(java.lang.String,java.lang.String,java.lang.String)
coil3.request.CachePolicy: coil3.request.CachePolicy[] values()
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
com.example.everytalk.data.DataClass.SafetySetting$Companion: SafetySetting$Companion()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
androidx.compose.material3.SnackbarDuration: androidx.compose.material3.SnackbarDuration[] values()
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
com.example.everytalk.data.DataClass.GenerationConfig: void getMaxOutputTokens$annotations()
kotlin.LazyThreadSafetyMode: kotlin.LazyThreadSafetyMode[] values()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
com.example.everytalk.data.DataClass.ChatRequest: void getCustomExtraBody$annotations()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: Part$FileUri$$serializer()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.compose.ui.graphics.AndroidPath_androidKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List component1()
com.example.everytalk.data.DataClass.GithubRelease$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Part$Text: java.lang.String component1()
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.GenerationConfig copy$default(com.example.everytalk.data.DataClass.GenerationConfig,java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig,int,java.lang.Object)
androidx.compose.ui.window.PopupLayout: boolean getShouldCreateCompositionOnAttachedToWindow()
com.example.everytalk.data.DataClass.Message: boolean getContentStarted()
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.PartsApiMessage$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction: androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Candidate: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.GenerationConfig: void getTopP$annotations()
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
com.example.everytalk.data.DataClass.ApiConfig: ApiConfig(int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.PromptFeedback: com.example.everytalk.data.DataClass.PromptFeedback copy$default(com.example.everytalk.data.DataClass.PromptFeedback,java.util.List,int,java.lang.Object)
com.example.everytalk.data.DataClass.WebSearchResult: int hashCode()
com.example.everytalk.data.DataClass.ContentPart$Code: com.example.everytalk.data.DataClass.ContentPart$Code copy(java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Message: java.lang.String getReasoning()
androidx.compose.ui.platform.ComposeView: java.lang.CharSequence getAccessibilityClassName()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
com.example.everytalk.statecontroller.LRUCache: int getSize()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GithubRelease)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.compose.ui.platform.AndroidComposeView: void setCoroutineContext(kotlin.coroutines.CoroutineContext)
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String toString()
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String getCategory()
com.example.everytalk.data.DataClass.ChatRequest: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ChatRequest,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.platform.DrawChildContainer getContainer()
kotlinx.serialization.json.JsonArray$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.actionmodecallback.MenuItemOption: androidx.compose.ui.platform.actionmodecallback.MenuItemOption valueOf(java.lang.String)
androidx.compose.runtime.collection.MutableVectorKt: void throwOutOfRangeException(int,int)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getImageSize()
com.example.everytalk.data.DataClass.IMessage: java.lang.String getRole()
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig(int,java.lang.Boolean,java.lang.Integer,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.material3.internal.InputPhase: androidx.compose.material3.internal.InputPhase[] values()
com.example.everytalk.data.DataClass.GeminiApiRequest: boolean equals(java.lang.Object)
androidx.compose.material3.tokens.ColorSchemeKeyTokens: androidx.compose.material3.tokens.ColorSchemeKeyTokens[] values()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String toString()
com.example.everytalk.data.DataClass.PartsApiMessage: void getId$annotations()
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.compose.ui.platform.AndroidComposeView: void setConfigurationChangeObserver(kotlin.jvm.functions.Function1)
androidx.activity.EdgeToEdgeApi29: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
kotlinx.serialization.json.DecodeSequenceMode: kotlinx.serialization.json.DecodeSequenceMode[] values()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: com.example.everytalk.data.DataClass.SimpleTextApiMessage copy$default(com.example.everytalk.data.DataClass.SimpleTextApiMessage,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: Part$InlineData$$serializer()
androidx.compose.animation.core.AnimationEndReason: androidx.compose.animation.core.AnimationEndReason valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Part$FileUri,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: WebSearchResult$$serializer()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.semantics.SemanticsOwner getSemanticsOwner()
com.example.everytalk.data.DataClass.Part$FileUri: com.example.everytalk.data.DataClass.Part$FileUri copy$default(com.example.everytalk.data.DataClass.Part$FileUri,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion: SimpleTextApiMessage$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: void getLastMatrixRecalculationAnimationTime$ui_release$annotations()
com.example.everytalk.data.DataClass.ChatRequest: void getMessages$annotations()
androidx.compose.ui.graphics.Path$Direction: androidx.compose.ui.graphics.Path$Direction[] values()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String component1()
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Candidate copy$default(com.example.everytalk.data.DataClass.Candidate,com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List,int,java.lang.Object)
io.ktor.util.date.Month: io.ktor.util.date.Month valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Candidate$$serializer: Candidate$$serializer()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
androidx.compose.ui.platform.AndroidComposeViewTranslationCallbackS: void clearViewTranslationCallback(android.view.View)
kotlin.io.encoding.Base64$PaddingOption: kotlin.io.encoding.Base64$PaddingOption valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.compose.ui.platform.DrawChildContainer: int getChildCount()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion: ApiContentPart$FileUri$Companion()
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.ui.unit.LayoutDirection: androidx.compose.ui.unit.LayoutDirection valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Content: com.example.everytalk.data.DataClass.Content copy$default(com.example.everytalk.data.DataClass.Content,java.util.List,java.lang.String,int,java.lang.Object)
androidx.compose.ui.platform.TextToolbarStatus: androidx.compose.ui.platform.TextToolbarStatus[] values()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.GeminiApiResponse copy(java.util.List,com.example.everytalk.data.DataClass.PromptFeedback)
com.example.everytalk.data.DataClass.Sender: kotlin.enums.EnumEntries getEntries()
com.example.everytalk.statecontroller.MainActivity: MainActivity()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String getTagName()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
kotlinx.serialization.json.internal.WriteMode: kotlinx.serialization.json.internal.WriteMode valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String getMimeType()
kotlin.io.encoding.Base64$PaddingOption: kotlin.io.encoding.Base64$PaddingOption[] values()
androidx.compose.ui.platform.ViewLayer: long getOwnerViewId()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String component4()
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
androidx.compose.foundation.text.Handle: androidx.compose.foundation.text.Handle[] values()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getModel()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.LayoutNode getRoot()
androidx.compose.foundation.layout.Direction: androidx.compose.foundation.layout.Direction[] values()
androidx.compose.ui.contentcapture.ContentCaptureEventType: androidx.compose.ui.contentcapture.ContentCaptureEventType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SafetySetting: SafetySetting(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: void getBase64Data$annotations()
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String component1()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
androidx.compose.foundation.layout.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: SimpleTextApiMessage(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Message$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Message)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component4()
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
com.example.everytalk.data.DataClass.ApiContentPart: ApiContentPart(int,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiConfig$Companion: ApiConfig$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Boolean getIncludeThoughts()
kotlinx.serialization.json.JsonNull: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String getCode()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorNext(long,float[],int)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.OwnerSnapshotObserver getSnapshotObserver()
androidx.compose.animation.core.RepeatMode: androidx.compose.animation.core.RepeatMode valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List component3()
com.example.everytalk.data.DataClass.SafetyRating: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl34: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
com.example.everytalk.data.network.AppStreamEvent$StatusUpdate$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.foundation.layout.IntrinsicSize: androidx.compose.foundation.layout.IntrinsicSize[] values()
com.example.everytalk.data.DataClass.ModalityType: kotlin.enums.EnumEntries getEntries()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: int hashCode()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AccessibilityManager getAccessibilityManager()
androidx.compose.ui.platform.AndroidComposeView: void setShowLayoutBounds(boolean)
com.example.everytalk.data.DataClass.ThinkingConfig$Companion: ThinkingConfig$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.statecontroller.LRUCache: java.util.Set getKeys()
androidx.compose.material3.SheetValue: androidx.compose.material3.SheetValue valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.Part$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.SafetyRating)
androidx.emoji2.text.EmojiCompatInitializer: EmojiCompatInitializer()
com.example.everytalk.data.DataClass.ChatRequest: int hashCode()
coil3.request.CachePolicy: coil3.request.CachePolicy valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.WindowInfo getWindowInfo()
com.example.everytalk.data.DataClass.Candidate: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Candidate,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Content: java.lang.String component2()
androidx.compose.ui.platform.AndroidComposeViewStartDragAndDropN: boolean startDragAndDrop(android.view.View,androidx.compose.ui.draganddrop.DragAndDropTransferData,androidx.compose.ui.draganddrop.ComposeDragShadowBuilder)
com.example.everytalk.data.DataClass.Candidate$Companion: Candidate$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.example.everytalk.data.DataClass.PartsApiMessage$Companion: PartsApiMessage$Companion()
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.Message: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Message,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List getContents()
androidx.compose.ui.util.ListUtilsKt: void throwUnsupportedOperationException(java.lang.String)
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.platform.AndroidComposeView getOwnerView()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
com.example.everytalk.data.DataClass.ApiContentPart: ApiContentPart()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
com.example.everytalk.data.DataClass.PromptFeedback: boolean equals(java.lang.Object)
androidx.compose.animation.core.PreconditionsKt: void throwIllegalArgumentException(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AutofillManager getAutofillManager()
com.example.everytalk.data.DataClass.ContentPart$Html: com.example.everytalk.data.DataClass.ContentPart$Html copy(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Content: int hashCode()
com.example.everytalk.data.DataClass.PartsApiMessage: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.compose.ui.platform.AndroidComposeView: void setLayoutDirection(androidx.compose.ui.unit.LayoutDirection)
io.ktor.util.Platform: io.ktor.util.Platform[] values()
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.compose.ui.platform.AndroidComposeView: long getMeasureIteration()
androidx.compose.ui.window.PopupLayout: void setPopupContentSize-fhxjrPA(androidx.compose.ui.unit.IntSize)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.compose.ui.layout.IntrinsicWidthHeight: androidx.compose.ui.layout.IntrinsicWidthHeight[] values()
com.example.everytalk.data.DataClass.Candidate: int hashCode()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiContentPart$FileUri)
com.example.everytalk.data.DataClass.Part$Text: java.lang.String getText()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String getContentId()
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function0 getReset()
androidx.compose.ui.viewinterop.FocusTargetPropertiesElement: FocusTargetPropertiesElement()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.TextToolbar getTextToolbar()
androidx.compose.ui.window.PopupLayout: void setContent(kotlin.jvm.functions.Function2)
com.example.everytalk.data.DataClass.PromptFeedback: int hashCode()
com.example.everytalk.data.DataClass.Part$Text$Companion: Part$Text$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl20: void setSystemUiVisibility(int)
com.example.everytalk.data.DataClass.GeminiApiRequest: java.lang.String toString()
androidx.compose.foundation.layout.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.PromptFeedback component2()
org.slf4j.event.Level: org.slf4j.event.Level[] values()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component2()
org.slf4j.helpers.Reporter$TargetChoice: org.slf4j.helpers.Reporter$TargetChoice valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String toString()
com.example.everytalk.data.DataClass.Part$Text: Part$Text(java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String getName()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
com.example.everytalk.statecontroller.LRUCache: java.util.Set keySet()
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
com.example.everytalk.data.DataClass.Candidate: int getIndex()
com.example.everytalk.data.DataClass.SafetySetting: com.example.everytalk.data.DataClass.SafetySetting copy(java.lang.String,java.lang.String)
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.layout.LayoutCoordinates getParentLayoutCoordinates()
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.models.SelectedMediaItem$GenericFile$Companion: kotlinx.serialization.KSerializer serializer()
kotlin.reflect.KVariance: kotlin.reflect.KVariance[] values()
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String component1()
com.example.everytalk.data.DataClass.Sender: kotlinx.serialization.KSerializer _init_$_anonymous_()
androidx.compose.ui.internal.InlineClassHelperKt: java.lang.Void throwIllegalStateExceptionForNullCheck(java.lang.String)
androidx.compose.ui.platform.CalculateMatrixToWindowApi29: void calculateMatrixToWindow-EL8BTi8(android.view.View,float[])
com.example.everytalk.data.DataClass.Content: boolean equals(java.lang.Object)
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
com.example.everytalk.data.DataClass.WebSearchResult$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SafetyRating: int hashCode()
com.example.everytalk.data.DataClass.PromptFeedback: com.example.everytalk.data.DataClass.PromptFeedback copy(java.util.List)
com.example.everytalk.data.DataClass.ApiConfig$$serializer: com.example.everytalk.data.DataClass.ApiConfig deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.ui.screens.MainScreen.AiMessageOption: com.example.everytalk.ui.screens.MainScreen.AiMessageOption[] values()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: com.example.everytalk.data.DataClass.SafetyRating deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.ViewLayer: float[] getUnderlyingMatrix-sQKQjiQ()
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String getBody()
androidx.core.view.WindowInsetsCompat$Impl: void setSystemUiVisibility(int)
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String toString()
kotlinx.serialization.json.JsonPrimitive$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Candidate$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.ComposeView: void setContent(kotlin.jvm.functions.Function2)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.compose.ui.viewinterop.AndroidViewHolder: android.view.ViewGroup$LayoutParams getLayoutParams()
com.example.everytalk.data.DataClass.Part: Part(int,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.Part$Companion: Part$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: int hashCode()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: ApiContentPart$FileUri(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.GeminiApiRequest: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.GeminiApiRequest,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.startup.InitializationProvider: InitializationProvider()
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.unit.IntRect getVisibleDisplayBounds()
com.example.everytalk.data.DataClass.Part$FileUri: com.example.everytalk.data.DataClass.Part$FileUri copy(java.lang.String)
androidx.compose.ui.platform.AbstractComposeView: void setParentCompositionContext(androidx.compose.runtime.CompositionContext)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float component15()
androidx.compose.ui.platform.AndroidComposeViewForceDarkModeQ: void disallowForceDark(android.view.View)
com.example.everytalk.data.DataClass.Part: kotlinx.serialization.KSerializer _init_$_anonymous_()
com.example.everytalk.data.DataClass.Sender$Companion: Sender$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20(androidx.core.view.WindowInsetsCompat)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.compose.ui.node.LayoutNode getLayoutNode()
androidx.graphics.path.PathIteratorPreApi34Impl: long createInternalPathIterator(android.graphics.Path,int,float)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getKey()
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean component6()
androidx.compose.foundation.text.selection.Direction: androidx.compose.foundation.text.selection.Direction[] values()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setModifier(androidx.compose.ui.Modifier)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.lang.String toString()
com.example.everytalk.data.DataClass.ContentPart$Code: ContentPart$Code(java.lang.String,java.lang.String,java.lang.String)
androidx.compose.ui.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String component1()
com.example.everytalk.data.DataClass.ApiContentPart: ApiContentPart(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.emoji2.text.EmojiExclusions$EmojiExclusions_Api34: java.util.Set getExclusions()
com.example.everytalk.data.DataClass.ContentPart$Html: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Part$InlineData: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Part$InlineData,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.animation.core.AnimationEndReason: androidx.compose.animation.core.AnimationEndReason[] values()
androidx.compose.runtime.InvalidationResult: androidx.compose.runtime.InvalidationResult valueOf(java.lang.String)
com.example.everytalk.models.SelectedMediaItem$Audio$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SafetySetting: int hashCode()
com.example.everytalk.data.DataClass.ThinkingConfig: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ThinkingConfig,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ThinkingConfig: void getIncludeThoughts$annotations()
com.example.everytalk.data.DataClass.Message: java.lang.String component11()
androidx.compose.runtime.SlotTableKt: void throwConcurrentModificationException()
com.example.everytalk.data.DataClass.ApiConfig: ApiConfig(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float)
com.example.everytalk.data.DataClass.ChatRequest: void getApiAddress$annotations()
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.ThinkingConfig getThinkingConfig()
com.example.everytalk.data.DataClass.ChatRequest: void getTools$annotations()
org.slf4j.helpers.Reporter$TargetChoice: org.slf4j.helpers.Reporter$TargetChoice[] values()
com.example.everytalk.data.DataClass.Message: boolean equals(java.lang.Object)
androidx.compose.ui.platform.AbstractComposeView: boolean getShouldCreateCompositionOnAttachedToWindow()
androidx.compose.ui.platform.AndroidComposeViewVerificationHelperMethodsN: void setPointerIcon(android.view.View,androidx.compose.ui.input.pointer.PointerIcon)
com.example.everytalk.data.network.AppStreamEvent$Companion: kotlinx.serialization.KSerializer serializer()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorPeek(long)
com.example.everytalk.data.DataClass.WebSearchResult$Companion: com.example.everytalk.data.DataClass.WebSearchResult fromMap(java.util.Map)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Part$Text: int hashCode()
androidx.compose.ui.window.SecureFlagPolicy: androidx.compose.ui.window.SecureFlagPolicy[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
com.example.everytalk.data.DataClass.ApiContentPart: void write$Self(com.example.everytalk.data.DataClass.ApiContentPart,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function1 getOnDensityChanged$ui_release()
androidx.compose.foundation.gestures.Orientation: androidx.compose.foundation.gestures.Orientation valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: java.util.List component13()
com.example.everytalk.data.DataClass.GenerationConfig: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Message: java.lang.String getId()
androidx.compose.ui.window.PopupLayout: java.lang.String getTestTag()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.draganddrop.DragAndDropManager getDragAndDropManager()
androidx.compose.ui.platform.ViewLayer: void setInvalidated(boolean)
androidx.compose.ui.viewinterop.ViewFactoryHolder: void setReleaseBlock(kotlin.jvm.functions.Function1)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer getNumInferenceSteps()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.LayoutNodeDrawScope getSharedDrawScope()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
com.example.everytalk.data.DataClass.ApiConfig: boolean isValid()
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType[] values()
com.example.everytalk.data.DataClass.Message$$serializer: com.example.everytalk.data.DataClass.Message deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String component1()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.hapticfeedback.HapticFeedback getHapticFeedBack()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ChatRequest: java.util.List component1()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.compose.ui.node.LookaheadPassDelegate$PlacedState: androidx.compose.ui.node.LookaheadPassDelegate$PlacedState[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String toString()
com.example.everytalk.data.DataClass.Candidate: Candidate(com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.compose.ui.platform.AbstractComposeView: boolean getHasComposition()
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.focus.FocusTargetNode$FocusTargetElement: FocusTargetNode$FocusTargetElement()
com.example.everytalk.data.DataClass.Message: Message(int,java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: com.example.everytalk.data.DataClass.GenerationConfig deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.lifecycle.LifecycleOwner getLifecycleOwner()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getModel()
com.example.everytalk.data.DataClass.Content$Companion: Content$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Object component10()
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: AbstractApiMessage$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: androidx.collection.IntObjectMap getLayoutNodes()
com.example.everytalk.data.network.AppStreamEvent$ToolCall$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
com.example.everytalk.data.DataClass.Part$InlineData: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.GithubRelease: void getBody$annotations()
androidx.compose.ui.window.PopupLayout: kotlin.jvm.functions.Function2 getContent()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorRawSize(long)
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String getFileUri()
com.example.everytalk.data.DataClass.ModalityType$Companion: ModalityType$Companion()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ModalityType getModalityType()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setDensity(androidx.compose.ui.unit.Density)
com.example.everytalk.data.DataClass.Content$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List getSafetySettings()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String component3()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: GeminiApiRequest$$serializer()
com.example.everytalk.data.DataClass.ApiContentPart$Text: ApiContentPart$Text(int,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component5()
com.example.everytalk.data.DataClass.Message: java.util.List getWebSearchResults()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.ViewConfiguration getViewConfiguration()
androidx.compose.foundation.text.selection.CrossStatus: androidx.compose.foundation.text.selection.CrossStatus valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String component3()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
com.example.everytalk.data.DataClass.Candidate$$serializer: com.example.everytalk.data.DataClass.Candidate deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.core.content.FileProvider: FileProvider()
androidx.compose.ui.platform.AbstractComposeView: void setParentContext(androidx.compose.runtime.CompositionContext)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Content: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
io.ktor.client.plugins.cache.ValidateStatus: io.ktor.client.plugins.cache.ValidateStatus[] values()
com.example.everytalk.data.DataClass.GeminiApiResponse: boolean equals(java.lang.Object)
coil3.decode.DataSource: coil3.decode.DataSource valueOf(java.lang.String)
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String toString()
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig(java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig)
androidx.compose.ui.viewinterop.ViewFactoryHolder: androidx.compose.ui.input.nestedscroll.NestedScrollDispatcher getDispatcher()
com.example.everytalk.data.DataClass.GithubRelease: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.GithubRelease,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.Message: Message(java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.SafetySetting$Companion: SafetySetting$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.WebSearchResult: com.example.everytalk.data.DataClass.WebSearchResult copy(int,java.lang.String,java.lang.String,java.lang.String)
kotlin.text.RegexOption: kotlin.text.RegexOption valueOf(java.lang.String)
coil3.size.Scale: coil3.size.Scale valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.ThinkingConfig component4()
com.example.everytalk.data.DataClass.Part$FileUri$Companion: Part$FileUri$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
io.ktor.util.Platform: io.ktor.util.Platform valueOf(java.lang.String)
androidx.compose.ui.input.pointer.PointerEventPass: androidx.compose.ui.input.pointer.PointerEventPass[] values()
androidx.compose.material3.ModalBottomSheetDialogLayout$Api34Impl: android.window.OnBackAnimationCallback createBackCallback(kotlin.jvm.functions.Function0,androidx.compose.animation.core.Animatable,kotlinx.coroutines.CoroutineScope)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: com.example.everytalk.data.DataClass.SimpleTextApiMessage copy(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.graphics.Path getManualClipPath()
androidx.core.view.WindowInsetsCompat$BuilderImpl34: void setInsets(int,androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.ChatRequest: void getModel$annotations()
org.slf4j.helpers.Reporter$Level: org.slf4j.helpers.Reporter$Level[] values()
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
com.example.everytalk.statecontroller.LRUCache: int size()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.compose.runtime.Recomposer$State: androidx.compose.runtime.Recomposer$State[] values()
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ChatRequest$Companion: ChatRequest$Companion()
com.example.everytalk.data.DataClass.ContentPart$Audio: com.example.everytalk.data.DataClass.ContentPart$Audio copy(java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback: PromptFeedback(int,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Integer getThinkingBudget()
androidx.navigation.NavBackStackEntry$SavedStateViewModel: NavBackStackEntry$SavedStateViewModel(androidx.lifecycle.SavedStateHandle)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
com.example.everytalk.data.network.AppStreamEvent$WebSearchResults$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.material3.SheetValue: androidx.compose.material3.SheetValue[] values()
androidx.compose.ui.viewinterop.AndroidViewHolder: android.view.View getView()
io.ktor.client.plugins.cache.ValidateStatus: io.ktor.client.plugins.cache.ValidateStatus valueOf(java.lang.String)
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component3()
com.example.everytalk.data.DataClass.PromptFeedback$Companion: PromptFeedback$Companion()
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String toString()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getRole()
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.Message: boolean isPlaceholderName()
com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean getQwenEnableSearch()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean component7()
com.example.everytalk.models.ImageSourceOption: com.example.everytalk.models.ImageSourceOption valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ThinkingConfig: com.example.everytalk.data.DataClass.ThinkingConfig copy(java.lang.Boolean,java.lang.Integer)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus: androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus[] values()
androidx.compose.foundation.text.TextContextMenuItems: androidx.compose.foundation.text.TextContextMenuItems[] values()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String component1()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidViewsHandler getAndroidViewsHandler$ui_release()
com.example.everytalk.data.DataClass.ThinkingConfig: boolean equals(java.lang.Object)
androidx.compose.ui.platform.AbstractComposeView: void setViewCompositionStrategy(androidx.compose.ui.platform.ViewCompositionStrategy)
androidx.graphics.path.PathIteratorPreApi34Impl: void destroyInternalPathIterator(long)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
androidx.compose.runtime.Recomposer$State: androidx.compose.runtime.Recomposer$State valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: float component9()
androidx.compose.material3.SnackbarDuration: androidx.compose.material3.SnackbarDuration valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: int hashCode()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setOnRequestDisallowInterceptTouchEvent$ui_release(kotlin.jvm.functions.Function1)
androidx.compose.ui.window.PopupLayout: void setParentLayoutDirection(androidx.compose.ui.unit.LayoutDirection)
com.example.everytalk.data.DataClass.Content: java.util.List getParts()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.Autofill getAutofill()
androidx.compose.ui.platform.TextToolbarStatus: androidx.compose.ui.platform.TextToolbarStatus valueOf(java.lang.String)
androidx.compose.ui.focus.FocusStateImpl: androidx.compose.ui.focus.FocusStateImpl[] values()
com.example.everytalk.data.DataClass.GeminiApiRequest: GeminiApiRequest(int,java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.Clipboard getClipboard()
com.example.everytalk.data.DataClass.PartsApiMessage$Companion: PartsApiMessage$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getContent$annotations()
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: AbstractApiMessage$Companion()
com.example.everytalk.data.DataClass.Sender: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
androidx.core.view.WindowInsetsCompat$BuilderImpl34: WindowInsetsCompat$BuilderImpl34()
coil3.util.Logger$Level: coil3.util.Logger$Level[] values()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.ChatRequest copy(java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiContentPart$InlineData)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.PartsApiMessage: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.PartsApiMessage,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig: boolean component7()
androidx.compose.ui.platform.AndroidComposeView: void setLastMatrixRecalculationAnimationTime$ui_release(long)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.PartsApiMessage)
com.example.everytalk.data.DataClass.ApiContentPart$Text: boolean equals(java.lang.Object)
androidx.compose.ui.platform.AndroidComposeView: void setContentCaptureManager$ui_release(androidx.compose.ui.contentcapture.AndroidContentCaptureManager)
kotlin.time.DurationUnit: kotlin.time.DurationUnit valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: int hashCode()
androidx.compose.foundation.gestures.Orientation: androidx.compose.foundation.gestures.Orientation[] values()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.compose.ui.text.internal.InlineClassHelperKt: java.lang.Void throwIllegalArgumentExceptionForNullCheck(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIndexOutOfBoundsException(java.lang.String)
com.example.everytalk.util.IncrementalMarkdownParser$TokenType: com.example.everytalk.util.IncrementalMarkdownParser$TokenType[] values()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String toString()
androidx.compose.material3.tokens.ShapeKeyTokens: androidx.compose.material3.tokens.ShapeKeyTokens valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ModalityType: ModalityType(java.lang.String,int,java.lang.String)
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GeminiApiResponse: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.appcompat.resources.Compatibility$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ApiConfig copy$default(com.example.everytalk.data.DataClass.ApiConfig,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float,int,java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig: ApiConfig(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.GeminiApiRequest: GeminiApiRequest(java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List)
androidx.compose.material3.SnackbarResult: androidx.compose.material3.SnackbarResult[] values()
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String getMimeType()
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
com.example.everytalk.data.DataClass.ChatRequest: java.util.List getTools()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getApiKey()
com.example.everytalk.data.DataClass.Sender$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
androidx.compose.ui.platform.AbstractComposeView: void setPreviousAttachedWindowToken(android.os.IBinder)
com.example.everytalk.data.DataClass.SafetySetting$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getApiAddress()
com.example.everytalk.data.DataClass.Candidate$Companion: Candidate$Companion()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.focus.CustomDestinationResult: androidx.compose.ui.focus.CustomDestinationResult valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig$Companion: GenerationConfig$Companion()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function0 getUpdate()
androidx.compose.animation.core.MutatePriority: androidx.compose.animation.core.MutatePriority[] values()
com.example.everytalk.data.DataClass.ChatRequest: void getGenerationConfig$annotations()
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.compose.material3.internal.InputPhase: androidx.compose.material3.internal.InputPhase valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: boolean component5()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.example.everytalk.data.DataClass.PromptFeedback: PromptFeedback(java.util.List)
okhttp3.Protocol: okhttp3.Protocol valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
com.example.everytalk.data.DataClass.GeminiApiResponse: java.util.List component1()
androidx.compose.ui.platform.ComposeView: void getShouldCreateCompositionOnAttachedToWindow$annotations()
androidx.compose.ui.window.PopupLayout: android.view.WindowManager$LayoutParams getParams$ui_release()
com.example.everytalk.data.DataClass.ApiContentPart: kotlinx.serialization.KSerializer _init_$_anonymous_()
androidx.compose.ui.viewinterop.ViewFactoryHolder: kotlin.jvm.functions.Function1 getResetBlock()
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.PromptFeedback getPromptFeedback()
com.example.everytalk.data.DataClass.ApiContentPart$Companion: ApiContentPart$Companion()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: AppViewModel$ExportedSettings(int,java.util.List,java.util.Set,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.window.PopupLayout: android.view.View getViewRoot()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: ThinkingConfig$$serializer()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component4()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component3()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.compose.material3.ModalBottomSheetDialogLayout$Api33Impl: void maybeUnregisterBackCallback(android.view.View,java.lang.Object)
com.example.everytalk.data.DataClass.WebSearchResult$Companion: WebSearchResult$Companion()
com.example.everytalk.data.DataClass.Part: void write$Self(com.example.everytalk.data.DataClass.Part,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String component2()
androidx.compose.ui.viewinterop.AndroidViewHolder: int getNestedScrollAxes()
com.example.everytalk.util.CorrectionIntensity: com.example.everytalk.util.CorrectionIntensity[] values()
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String toString()
androidx.compose.material3.DrawerValue: androidx.compose.material3.DrawerValue valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component13()
com.example.everytalk.data.DataClass.Part: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
androidx.compose.ui.platform.ViewLayer: void setCameraDistancePx(float)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getId$annotations()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: GenerationConfig$$serializer()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiConfig)
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String component2()
com.example.everytalk.data.DataClass.ThinkingConfig: com.example.everytalk.data.DataClass.ThinkingConfig copy$default(com.example.everytalk.data.DataClass.ThinkingConfig,java.lang.Boolean,java.lang.Integer,int,java.lang.Object)
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String component2()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getName$annotations()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
androidx.compose.ui.platform.AndroidComposeView: void setDensity(androidx.compose.ui.unit.Density)
androidx.compose.ui.window.PopupLayout: void getParams$ui_release$annotations()
androidx.compose.material3.ScaffoldLayoutContent: androidx.compose.material3.ScaffoldLayoutContent[] values()
kotlinx.coroutines.flow.SharingCommand: kotlinx.coroutines.flow.SharingCommand[] values()
com.example.everytalk.data.DataClass.ApiConfig: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ApiConfig,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.ui.text.font.FontWeightAdjustmentHelperApi31: int fontWeightAdjustment(android.content.Context)
com.example.everytalk.data.DataClass.ChatRequest: void getForceGoogleReasoningPrompt$annotations()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Content$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Content)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.spatial.RectManager getRectManager()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setReset(kotlin.jvm.functions.Function0)
com.example.everytalk.data.DataClass.PartsApiMessage: boolean equals(java.lang.Object)
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
com.example.everytalk.data.DataClass.ApiContentPart$Text: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ApiContentPart$Text,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
kotlin.reflect.KVariance: kotlin.reflect.KVariance valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: void set_viewTreeOwners(androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners)
androidx.compose.runtime.collection.MutableVectorKt: void throwNegativeIndexException(int)
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String getMarkdownWithKatex()
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType[] $values()
androidx.compose.ui.input.pointer.PointerInteropFilter$DispatchToViewState: androidx.compose.ui.input.pointer.PointerInteropFilter$DispatchToViewState[] values()
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.String toString()
androidx.compose.ui.input.pointer.PointerInputEventHandler: java.lang.Object invoke(androidx.compose.ui.input.pointer.PointerInputScope,kotlin.coroutines.Continuation)
com.example.everytalk.data.DataClass.ApiContentPart$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component5()
com.example.everytalk.data.DataClass.ApiConfig$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Candidate: java.util.List getSafetyRatings()
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String component1()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.window.PopupPositionProvider getPositionProvider()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.PartsApiMessage: java.util.List getParts()
com.example.everytalk.data.DataClass.ContentPart$Html: com.example.everytalk.data.DataClass.ContentPart$Html copy$default(com.example.everytalk.data.DataClass.ContentPart$Html,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.models.SelectedMediaItem$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.ui.screens.MainScreen.AiMessageOption: com.example.everytalk.ui.screens.MainScreen.AiMessageOption valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback: java.lang.String toString()
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String getData()
com.example.everytalk.data.DataClass.ChatRequest$$serializer: com.example.everytalk.data.DataClass.ChatRequest deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.util.CorrectionIntensity: com.example.everytalk.util.CorrectionIntensity valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component2()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion: ApiContentPart$FileUri$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: void write$Self$app_benchmark(com.example.everytalk.statecontroller.AppViewModel$ExportedSettings,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Message$$serializer: Message$$serializer()
com.example.everytalk.data.DataClass.GeminiApiRequest: GeminiApiRequest(java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.activity.EdgeToEdgeApi23: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.compose.ui.platform.AndroidComposeView: boolean getShowLayoutBounds()
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight[] values()
androidx.compose.ui.platform.AndroidComposeViewTranslationCallbackS: void setViewTranslationCallback(android.view.View)
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String toString()
androidx.compose.material3.tokens.ColorSchemeKeyTokens: androidx.compose.material3.tokens.ColorSchemeKeyTokens valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.Part$Text$$serializer: Part$Text$$serializer()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float getTopP()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
androidx.compose.runtime.InvalidationResult: androidx.compose.runtime.InvalidationResult[] values()
androidx.navigation.compose.ComposeNavigator: ComposeNavigator()
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
androidx.lifecycle.SavedStateHandlesVM: SavedStateHandlesVM()
com.example.everytalk.data.DataClass.ContentPart: java.lang.String getContentId()
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender[] $values()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.AbstractApiMessage: java.lang.String getRole()
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String component2()
com.example.everytalk.data.DataClass.GithubRelease$Companion: GithubRelease$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String component2()
androidx.compose.ui.platform.AbstractComposeView: boolean getShowLayoutBounds()
com.example.everytalk.data.DataClass.GithubRelease: void getHtmlUrl$annotations()
com.example.everytalk.data.DataClass.Candidate: java.lang.String toString()
com.example.everytalk.data.DataClass.GithubRelease: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map component13()
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
com.example.everytalk.models.SelectedMediaItem$ImageFromUri$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float getGuidanceScale()
androidx.compose.ui.platform.AndroidViewsHandler: java.util.HashMap getHolderToLayoutNode()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Message copy(java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List)
com.example.everytalk.data.DataClass.ContentPart$Code: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorSize(long)
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.unit.IntSize getPopupContentSize-bOM6tXw()
com.example.everytalk.data.DataClass.GithubRelease: GithubRelease(java.lang.String,java.lang.String,java.lang.String)
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
com.example.everytalk.data.DataClass.PartsApiMessage: int hashCode()
com.example.everytalk.data.DataClass.Content: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Content,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.savedstate.SavedStateRegistryOwner getSavedStateRegistryOwner()
com.example.everytalk.data.DataClass.Message: boolean component6()
com.example.everytalk.data.DataClass.Part$Text$Companion: Part$Text$Companion()
com.example.everytalk.data.DataClass.Candidate$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Candidate)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
androidx.navigation.compose.BackStackEntryIdViewModel: BackStackEntryIdViewModel(androidx.lifecycle.SavedStateHandle)
kotlin.text.RegexOption: kotlin.text.RegexOption[] values()
com.example.everytalk.data.DataClass.ContentPart$Html: ContentPart$Html(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Message: Message(java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
androidx.compose.ui.platform.AndroidComposeView: kotlin.jvm.functions.Function1 getConfigurationChangeObserver()
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: ApiContentPart$InlineData(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
com.example.everytalk.data.DataClass.ApiConfig$Companion: ApiConfig$Companion()
com.example.everytalk.data.DataClass.WebSearchResult: com.example.everytalk.data.DataClass.WebSearchResult copy$default(com.example.everytalk.data.DataClass.WebSearchResult,int,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.runtime.collection.MutableVectorKt: void throwListIndexOutOfBoundsException(int,int)
com.example.everytalk.data.DataClass.GeminiApiRequest: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.DataClass.Message: java.lang.String component7()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String toString()
androidx.core.view.WindowInsetsCompat$Impl34: boolean isVisible(int)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String getMimeType()
androidx.compose.ui.autofill.AutofillType: androidx.compose.ui.autofill.AutofillType[] values()
androidx.compose.ui.viewinterop.ViewFactoryHolder: androidx.compose.ui.platform.AbstractComposeView getSubCompositionView()
kotlinx.serialization.json.ClassDiscriminatorMode: kotlinx.serialization.json.ClassDiscriminatorMode valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getContent()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.platform.AndroidComposeViewAssistHelperMethodsO: void setClassName(android.view.ViewStructure,android.view.View)
androidx.compose.foundation.text.TextContextMenuItems: androidx.compose.foundation.text.TextContextMenuItems valueOf(java.lang.String)
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
io.ktor.util.date.WeekDay: io.ktor.util.date.WeekDay[] values()
com.example.everytalk.data.DataClass.Content$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
com.example.everytalk.data.DataClass.GithubRelease: int hashCode()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$FileUri deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.statecontroller.LRUCache: java.util.Set entrySet()
com.example.everytalk.data.DataClass.Content: com.example.everytalk.data.DataClass.Content copy(java.util.List,java.lang.String)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.compose.ui.unit.Density getDensity()
com.example.everytalk.data.DataClass.SafetySetting: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.GenerationConfig copy(java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component1()
com.example.everytalk.data.DataClass.PartsApiMessage: PartsApiMessage(java.lang.String,java.lang.String,java.util.List,java.lang.String,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: int hashCode()
androidx.compose.ui.platform.AndroidComposeView: android.view.View getView()
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.ui.window.SecureFlagPolicy: androidx.compose.ui.window.SecureFlagPolicy valueOf(java.lang.String)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
com.example.everytalk.data.DataClass.AbstractApiMessage: AbstractApiMessage()
androidx.compose.ui.layout.IntrinsicMinMax: androidx.compose.ui.layout.IntrinsicMinMax valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: java.util.List getAttachments()
com.example.everytalk.data.DataClass.ChatRequest: ChatRequest(int,java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.core.view.WindowInsetsCompat$TypeImpl34: int toPlatformType(int)
com.example.everytalk.data.DataClass.Part$InlineData: Part$InlineData(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.SafetyRating$Companion: SafetyRating$Companion()
com.example.everytalk.data.DataClass.ContentPart$Code: int hashCode()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component3()
androidx.compose.ui.text.style.ResolvedTextDirection: androidx.compose.ui.text.style.ResolvedTextDirection[] values()
com.example.everytalk.data.DataClass.GenerationConfig$Companion: GenerationConfig$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: GeminiApiResponse$$serializer()
com.example.everytalk.data.DataClass.Candidate: java.lang.String getFinishReason()
androidx.compose.ui.input.pointer.PointerEventPass: androidx.compose.ui.input.pointer.PointerEventPass valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Candidate: java.util.List component4()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners getViewTreeOwners()
androidx.lifecycle.ReportFragment: ReportFragment()
androidx.compose.ui.platform.OutlineVerificationHelper: void setPath(android.graphics.Outline,androidx.compose.ui.graphics.Path)
com.example.everytalk.data.DataClass.Message$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion: ApiContentPart$Text$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ChatRequest$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: SimpleTextApiMessage(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest: int hashCode()
com.example.everytalk.data.DataClass.ModalityType: java.lang.String getDisplayName()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.List getApiConfigs()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: ApiContentPart$InlineData(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Integer getMaxOutputTokens()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.PartsApiMessage: void getName$annotations()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
coil3.network.okhttp.internal.OkHttpNetworkFetcherServiceLoaderTarget: OkHttpNetworkFetcherServiceLoaderTarget()
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String getText()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Boolean getDefaultUseWebSearch()
com.example.everytalk.data.DataClass.Part$FileUri: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.Set getCustomProviders()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: com.example.everytalk.data.DataClass.GithubRelease deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: SimpleTextApiMessage(int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: long getLastMatrixRecalculationAnimationTime$ui_release()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: com.example.everytalk.data.DataClass.ApiContentPart$InlineData copy$default(com.example.everytalk.data.DataClass.ApiContentPart$InlineData,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ThinkingConfig$Companion: ThinkingConfig$Companion()
com.example.everytalk.data.DataClass.Message$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.material.ripple.UnprojectedRipple$MRadiusHelper: void setRadius(android.graphics.drawable.RippleDrawable,int)
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.material3.ColorResourceHelper: long getColor-WaAFU9c(android.content.Context,int)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: com.example.everytalk.data.DataClass.Part$FileUri deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.AndroidComposeView: int getImportantForAutofill()
com.example.everytalk.data.DataClass.ChatRequest: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Boolean component1()
androidx.compose.material3.internal.TextFieldType: androidx.compose.material3.internal.TextFieldType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
com.example.everytalk.data.DataClass.ApiConfig: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Message: java.lang.String getText()
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.draganddrop.AndroidDragAndDropManager getDragAndDropManager()
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.ChatRequest copy$default(com.example.everytalk.data.DataClass.ChatRequest,java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map,int,java.lang.Object)
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion: GeminiApiRequest$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.contentcapture.ContentCaptureEventType: androidx.compose.ui.contentcapture.ContentCaptureEventType[] values()
com.example.everytalk.statecontroller.LRUCache: java.util.Collection getValues()
androidx.compose.ui.platform.ViewLayer: float getCameraDistancePx()
androidx.compose.ui.graphics.layer.ViewLayer: void setInvalidated(boolean)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: ApiContentPart$FileUri$$serializer()
androidx.tracing.TraceApi29Impl: boolean isEnabled()
com.example.everytalk.data.DataClass.ChatRequest: void getCustomModelParameters$annotations()
com.example.everytalk.data.DataClass.Part$Text$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isTypeVisible(int)
androidx.compose.ui.platform.AndroidComposeView: void getShowLayoutBounds$annotations()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.compose.ui.viewinterop.AndroidViewHolder: java.lang.CharSequence getAccessibilityClassName()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: boolean equals(java.lang.Object)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.example.everytalk.data.DataClass.Message: java.util.List component12()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: void getMimeType$annotations()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String getId()
io.ktor.util.date.WeekDay: io.ktor.util.date.WeekDay valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
androidx.compose.ui.platform.AbstractComposeView: void getDisposeViewCompositionStrategy$annotations()
androidx.compose.runtime.ComposerKt: void composeImmediateRuntimeError(java.lang.String)
com.example.everytalk.data.DataClass.Message: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getProvider()
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: void setOnViewTreeOwnersAvailable(kotlin.jvm.functions.Function1)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: com.example.everytalk.data.DataClass.GeminiApiResponse deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.input.pointer.PointerInteropFilter$DispatchToViewState: androidx.compose.ui.input.pointer.PointerInteropFilter$DispatchToViewState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
okhttp3.Protocol: okhttp3.Protocol[] values()
com.example.everytalk.data.DataClass.WebSearchResult: WebSearchResult(int,int,java.lang.String,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component4()
androidx.compose.ui.graphics.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.Sender$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.compose.ui.viewinterop.AndroidViewHolder: void setLifecycleOwner(androidx.lifecycle.LifecycleOwner)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Part$FileUri)
androidx.compose.ui.platform.MotionEventVerifierApi29: boolean isValidMotionEvent(android.view.MotionEvent,int)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: com.example.everytalk.data.DataClass.ApiContentPart$FileUri copy$default(com.example.everytalk.data.DataClass.ApiContentPart$FileUri,java.lang.String,java.lang.String,int,java.lang.Object)
androidx.compose.ui.focus.FocusStateImpl: androidx.compose.ui.focus.FocusStateImpl valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Audio: com.example.everytalk.data.DataClass.ContentPart$Audio copy$default(com.example.everytalk.data.DataClass.ContentPart$Audio,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
androidx.compose.foundation.internal.InlineClassHelperKt: java.lang.Void throwIllegalArgumentExceptionForNullCheck(java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String getRole()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Content getContent()
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GeminiApiRequest copy(java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List)
okhttp3.internal.publicsuffix.PublicSuffixDatabase: PublicSuffixDatabase()
androidx.compose.material3.SnackbarResult: androidx.compose.material3.SnackbarResult valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$InlineData deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.runtime.PreconditionsKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Part$FileUri: Part$FileUri(int,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.Part$FileUri: int hashCode()
com.example.everytalk.data.DataClass.ChatRequest: void getApiKey$annotations()
androidx.compose.animation.EnterExitState: androidx.compose.animation.EnterExitState[] values()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: com.example.everytalk.data.DataClass.SimpleTextApiMessage deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.graphics.layer.ViewLayer: android.view.View getOwnerView()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String component2()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Object getToolChoice()
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse()
com.example.everytalk.data.DataClass.ApiConfig: float getTemperature()
com.example.everytalk.data.network.AppStreamEvent$Error$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String component3()
com.example.everytalk.data.DataClass.ContentPart: ContentPart(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String getBase64Data()
androidx.compose.runtime.PreconditionsKt: void throwIllegalArgumentException(java.lang.String)
androidx.compose.ui.node.LayoutNode$LayoutState: androidx.compose.ui.node.LayoutNode$LayoutState valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setInsets(int,androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getName()
androidx.core.view.WindowInsetsCompat$BuilderImpl34: WindowInsetsCompat$BuilderImpl34(androidx.core.view.WindowInsetsCompat)
androidx.compose.ui.viewinterop.AndroidViewHolder: void setRelease(kotlin.jvm.functions.Function0)
androidx.compose.ui.platform.ViewLayer: long getLayerId()
com.example.everytalk.data.DataClass.Message$Companion: Message$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.PromptFeedback: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String toString()
coil3.size.Scale: coil3.size.Scale[] values()
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GenerationConfig getGenerationConfig()
com.example.everytalk.data.DataClass.SafetyRating$Companion: SafetyRating$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.unit.LayoutDirection getLayoutDirection()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
io.ktor.util.date.Month: io.ktor.util.date.Month[] values()
com.example.everytalk.data.DataClass.ChatRequest$$serializer: ChatRequest$$serializer()
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
com.example.everytalk.models.MoreOptionsType: com.example.everytalk.models.MoreOptionsType[] values()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: com.example.everytalk.data.DataClass.GeminiApiRequest deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: com.example.everytalk.data.DataClass.ApiContentPart$FileUri copy(java.lang.String,java.lang.String)
androidx.compose.animation.core.PreconditionsKt: void throwIllegalStateException(java.lang.String)
androidx.compose.ui.node.LookaheadPassDelegate$PlacedState: androidx.compose.ui.node.LookaheadPassDelegate$PlacedState valueOf(java.lang.String)
androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction: androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction[] values()
com.example.everytalk.data.DataClass.Message: boolean component9()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ApiContentPart$Text: com.example.everytalk.data.DataClass.ApiContentPart$Text copy(java.lang.String)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
androidx.compose.foundation.text.selection.SelectionHandleAnchor: androidx.compose.foundation.text.selection.SelectionHandleAnchor valueOf(java.lang.String)
com.example.everytalk.data.DataClass.AbstractApiMessage: void write$Self(com.example.everytalk.data.DataClass.AbstractApiMessage,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.network.AppStreamEvent$Content$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.AndroidComposeView: android.view.View findViewByAccessibilityIdTraversal(int)
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String getSnippet()
com.example.everytalk.data.DataClass.ChatRequest$Companion: ChatRequest$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl20: boolean systemBarVisibilityEquals(int,int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
org.slf4j.event.Level: org.slf4j.event.Level valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
com.example.everytalk.data.DataClass.SafetySetting: SafetySetting(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback$Companion: PromptFeedback$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AutofillTree getAutofillTree()
androidx.compose.foundation.internal.InlineClassHelperKt: java.lang.Void throwIllegalStateExceptionForNullCheck(java.lang.String)
androidx.compose.ui.platform.actionmodecallback.MenuItemOption: androidx.compose.ui.platform.actionmodecallback.MenuItemOption[] values()
com.example.everytalk.data.DataClass.GithubRelease: com.example.everytalk.data.DataClass.GithubRelease copy$default(com.example.everytalk.data.DataClass.GithubRelease,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.example.everytalk.data.DataClass.PromptFeedback: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.PromptFeedback,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map getCustomModelParameters()
com.example.everytalk.data.DataClass.ContentPart$Html: int hashCode()
com.example.everytalk.data.DataClass.GenerationConfig$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand: androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SafetyRating: SafetyRating(java.lang.String,java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
androidx.core.view.WindowInsetsCompat$Impl30: boolean isVisible(int)
com.example.everytalk.data.DataClass.WebSearchResult: int component1()
com.example.everytalk.data.DataClass.PromptFeedback$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String getContentId()
com.example.everytalk.data.DataClass.Part$Text: Part$Text(int,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String component2()
org.slf4j.helpers.Reporter$Level: org.slf4j.helpers.Reporter$Level valueOf(java.lang.String)
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax[] values()
com.example.everytalk.data.DataClass.ApiContentPart$Text: int hashCode()
com.example.everytalk.data.DataClass.ChatRequest: java.util.List getMessages()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setOnDensityChanged$ui_release(kotlin.jvm.functions.Function1)
com.example.everytalk.data.DataClass.ApiContentPart$Companion: ApiContentPart$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: void getFontLoader$annotations()
com.example.everytalk.data.DataClass.Part$Text: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Part$Text,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.ThinkingConfig$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.WebSearchResult: int getIndex()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String getData()
com.example.everytalk.data.DataClass.ContentPart$Audio: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse(java.util.List,com.example.everytalk.data.DataClass.PromptFeedback,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.compose.ui.graphics.layer.ViewLayer: void setCanUseCompositingLayer$ui_graphics_release(boolean)
androidx.compose.ui.viewinterop.AndroidViewHolder: void setUpdate(kotlin.jvm.functions.Function0)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String component1()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean getForceGoogleReasoningPrompt()
androidx.compose.ui.viewinterop.AndroidViewHolder: android.view.View getInteropView()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ApiContentPart$InlineData,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Message: long component8()
com.example.everytalk.data.DataClass.ChatRequest: java.util.List component9()
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion: GeminiApiResponse$Companion()
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig(int,java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.foundation.text.KeyCommand: androidx.compose.foundation.text.KeyCommand valueOf(java.lang.String)
androidx.compose.ui.text.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl34: androidx.core.graphics.Insets getInsets(int)
kotlinx.serialization.json.JsonObject$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: PromptFeedback$$serializer()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.SafetySetting$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SafetyRating: com.example.everytalk.data.DataClass.SafetyRating copy(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float getTopP()
com.example.everytalk.data.DataClass.Message: java.lang.String getName()
com.example.everytalk.data.DataClass.Part$InlineData: com.example.everytalk.data.DataClass.Part$InlineData copy$default(com.example.everytalk.data.DataClass.Part$InlineData,java.lang.String,java.lang.String,int,java.lang.Object)
androidx.compose.ui.platform.ViewLayerContainer: void dispatchGetDisplayList()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String getHtmlUrl()
com.example.everytalk.data.DataClass.Part$FileUri$Companion: Part$FileUri$Companion()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ChatRequest: ChatRequest(java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map)
androidx.compose.ui.platform.AndroidComposeView: void getTextInputService$annotations()
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation: LegacySavedStateHandleController$OnRecreation()
com.example.everytalk.data.DataClass.SafetyRating$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Sender$Companion: Sender$Companion()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.compose.ui.platform.AbstractComposeView: void getShowLayoutBounds$annotations()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.GenerationConfig getGenerationConfig()
androidx.compose.ui.viewinterop.AndroidViewHolder: androidx.compose.ui.node.OwnerSnapshotObserver getSnapshotObserver()
com.example.everytalk.data.DataClass.Part$InlineData$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AndroidAutofillManager get_autofillManager$ui_release()
com.example.everytalk.data.DataClass.Part$InlineData: com.example.everytalk.data.DataClass.Part$InlineData copy(java.lang.String,java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners get_viewTreeOwners()
androidx.core.view.WindowInsetsCompat$BuilderImpl30: void setInsets(int,androidx.core.graphics.Insets)
androidx.compose.ui.node.LayoutNode$LayoutState: androidx.compose.ui.node.LayoutNode$LayoutState[] values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.contentcapture.AndroidContentCaptureManager getContentCaptureManager$ui_release()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Part$InlineData: Part$InlineData(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.node.LayoutNode$UsageByParent: androidx.compose.ui.node.LayoutNode$UsageByParent[] values()
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig(java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
com.example.everytalk.models.ImageSourceOption: com.example.everytalk.models.ImageSourceOption[] values()
androidx.compose.ui.viewinterop.AndroidViewHolder: void setOnModifierChanged$ui_release(kotlin.jvm.functions.Function1)
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Content component1()
com.example.everytalk.data.DataClass.ChatRequest: void getProvider$annotations()
androidx.compose.ui.node.LayoutNode$UsageByParent: androidx.compose.ui.node.LayoutNode$UsageByParent valueOf(java.lang.String)
androidx.compose.ui.text.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.Message: java.lang.String getRole()
androidx.compose.ui.unit.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy: androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy[] values()
com.example.everytalk.data.DataClass.ApiContentPart$Text: com.example.everytalk.data.DataClass.ApiContentPart$Text copy$default(com.example.everytalk.data.DataClass.ApiContentPart$Text,java.lang.String,int,java.lang.Object)
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.unit.LayoutDirection getParentLayoutDirection()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getId()
com.example.everytalk.data.DataClass.SafetyRating: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.SafetyRating,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.ui.window.PopupLayout: void setLayoutDirection(int)
androidx.compose.ui.viewinterop.ViewFactoryHolder: kotlin.jvm.functions.Function1 getUpdateBlock()
com.example.everytalk.data.DataClass.ChatRequest: ChatRequest(java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: ApiContentPart$FileUri(java.lang.String,java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
com.example.everytalk.data.DataClass.WebSearchResult: WebSearchResult(int,java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage: com.example.everytalk.data.DataClass.PartsApiMessage copy(java.lang.String,java.lang.String,java.util.List,java.lang.String)
com.example.everytalk.data.DataClass.Message: java.lang.String toString()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: com.example.everytalk.data.DataClass.Part$InlineData deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.AndroidViewsHandler: java.util.HashMap getLayoutNodeToHolder()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: com.example.everytalk.data.DataClass.ThinkingConfig deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Part$Text)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean component11()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String component1()
androidx.compose.ui.text.AnnotationType: androidx.compose.ui.text.AnnotationType[] values()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: com.example.everytalk.statecontroller.AppViewModel$ExportedSettings copy$default(com.example.everytalk.statecontroller.AppViewModel$ExportedSettings,java.util.List,java.util.Set,int,java.lang.Object)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.compose.animation.core.MutatePriority: androidx.compose.animation.core.MutatePriority valueOf(java.lang.String)
androidx.compose.animation.EnterExitState: androidx.compose.animation.EnterExitState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion: ApiContentPart$InlineData$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.text.AnnotationType: androidx.compose.ui.text.AnnotationType valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: boolean getScrollCaptureInProgress$ui_release()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ThinkingConfig)
kotlinx.serialization.json.internal.WriteMode: kotlinx.serialization.json.internal.WriteMode[] values()
com.example.everytalk.data.DataClass.Part$InlineData$Companion: Part$InlineData$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: com.example.everytalk.data.DataClass.PromptFeedback deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.layout.Placeable$PlacementScope getPlacementScope()
androidx.core.view.WindowInsetsCompat$Impl: boolean isVisible(int)
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax[] values()
coil3.decode.DataSource: coil3.decode.DataSource[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
com.example.everytalk.data.DataClass.PromptFeedback: java.util.List getSafetyRatings()
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String component1()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.AbstractApiMessage: kotlinx.serialization.KSerializer _init_$_anonymous_()
com.example.everytalk.data.DataClass.AbstractApiMessage: AbstractApiMessage(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.foundation.MutatePriority: androidx.compose.foundation.MutatePriority valueOf(java.lang.String)
androidx.compose.animation.core.RepeatMode: androidx.compose.animation.core.RepeatMode[] values()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Integer component3()
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig()
androidx.compose.ui.graphics.Path$Direction: androidx.compose.ui.graphics.Path$Direction valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String component2()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.font.FontFamily$Resolver getFontFamilyResolver()
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String component4()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component6()
androidx.activity.EdgeToEdgeApi21: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: com.example.everytalk.statecontroller.AppViewModel$ExportedSettings copy(java.util.List,java.util.Set)
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig(java.lang.Boolean,java.lang.Integer)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: boolean equals(java.lang.Object)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
com.example.everytalk.data.DataClass.Part$Text: com.example.everytalk.data.DataClass.Part$Text copy(java.lang.String)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part$FileUri$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean getUseWebSearch()
coil3.util.Logger$Level: coil3.util.Logger$Level valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: java.lang.String component1()
androidx.compose.ui.unit.ConstraintsKt: java.lang.Void throwInvalidConstraintsSizeException(int)
androidx.compose.material3.ModalBottomSheetDialogLayout$Api33Impl: android.window.OnBackInvokedCallback createBackCallback(kotlin.jvm.functions.Function0)
androidx.compose.ui.viewinterop.AndroidViewHolder: kotlin.jvm.functions.Function1 getOnModifierChanged$ui_release()
androidx.compose.ui.viewinterop.ViewFactoryHolder: kotlin.jvm.functions.Function1 getReleaseBlock()
androidx.compose.ui.platform.AndroidComposeView: void setFontFamilyResolver(androidx.compose.ui.text.font.FontFamily$Resolver)
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String getProbability()
androidx.compose.ui.internal.InlineClassHelperKt: void throwIndexOutOfBoundsException(java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender[] values()
androidx.compose.ui.platform.AndroidComposeView: boolean getHasPendingMeasureOrLayout()
com.example.everytalk.data.DataClass.Message: java.lang.String component4()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.SoftwareKeyboardController getSoftwareKeyboardController()
com.example.everytalk.data.DataClass.WebSearchResult: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.WebSearchResult,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.foundation.text.HandleState: androidx.compose.foundation.text.HandleState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion: GeminiApiResponse$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Candidate$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ContentPart: ContentPart(java.lang.String,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.GeminiApiResponse copy$default(com.example.everytalk.data.DataClass.GeminiApiResponse,java.util.List,com.example.everytalk.data.DataClass.PromptFeedback,int,java.lang.Object)
com.example.everytalk.models.MoreOptionsType: com.example.everytalk.models.MoreOptionsType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Integer component2()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.compose.ui.focus.CustomDestinationResult: androidx.compose.ui.focus.CustomDestinationResult[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
com.example.everytalk.data.DataClass.SafetySetting: com.example.everytalk.data.DataClass.SafetySetting copy$default(com.example.everytalk.data.DataClass.SafetySetting,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.Candidate: Candidate(int,com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.viewinterop.AndroidViewHolder: void setSavedStateRegistryOwner(androidx.savedstate.SavedStateRegistryOwner)
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: ApiContentPart$Text$$serializer()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Part$Text: java.lang.String toString()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion: ApiContentPart$InlineData$Companion()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.RootForTest getRootForTest()
kotlin.DeprecationLevel: kotlin.DeprecationLevel[] values()
kotlin.time.DurationUnit: kotlin.time.DurationUnit[] values()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidAccessibilityManager getAccessibilityManager()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getId()
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy: androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy valueOf(java.lang.String)
androidx.graphics.path.PathIteratorPreApi34Impl: boolean internalPathIteratorHasNext(long)
androidx.compose.ui.platform.AbstractComposeView: void setShowLayoutBounds(boolean)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float component10()
com.example.everytalk.data.DataClass.GenerationConfig: void getTemperature$annotations()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GeminiApiResponse)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
kotlin.LazyThreadSafetyMode: kotlin.LazyThreadSafetyMode valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
com.example.everytalk.data.DataClass.Part$FileUri: Part$FileUri(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String toString()
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.material3.MinimumInteractiveModifier: MinimumInteractiveModifier()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
com.example.everytalk.data.DataClass.ContentPart$Code: com.example.everytalk.data.DataClass.ContentPart$Code copy$default(com.example.everytalk.data.DataClass.ContentPart$Code,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.GenerationConfig component8()
