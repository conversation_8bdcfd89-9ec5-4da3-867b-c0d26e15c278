package com.example.everytalk.util

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp

/**
 * 增强的代码块渲染器
 * 专门优化AI输出中的代码显示
 */
object EnhancedCodeRenderer {
    
    // 代码块样式
    private val codeBlockStyle = SpanStyle(
        fontFamily = FontFamily.Monospace,
        fontSize = 13.sp,
        color = Color(0xFF2E3440),
        background = Color(0xFFF8F9FA)
    )
    
    // 行内代码样式
    private val inlineCodeStyle = SpanStyle(
        fontFamily = FontFamily.Monospace,
        fontSize = 13.sp,
        color = Color(0xFFD73A49),
        background = Color(0xFFF6F8FA)
    )
    
    // 语言标签样式
    private val languageStyle = SpanStyle(
        fontSize = 11.sp,
        color = Color(0xFF586069),
        fontWeight = FontWeight.Medium
    )
    
    // 行号样式
    private val lineNumberStyle = SpanStyle(
        fontSize = 11.sp,
        color = Color(0xFF959DA5),
        fontFamily = FontFamily.Monospace
    )
    
    // 关键字高亮颜色映射
    private val keywordColors = mapOf(
        // Python
        "def" to Color(0xFF0000FF),
        "class" to Color(0xFF0000FF),
        "import" to Color(0xFF0000FF),
        "from" to Color(0xFF0000FF),
        "if" to Color(0xFF0000FF),
        "else" to Color(0xFF0000FF),
        "elif" to Color(0xFF0000FF),
        "for" to Color(0xFF0000FF),
        "while" to Color(0xFF0000FF),
        "return" to Color(0xFF0000FF),
        "try" to Color(0xFF0000FF),
        "except" to Color(0xFF0000FF),
        "finally" to Color(0xFF0000FF),
        
        // JavaScript/TypeScript
        "function" to Color(0xFF0000FF),
        "const" to Color(0xFF0000FF),
        "let" to Color(0xFF0000FF),
        "var" to Color(0xFF0000FF),
        "async" to Color(0xFF0000FF),
        "await" to Color(0xFF0000FF),
        
        // Java/Kotlin
        "public" to Color(0xFF0000FF),
        "private" to Color(0xFF0000FF),
        "protected" to Color(0xFF0000FF),
        "static" to Color(0xFF0000FF),
        "final" to Color(0xFF0000FF),
        "abstract" to Color(0xFF0000FF),
        "interface" to Color(0xFF0000FF),
        "extends" to Color(0xFF0000FF),
        "implements" to Color(0xFF0000FF),
        
        // 通用
        "true" to Color(0xFF008000),
        "false" to Color(0xFF008000),
        "null" to Color(0xFF008000),
        "undefined" to Color(0xFF008000)
    )
    
    /**
     * 渲染代码块
     */
    fun renderCodeBlock(code: String, language: String? = null): AnnotatedString {
        return buildAnnotatedString {
            // 添加语言标签
            if (!language.isNullOrBlank()) {
                withStyle(languageStyle) {
                    append("[$language]")
                }
                append("\n")
            }
            
            val lines = code.split('\n')
            val maxLineNumberWidth = lines.size.toString().length
            
            for (i in lines.indices) {
                val line = lines[i]
                
                // 添加行号
                withStyle(lineNumberStyle) {
                    val lineNumber = (i + 1).toString().padStart(maxLineNumberWidth)
                    append("$lineNumber │ ")
                }
                
                // 渲染代码行
                renderCodeLine(line, language)
                
                if (i < lines.size - 1) {
                    append("\n")
                }
            }
        }
    }
    
    /**
     * 渲染行内代码
     */
    fun renderInlineCode(code: String): AnnotatedString {
        return buildAnnotatedString {
            withStyle(inlineCodeStyle) {
                append(code)
            }
        }
    }
    
    /**
     * 渲染单行代码
     */
    private fun AnnotatedString.Builder.renderCodeLine(line: String, language: String?) {
        if (line.trim().isEmpty()) {
            append(line)
            return
        }
        
        // 简单的语法高亮
        val tokens = tokenizeLine(line)
        
        for (token in tokens) {
            val style = when {
                // 字符串
                token.startsWith("\"") && token.endsWith("\"") -> 
                    codeBlockStyle.copy(color = Color(0xFF032F62))
                token.startsWith("'") && token.endsWith("'") -> 
                    codeBlockStyle.copy(color = Color(0xFF032F62))
                
                // 注释
                token.startsWith("//") || token.startsWith("#") -> 
                    codeBlockStyle.copy(color = Color(0xFF6A737D))
                
                // 数字
                token.matches(Regex("\\d+(\\.\\d+)?")) -> 
                    codeBlockStyle.copy(color = Color(0xFF005CC5))
                
                // 关键字
                keywordColors.containsKey(token) -> 
                    codeBlockStyle.copy(color = keywordColors[token]!!)
                
                else -> codeBlockStyle
            }
            
            withStyle(style) {
                append(token)
            }
        }
    }
    
    /**
     * 简单的代码分词
     */
    private fun tokenizeLine(line: String): List<String> {
        val tokens = mutableListOf<String>()
        var current = ""
        var inString = false
        var stringChar = ' '
        var inComment = false
        
        for (i in line.indices) {
            val char = line[i]
            
            when {
                // 处理字符串
                !inComment && (char == '"' || char == '\'') -> {
                    if (!inString) {
                        if (current.isNotEmpty()) {
                            tokens.addAll(splitNonString(current))
                            current = ""
                        }
                        inString = true
                        stringChar = char
                        current += char
                    } else if (char == stringChar) {
                        current += char
                        tokens.add(current)
                        current = ""
                        inString = false
                    } else {
                        current += char
                    }
                }
                
                // 处理注释
                !inString && i < line.length - 1 && 
                (line.substring(i, i + 2) == "//" || line.substring(i, i + 1) == "#") -> {
                    if (current.isNotEmpty()) {
                        tokens.addAll(splitNonString(current))
                        current = ""
                    }
                    tokens.add(line.substring(i))
                    break
                }
                
                inString || inComment -> {
                    current += char
                }
                
                char.isWhitespace() -> {
                    if (current.isNotEmpty()) {
                        tokens.addAll(splitNonString(current))
                        current = ""
                    }
                    tokens.add(char.toString())
                }
                
                else -> {
                    current += char
                }
            }
        }
        
        if (current.isNotEmpty()) {
            if (inString || inComment) {
                tokens.add(current)
            } else {
                tokens.addAll(splitNonString(current))
            }
        }
        
        return tokens
    }
    
    /**
     * 分割非字符串内容
     */
    private fun splitNonString(text: String): List<String> {
        val operators = setOf("=", "+", "-", "*", "/", "%", "!", "<", ">", "&", "|", "^", "~")
        val delimiters = setOf("(", ")", "[", "]", "{", "}", ",", ";", ":", ".")
        
        val result = mutableListOf<String>()
        var current = ""
        
        for (char in text) {
            when {
                operators.contains(char.toString()) || delimiters.contains(char.toString()) -> {
                    if (current.isNotEmpty()) {
                        result.add(current)
                        current = ""
                    }
                    result.add(char.toString())
                }
                else -> {
                    current += char
                }
            }
        }
        
        if (current.isNotEmpty()) {
            result.add(current)
        }
        
        return result
    }
    
    /**
     * 检测语言类型
     */
    fun detectLanguage(code: String): String? {
        val trimmedCode = code.trim()
        
        return when {
            trimmedCode.contains("def ") && trimmedCode.contains(":") -> "python"
            trimmedCode.contains("function ") || trimmedCode.contains("const ") -> "javascript"
            trimmedCode.contains("public class ") || trimmedCode.contains("private ") -> "java"
            trimmedCode.contains("fun ") || trimmedCode.contains("class ") -> "kotlin"
            trimmedCode.contains("<!DOCTYPE") || trimmedCode.contains("<html") -> "html"
            trimmedCode.contains("SELECT ") || trimmedCode.contains("FROM ") -> "sql"
            trimmedCode.contains("#include") || trimmedCode.contains("int main") -> "cpp"
            else -> null
        }
    }
}