# Enhanced KaTeX and Markdown Formatting Instructions

# Enhanced formatting instruction for all models
UNIFIED_ENHANCED_FORMATTING_INSTRUCTION = """CRITICAL FORMATTING GUIDELINES:

🔢 MATHEMATICAL EXPRESSIONS:
   - Inline math: $expression$ (paired dollar signs, no spaces inside)
   - Display math: $$expression$$ (separate lines, paired, no spaces inside)
   - Fractions: \\frac{numerator}{denominator} (always use braces)
   - Roots: \\sqrt{content}, \\sqrt[n]{content} (use braces for content)
   - Functions: \\sin, \\cos, \\tan, \\log, \\ln, \\exp (use backslash)
   - Subscripts/Superscripts: x_{sub}^{sup} (always use braces, even for single chars)
   - Matrices: \\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}
   - Align equations: \\begin{aligned} ... \\end{aligned}
   - Greek letters: \\alpha, \\beta, \\gamma, \\delta, \\theta, \\lambda, \\pi, \\sigma
   - Operators: \\times, \\div, \\pm, \\mp, \\cdot, \\leq, \\geq, \\neq, \\approx
   - Sets: \\in, \\notin, \\subset, \\supset, \\cup, \\cap, \\emptyset
   - Arrows: \\rightarrow, \\leftarrow, \\Rightarrow, \\Leftarrow
   - Calculus: \\int, \\sum, \\prod, \\lim, \\partial, \\nabla, \\infty
   - Spacing: Use \\, for thin space, \\; for medium space, \\quad for large space

📊 TABLE FORMATTING:
   | Column 1 | Column 2 | Column 3 |
   |----------|----------|----------|
   | Data 1   | Data 2   | Data 3   |
   
   Rules: Always start/end with |, use --- for headers

💻 CODE BLOCKS:
   ```language
   code content here
   ```
   
   Rules: Specify language, ensure closing backticks

📋 LISTS:
   - Unordered: use - consistently
   1. Ordered: use sequential numbers
   
   Rules: Proper spacing, no duplicate markers

⚠️ CRITICAL: Maintain mathematical integrity, table structure, and code block completeness across all output chunks.

🎯 ADVANCED MATH FORMATTING:
   - Complex fractions: \\frac{\\frac{a}{b}}{\\frac{c}{d}} or \\cfrac{a}{b+\\cfrac{c}{d}}
   - Binomial coefficients: \\binom{n}{k} or \\choose
   - Integrals: \\int_{a}^{b}, \\iint, \\iiint, \\oint
   - Summations: \\sum_{i=1}^{n}, \\prod_{i=1}^{n}
   - Limits: \\lim_{x \\to \\infty}, \\lim_{h \\to 0}
   - Derivatives: \\frac{d}{dx}, \\frac{\\partial}{\\partial x}
   - Vectors: \\vec{v}, \\mathbf{v}, \\hat{i}, \\hat{j}, \\hat{k}
   - Matrices: \\begin{bmatrix}, \\begin{vmatrix}, \\begin{Vmatrix}
   - Cases: \\begin{cases} x & \\text{if } x > 0 \\\\ -x & \\text{if } x < 0 \\end{cases}
   - Aligned equations: \\begin{aligned} f(x) &= ax^2 + bx + c \\\\ &= a(x-h)^2 + k \\end{aligned}

🔧 MATH QUALITY CHECKS:
   - Ensure all braces {} are properly paired
   - Use \\text{} for text within math expressions
   - Avoid spaces inside $ $ delimiters
   - Use proper alignment in multi-line equations
   - Ensure subscripts and superscripts use braces
   - Check Greek letter spelling and capitalization"""

KATEX_FORMATTING_INSTRUCTION = UNIFIED_ENHANCED_FORMATTING_INSTRUCTION

DEEPSEEK_KATEX_FORMATTING_INSTRUCTION = UNIFIED_ENHANCED_FORMATTING_INSTRUCTION + """

🧠 DEEPSEEK-SPECIFIC:
   - Reasoning: Use <think>...</think> for internal reasoning
   - Math focus: Excel at complex mathematical derivations
   - Code optimization: Provide efficient implementations"""

QWEN_KATEX_FORMATTING_INSTRUCTION = UNIFIED_ENHANCED_FORMATTING_INSTRUCTION + """

🌐 QWEN-SPECIFIC:
   - Multilingual: Support Chinese mathematical notation
   - Search integration: Use built-in search capabilities
   - Code generation: Focus on practical implementations"""

# Enhanced Gemini-specific formatting instruction
GEMINI_ENHANCED_FORMATTING_INSTRUCTION = UNIFIED_ENHANCED_FORMATTING_INSTRUCTION + """

🔍 GEMINI-SPECIFIC:
   - Citations: [1](url) format for web sources
   - Thinking: Use structured reasoning
   - Search integration: Reference sources naturally"""

# 数学公式质量检查和优化工具
def validate_and_enhance_math_expressions(text: str) -> str:
    """
    验证和增强数学表达式的质量

    Args:
        text: 包含数学表达式的文本

    Returns:
        优化后的文本
    """
    import re

    # 1. 检查数学分隔符平衡
    single_dollar_count = text.count('$') - text.count('$$') * 2
    if single_dollar_count % 2 != 0:
        text += '$'  # 添加缺失的分隔符

    # 2. 优化常见的数学表达式
    math_fixes = {
        # 分数优化
        r'(\d+)/(\d+)': r'\\frac{\1}{\2}',
        r'([a-zA-Z]+)/([a-zA-Z]+)': r'\\frac{\1}{\2}',

        # 指数和下标优化
        r'([a-zA-Z0-9])\^([a-zA-Z0-9])': r'\1^{\2}',
        r'([a-zA-Z0-9])_([a-zA-Z0-9])': r'\1_{\2}',

        # 函数优化
        r'\\sin\s*\(': r'\\sin(',
        r'\\cos\s*\(': r'\\cos(',
        r'\\tan\s*\(': r'\\tan(',
        r'\\log\s*\(': r'\\log(',
        r'\\ln\s*\(': r'\\ln(',

        # 根号优化
        r'sqrt\(([^)]+)\)': r'\\sqrt{\1}',

        # 求和和积分优化
        r'sum\s*\(': r'\\sum(',
        r'int\s*\(': r'\\int(',

        # 希腊字母优化
        r'\\alpha\s+': r'\\alpha ',
        r'\\beta\s+': r'\\beta ',
        r'\\gamma\s+': r'\\gamma ',
        r'\\delta\s+': r'\\delta ',
        r'\\theta\s+': r'\\theta ',
        r'\\lambda\s+': r'\\lambda ',
        r'\\pi\s+': r'\\pi ',
        r'\\sigma\s+': r'\\sigma ',
    }

    for pattern, replacement in math_fixes.items():
        text = re.sub(pattern, replacement, text)

    # 3. 清理数学表达式内的多余空格
    def clean_math_spaces(match):
        math_content = match.group(1)
        # 移除多余空格但保留必要的间距
        cleaned = re.sub(r'\s+', ' ', math_content.strip())
        return f'${cleaned}$'

    text = re.sub(r'\$([^$]+)\$', clean_math_spaces, text)

    return text

def get_enhanced_formatting_instruction(model_type: str = "general") -> str:
    """
    获取增强的格式化指令

    Args:
        model_type: 模型类型

    Returns:
        格式化指令
    """
    base_instruction = UNIFIED_ENHANCED_FORMATTING_INSTRUCTION

    if model_type == "deepseek":
        return DEEPSEEK_KATEX_FORMATTING_INSTRUCTION
    elif model_type == "qwen":
        return QWEN_KATEX_FORMATTING_INSTRUCTION
    elif model_type == "gemini":
        return GEMINI_ENHANCED_FORMATTING_INSTRUCTION
    else:
        return base_instruction