# Enhanced KaTeX and Markdown Formatting Instructions

# Enhanced formatting instruction for all models
UNIFIED_ENHANCED_FORMATTING_INSTRUCTION = """CRITICAL FORMATTING GUIDELINES:

🔢 MATHEMATICAL EXPRESSIONS:
   - Inline math: $expression$ (paired dollar signs)
   - Display math: $$expression$$ (separate lines, paired)
   - Fractions: \\frac{num}{den} (always use braces)
   - Roots: \\sqrt{content}, \\sqrt[n]{content}
   - Functions: \\sin, \\cos, \\log, \\ln (use backslash)
   - Subscripts/Superscripts: x_{sub}^{sup} (use braces)
   - Matrices: \\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}
   - Align equations: \\begin{aligned} ... \\end{aligned}

📊 TABLE FORMATTING:
   | Column 1 | Column 2 | Column 3 |
   |----------|----------|----------|
   | Data 1   | Data 2   | Data 3   |
   
   Rules: Always start/end with |, use --- for headers

💻 CODE BLOCKS:
   ```language
   code content here
   ```
   
   Rules: Specify language, ensure closing backticks

📋 LISTS:
   - Unordered: use - consistently
   1. Ordered: use sequential numbers
   
   Rules: Proper spacing, no duplicate markers

⚠️ CRITICAL: Maintain mathematical integrity, table structure, and code block completeness across all output chunks."""

KATEX_FORMATTING_INSTRUCTION = UNIFIED_ENHANCED_FORMATTING_INSTRUCTION

DEEPSEEK_KATEX_FORMATTING_INSTRUCTION = UNIFIED_ENHANCED_FORMATTING_INSTRUCTION + """

🧠 DEEPSEEK-SPECIFIC:
   - Reasoning: Use <think>...</think> for internal reasoning
   - Math focus: Excel at complex mathematical derivations
   - Code optimization: Provide efficient implementations"""

QWEN_KATEX_FORMATTING_INSTRUCTION = UNIFIED_ENHANCED_FORMATTING_INSTRUCTION + """

🌐 QWEN-SPECIFIC:
   - Multilingual: Support Chinese mathematical notation
   - Search integration: Use built-in search capabilities
   - Code generation: Focus on practical implementations"""

# Enhanced Gemini-specific formatting instruction
GEMINI_ENHANCED_FORMATTING_INSTRUCTION = UNIFIED_ENHANCED_FORMATTING_INSTRUCTION + """

🔍 GEMINI-SPECIFIC:
   - Citations: [1](url) format for web sources
   - Thinking: Use structured reasoning
   - Search integration: Reference sources naturally"""