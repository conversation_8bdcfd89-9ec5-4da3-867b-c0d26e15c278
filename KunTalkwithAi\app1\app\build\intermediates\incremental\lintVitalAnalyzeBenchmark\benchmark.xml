<variant
    name="benchmark"
    useSupportLibraryVectorDrawables="true"
    package="com.example.everytalk"
    minSdkVersion="27"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="build\intermediates\merged_manifest\benchmark\processBenchmarkMainManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.11.1;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\benchmark\lintVitalAnalyzeBenchmark\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.13\transforms\91e29260bc8a53f56ee6a629b7428ba3\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\benchmark\java;src\main\kotlin;src\benchmark\kotlin"
        resDirectories="src\main\res;src\benchmark\res"
        assetsDirectories="src\main\assets;src\benchmark\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\benchmark\compileBenchmarkJavaWithJavac\classes;build\tmp\kotlin-classes\benchmark;build\kotlinToolingMetadata;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\benchmark\processBenchmarkResources\R.jar"
      type="MAIN"
      applicationId="com.example.everytalk"
      generatedSourceFolders="build\generated\ap_generated_sources\benchmark\out"
      generatedResourceFolders="build\generated\res\resValues\benchmark"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.13\transforms\91e29260bc8a53f56ee6a629b7428ba3\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
