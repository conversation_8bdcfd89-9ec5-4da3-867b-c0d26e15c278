<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.1" type="partial_results">
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.intent.action.VIEW (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/everytalk/ui/screens/BubbleMain/Main/BubbleContentTypes.kt"
                            line="437"
                            column="58"
                            startOffset="22247"
                            endLine="437"
                            endColumn="84"
                            endOffset="22273"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/everytalk/ui/screens/BubbleMain/Main/BubbleContentTypes.kt"
                            line="476"
                            column="58"
                            startOffset="24463"
                            endLine="476"
                            endColumn="84"
                            endOffset="24489"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.everytalk.statecontroller.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="300"
            endLine="8"
            endColumn="24"
            endOffset="312"/>
        <location id="R.color.ktalk"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/ktalk.xml"
            line="3"
            column="12"
            startOffset="64"
            endLine="3"
            endColumn="24"
            endOffset="76"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="64"
            endLine="3"
            endColumn="29"
            endOffset="81"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="112"
            endLine="4"
            endColumn="29"
            endOffset="129"/>
        <location id="R.color.purple_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="160"
            endLine="5"
            endColumn="29"
            endOffset="177"/>
        <location id="R.color.teal_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="208"
            endLine="6"
            endColumn="27"
            endOffset="223"/>
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="254"
            endLine="7"
            endColumn="27"
            endOffset="269"/>
        <location id="R.color.white"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="343"
            endLine="9"
            endColumn="24"
            endOffset="355"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="74"
            endColumn="10"
            endOffset="4939"/>
        <location id="R.mipmap.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.webp"/>
        <location id="R.mipmap.kztalk"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/kztalk.webp"/>
        <location id="R.string.code_copied"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="165"
            endLine="5"
            endColumn="31"
            endOffset="183"/>
        <location id="R.string.copy"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="212"
            endLine="6"
            endColumn="24"
            endOffset="223"/>
        <location id="R.string.no_app_found_for_link"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="366"
            endLine="9"
            endColumn="41"
            endOffset="394"/>
        <location id="R.string.no_permission_open_link"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="303"
            endLine="8"
            endColumn="43"
            endOffset="333"/>
        <entry
            name="model"
            string="color[ic_launcher_background(U),purple_200(D),purple_500(D),purple_700(D),teal_200(D),teal_700(D),black(D),white(D),ktalk(D)],drawable[ic_foreground_logo(U),ic_launcher_background(D),ic_launcher_foreground(U)],id[webview_template_tag_key(D)],mipmap[ic_launcher(U),ic_launcher_round(U),ic_launcher_foreground(D),kztalk(D)],string[app_name(U),connecting_to_model(U),code_copied(D),copy(D),view_sources(U),no_permission_open_link(D),no_app_found_for_link(D),cannot_open_link(U),ai_reply_message(U)],style[Theme_App1(U)],xml[data_extraction_rules(U),backup_rules(U),network_security_config(U),file_paths(U)];b^9,d^0^b,e^0^b;;;"/>
    </map>

</incidents>
