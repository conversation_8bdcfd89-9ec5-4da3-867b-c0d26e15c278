package com.example.everytalk.util

/**
 * 增强的LaTeX到Unicode转换工具
 * 专门优化AI输出中的数学公式显示
 */
object EnhancedLatexToUnicode {
    
    // 希腊字母映射
    private val greekLetters = mapOf(
        "alpha" to "α", "beta" to "β", "gamma" to "γ", "delta" to "δ",
        "epsilon" to "ε", "zeta" to "ζ", "eta" to "η", "theta" to "θ",
        "iota" to "ι", "kappa" to "κ", "lambda" to "λ", "mu" to "μ",
        "nu" to "ν", "xi" to "ξ", "omicron" to "ο", "pi" to "π",
        "rho" to "ρ", "sigma" to "σ", "tau" to "τ", "upsilon" to "υ",
        "phi" to "φ", "chi" to "χ", "psi" to "ψ", "omega" to "ω",
        
        // 大写希腊字母
        "Alpha" to "Α", "Beta" to "Β", "Gamma" to "Γ", "Delta" to "Δ",
        "Epsilon" to "Ε", "Zeta" to "Ζ", "Eta" to "Η", "Theta" to "Θ",
        "Iota" to "Ι", "Kappa" to "Κ", "Lambda" to "Λ", "Mu" to "Μ",
        "Nu" to "Ν", "Xi" to "Ξ", "Omicron" to "Ο", "Pi" to "Π",
        "Rho" to "Ρ", "Sigma" to "Σ", "Tau" to "Τ", "Upsilon" to "Υ",
        "Phi" to "Φ", "Chi" to "Χ", "Psi" to "Ψ", "Omega" to "Ω"
    )
    
    // 数学符号映射
    private val mathSymbols = mapOf(
        "cdot" to "·", "times" to "×", "div" to "÷", "pm" to "±", "mp" to "∓",
        "leq" to "≤", "geq" to "≥", "neq" to "≠", "approx" to "≈", "equiv" to "≡",
        "sim" to "∼", "simeq" to "≃", "cong" to "≅", "propto" to "∝",
        "infty" to "∞", "partial" to "∂", "nabla" to "∇", "sum" to "∑",
        "prod" to "∏", "int" to "∫", "oint" to "∮", "iint" to "∬", "iiint" to "∭",
        "sqrt" to "√", "cbrt" to "∛", "angle" to "∠", "perp" to "⊥",
        "parallel" to "∥", "in" to "∈", "notin" to "∉", "subset" to "⊂",
        "supset" to "⊃", "subseteq" to "⊆", "supseteq" to "⊇", "cap" to "∩",
        "cup" to "∪", "emptyset" to "∅", "forall" to "∀", "exists" to "∃",
        "neg" to "¬", "land" to "∧", "lor" to "∨", "rightarrow" to "→",
        "leftarrow" to "←", "leftrightarrow" to "↔", "Rightarrow" to "⇒",
        "Leftarrow" to "⇐", "Leftrightarrow" to "⇔", "uparrow" to "↑",
        "downarrow" to "↓", "updownarrow" to "↕"
    )
    
    // 上标数字映射
    private val superscriptDigits = mapOf(
        "0" to "⁰", "1" to "¹", "2" to "²", "3" to "³", "4" to "⁴",
        "5" to "⁵", "6" to "⁶", "7" to "⁷", "8" to "⁸", "9" to "⁹",
        "+" to "⁺", "-" to "⁻", "=" to "⁼", "(" to "⁽", ")" to "⁾"
    )
    
    // 下标数字映射
    private val subscriptDigits = mapOf(
        "0" to "₀", "1" to "₁", "2" to "₂", "3" to "₃", "4" to "₄",
        "5" to "₅", "6" to "₆", "7" to "₇", "8" to "₈", "9" to "₉",
        "+" to "₊", "-" to "₋", "=" to "₌", "(" to "₍", ")" to "₎"
    )
    
    /**
     * 转换LaTeX表达式为Unicode
     */
    fun convert(latex: String): String {
        var result = latex.trim()
        
        // 1. 处理希腊字母
        for ((latexSymbol, unicode) in greekLetters) {
            result = result.replace("\\$latexSymbol", unicode)
        }
        
        // 2. 处理数学符号
        for ((latexSymbol, unicode) in mathSymbols) {
            result = result.replace("\\$latexSymbol", unicode)
        }
        
        // 3. 处理分数 \frac{a}{b}
        result = result.replace(Regex("\\\\frac\\{([^}]+)\\}\\{([^}]+)\\}")) { matchResult ->
            val numerator = matchResult.groupValues[1]
            val denominator = matchResult.groupValues[2]
            "$numerator/$denominator"
        }
        
        // 4. 处理平方根 \sqrt{x}
        result = result.replace(Regex("\\\\sqrt\\{([^}]+)\\}")) { matchResult ->
            val content = matchResult.groupValues[1]
            "√($content)"
        }
        
        // 5. 处理上标 ^{...} 或 ^x
        result = result.replace(Regex("\\^\\{([^}]+)\\}")) { matchResult ->
            val superscript = matchResult.groupValues[1]
            convertToSuperscript(superscript)
        }
        result = result.replace(Regex("\\^([a-zA-Z0-9])")) { matchResult ->
            val char = matchResult.groupValues[1]
            convertToSuperscript(char)
        }
        
        // 6. 处理下标 _{...} 或 _x
        result = result.replace(Regex("_\\{([^}]+)\\}")) { matchResult ->
            val subscript = matchResult.groupValues[1]
            convertToSubscript(subscript)
        }
        result = result.replace(Regex("_([a-zA-Z0-9])")) { matchResult ->
            val char = matchResult.groupValues[1]
            convertToSubscript(char)
        }
        
        // 7. 处理求和 \sum_{i=1}^{n}
        result = result.replace(Regex("\\\\sum_\\{([^}]+)\\}\\^\\{([^}]+)\\}")) { matchResult ->
            val lower = convertToSubscript(matchResult.groupValues[1])
            val upper = convertToSuperscript(matchResult.groupValues[2])
            "∑$lower$upper"
        }
        
        // 8. 处理积分 \int_{a}^{b}
        result = result.replace(Regex("\\\\int_\\{([^}]+)\\}\\^\\{([^}]+)\\}")) { matchResult ->
            val lower = convertToSubscript(matchResult.groupValues[1])
            val upper = convertToSuperscript(matchResult.groupValues[2])
            "∫$lower$upper"
        }
        
        // 9. 处理极限 \lim_{x \to a}
        result = result.replace(Regex("\\\\lim_\\{([^}]+)\\}")) { matchResult ->
            val condition = convertToSubscript(matchResult.groupValues[1])
            "lim$condition"
        }
        
        // 10. 处理文本 \text{...}
        result = result.replace(Regex("\\\\text\\{([^}]+)\\}")) { matchResult ->
            matchResult.groupValues[1]
        }
        
        // 11. 处理矩阵和向量的括号
        result = result.replace("\\left(", "(")
        result = result.replace("\\right)", ")")
        result = result.replace("\\left[", "[")
        result = result.replace("\\right]", "]")
        result = result.replace("\\left\\{", "{")
        result = result.replace("\\right\\}", "}")
        
        // 12. 清理剩余的反斜杠
        result = result.replace(Regex("\\\\([a-zA-Z]+)")) { matchResult ->
            val command = matchResult.groupValues[1]
            // 如果是未知命令，保留原样或简化
            when (command) {
                "displaystyle" -> ""
                "scriptstyle" -> ""
                "scriptscriptstyle" -> ""
                else -> command
            }
        }
        
        return result
    }
    
    /**
     * 转换为上标
     */
    private fun convertToSuperscript(text: String): String {
        return text.map { char ->
            superscriptDigits[char.toString()] ?: char
        }.joinToString("")
    }
    
    /**
     * 转换为下标
     */
    private fun convertToSubscript(text: String): String {
        return text.map { char ->
            subscriptDigits[char.toString()] ?: char
        }.joinToString("")
    }
}