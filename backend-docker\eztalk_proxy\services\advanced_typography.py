"""
高级排版处理器
提供智能段落分割、自动缩进、标点符号优化、中英文混排优化等功能
"""

import re
import logging
from typing import Dict, List, Tuple, Optional

logger = logging.getLogger(__name__)

class AdvancedTypographyProcessor:
    """高级排版处理器"""
    
    def __init__(self):
        self.config = {
            "enable_smart_indentation": True,
            "enable_punctuation_optimization": True,
            "enable_chinese_english_spacing": True,
            "enable_paragraph_optimization": False,  # 临时禁用，避免过度分割
            "enable_line_height_optimization": True,
            "max_line_length": 80,
            "paragraph_spacing": 1,
            "list_indentation": 2,
        }
        
        self.stats = {
            "indentation_fixes": 0,
            "punctuation_fixes": 0,
            "spacing_fixes": 0,
            "paragraph_fixes": 0,
            "line_breaks": 0,
        }
    
    def process_text(self, text: str, log_prefix: str = "") -> str:
        """处理文本的高级排版"""
        if not text or not text.strip():
            return text
        
        original_text = text
        processed = text
        
        # 1. 智能段落分割
        if self.config["enable_paragraph_optimization"]:
            processed = self._optimize_paragraph_structure(processed)
        
        # 2. 自动缩进处理
        if self.config["enable_smart_indentation"]:
            processed = self._apply_smart_indentation(processed)
        
        # 3. 标点符号优化
        if self.config["enable_punctuation_optimization"]:
            processed = self._optimize_punctuation(processed)
        
        # 4. 中英文混排优化
        if self.config["enable_chinese_english_spacing"]:
            processed = self._optimize_chinese_english_spacing(processed)
        
        # 5. 行高优化
        if self.config["enable_line_height_optimization"]:
            processed = self._optimize_line_height(processed)
        
        if processed != original_text:
            logger.info(f"{log_prefix}: Applied advanced typography processing")
        
        return processed
    
    def _optimize_paragraph_structure(self, text: str) -> str:
        """优化段落结构"""
        lines = text.split('\n')
        result = []
        
        for i, line in enumerate(lines):
            result.append(line)
            
            # 检查是否需要在当前行后添加段落分隔
            if i < len(lines) - 1:
                current_line = line.strip()
                next_line = lines[i + 1].strip()
                
                if self._should_add_paragraph_break(current_line, next_line):
                    result.append('')  # 添加空行分隔段落
                    self.stats["paragraph_fixes"] += 1
        
        return '\n'.join(result)
    
    def _should_add_paragraph_break(self, current_line: str, next_line: str) -> bool:
        """判断是否应该添加段落分隔 - 更保守的策略"""
        if not current_line or not next_line:
            return False

        # 只有在明确的段落结束标志时才添加分隔
        # 1. 当前行以句号、感叹号、问号结尾
        sentence_endings = ['。', '！', '？', '.', '!', '?']
        ends_with_sentence = any(current_line.endswith(ending) for ending in sentence_endings)

        if not ends_with_sentence:
            return False

        # 2. 下一行不是特殊格式
        special_formats = ['-', '*', '+', '#', '```', '|']
        is_special = any(next_line.startswith(fmt) for fmt in special_formats)
        is_numbered_list = re.match(r'^\d+\.', next_line)

        if is_special or is_numbered_list:
            return False

        # 3. 检查语义连续性 - 扩展连接词列表
        continuity_words = [
            # 中文连接词
            '然而', '但是', '不过', '因此', '所以', '因为', '由于', '另外', '此外', '而且', '并且',
            '同时', '接着', '然后', '接下来', '首先', '其次', '最后', '总之', '综上', '例如', '比如',
            '也就是说', '换句话说', '具体来说', '简单来说', '总的来说', '一般来说', '通常来说',
            # 英文连接词
            'however', 'but', 'therefore', 'because', 'moreover', 'furthermore', 'additionally',
            'meanwhile', 'then', 'next', 'first', 'second', 'finally', 'in conclusion', 'for example',
            'that is', 'in other words', 'specifically', 'generally', 'usually', 'typically'
        ]

        for word in continuity_words:
            if next_line.startswith(word):
                return False

        # 4. 检查行长度 - 如果当前行很短，可能不是段落结束
        if len(current_line.strip()) < 20:
            return False

        # 5. 检查下一行是否以小写字母开头（可能是句子延续）
        if next_line and next_line[0].islower():
            return False

        # 6. 检查是否是问答格式
        if current_line.strip().endswith('?') or current_line.strip().endswith('？'):
            # 如果下一行看起来像答案，不分段
            answer_starters = ['是', '不', '可以', '不可以', '会', '不会', 'yes', 'no', 'it', 'this', 'that']
            if any(next_line.lower().startswith(starter) for starter in answer_starters):
                return False

        return True
    
    def _apply_smart_indentation(self, text: str) -> str:
        """应用智能缩进"""
        lines = text.split('\n')
        result = []
        indent_level = 0
        
        for line in lines:
            stripped = line.strip()
            
            if not stripped:
                result.append('')
                continue
            
            # 检测列表项
            if re.match(r'^[-*+]\s', stripped):
                # 无序列表
                indented_line = '  ' * indent_level + stripped
                result.append(indented_line)
                self.stats["indentation_fixes"] += 1
            elif re.match(r'^\d+\.\s', stripped):
                # 有序列表
                indented_line = '  ' * indent_level + stripped
                result.append(indented_line)
                self.stats["indentation_fixes"] += 1
            elif stripped.startswith('#'):
                # 标题不缩进
                result.append(stripped)
                indent_level = 0
            elif stripped.startswith('```'):
                # 代码块不缩进
                result.append(stripped)
            else:
                # 普通段落
                if self.config["enable_smart_indentation"] and len(stripped) > 0:
                    # 检查是否是段落开始
                    if self._is_paragraph_start(stripped, result):
                        indented_line = '    ' + stripped  # 段落首行缩进
                        result.append(indented_line)
                        self.stats["indentation_fixes"] += 1
                    else:
                        result.append(stripped)
                else:
                    result.append(stripped)
        
        return '\n'.join(result)
    
    def _is_paragraph_start(self, line: str, previous_lines: List[str]) -> bool:
        """判断是否是段落开始"""
        if not previous_lines:
            return True
        
        # 检查前一行是否是空行
        if previous_lines and previous_lines[-1].strip() == '':
            return True
        
        return False
    
    def _optimize_punctuation(self, text: str) -> str:
        """优化标点符号"""
        optimized = text
        
        # 中文标点符号优化
        chinese_punctuation_fixes = {
            r'，\s+': '，',  # 中文逗号后不需要空格
            r'。\s+': '。',  # 中文句号后不需要空格
            r'！\s+': '！',  # 中文感叹号后不需要空格
            r'？\s+': '？',  # 中文问号后不需要空格
            r'；\s+': '；',  # 中文分号后不需要空格
            r'：\s+': '：',  # 中文冒号后不需要空格
        }
        
        for pattern, replacement in chinese_punctuation_fixes.items():
            new_text = re.sub(pattern, replacement, optimized)
            if new_text != optimized:
                self.stats["punctuation_fixes"] += 1
                optimized = new_text
        
        # 英文标点符号优化
        english_punctuation_fixes = {
            r'([,.!?;:])([a-zA-Z])': r'\1 \2',  # 英文标点后需要空格
            r'\s+([,.!?;:])': r'\1',  # 英文标点前不需要空格
        }
        
        for pattern, replacement in english_punctuation_fixes.items():
            new_text = re.sub(pattern, replacement, optimized)
            if new_text != optimized:
                self.stats["punctuation_fixes"] += 1
                optimized = new_text
        
        return optimized
    
    def _optimize_chinese_english_spacing(self, text: str) -> str:
        """优化中英文混排间距"""
        optimized = text
        
        # 中文字符和英文字符之间添加空格
        spacing_fixes = {
            r'([\u4e00-\u9fa5])([a-zA-Z0-9])': r'\1 \2',
            r'([a-zA-Z0-9])([\u4e00-\u9fa5])': r'\1 \2',
            r'([\u4e00-\u9fa5])(\d)': r'\1 \2',
            r'(\d)([\u4e00-\u9fa5])': r'\1 \2',
        }
        
        for pattern, replacement in spacing_fixes.items():
            new_text = re.sub(pattern, replacement, optimized)
            if new_text != optimized:
                self.stats["spacing_fixes"] += 1
                optimized = new_text
        
        # 避免在标点符号周围添加多余空格
        optimized = re.sub(r'([\u4e00-\u9fa5])\s+([，。！？；：])', r'\1\2', optimized)
        
        return optimized
    
    def _optimize_line_height(self, text: str) -> str:
        """优化行高"""
        # 限制连续空行数量
        max_consecutive_newlines = self.config["paragraph_spacing"] + 1
        pattern = f'\\n{{{max_consecutive_newlines + 1},}}'
        replacement = '\n' * max_consecutive_newlines
        
        optimized = re.sub(pattern, replacement, text)
        
        if optimized != text:
            self.stats["line_breaks"] += 1
        
        return optimized
    
    def get_stats(self) -> Dict[str, int]:
        """获取处理统计信息"""
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        for key in self.stats:
            self.stats[key] = 0

# 全局实例
advanced_typography_processor = AdvancedTypographyProcessor()
