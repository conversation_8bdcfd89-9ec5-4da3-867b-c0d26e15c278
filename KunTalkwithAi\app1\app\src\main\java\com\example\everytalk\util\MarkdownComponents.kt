package com.example.everytalk.util

import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight

/**
 * 简化的Markdown文本组件
 */
@Composable
fun MarkdownText(
    text: String,
    style: TextStyle = MaterialTheme.typography.bodyLarge,
    modifier: Modifier = Modifier,
    isStreaming: Boolean = false,
    renderer: SimpleMarkdownRenderer,
    messageId: String = ""
) {
    val annotatedString = remember(text, isStreaming) {
        renderer.renderText(text, isStreaming)
    }
    
    ClickableText(
        text = annotatedString,
        style = style,
        modifier = modifier,
        onClick = { offset ->
            // 简化的链接点击处理
            annotatedString.getStringAnnotations(
                tag = "URL",
                start = offset,
                end = offset
            ).firstOrNull()?.let { annotation ->
                // 这里可以添加链接点击处理逻辑
            }
        }
    )
}

/**
 * 简化的Markdown标题组件
 */
@Composable
fun MarkdownHeader(
    block: MarkdownBlock.Header,
    contentColor: Color,
    isStreaming: Boolean = false,
    renderer: SimpleMarkdownRenderer
) {
    val fontSize = when (block.level) {
        1 -> MaterialTheme.typography.headlineLarge.fontSize
        2 -> MaterialTheme.typography.headlineMedium.fontSize
        3 -> MaterialTheme.typography.headlineSmall.fontSize
        4 -> MaterialTheme.typography.titleLarge.fontSize
        5 -> MaterialTheme.typography.titleMedium.fontSize
        else -> MaterialTheme.typography.titleSmall.fontSize
    }
    
    MarkdownText(
        text = block.text,
        style = MaterialTheme.typography.bodyLarge.copy(
            fontSize = fontSize,
            fontWeight = FontWeight.Bold,
            color = contentColor
        ),
        isStreaming = isStreaming,
        renderer = renderer
    )
}