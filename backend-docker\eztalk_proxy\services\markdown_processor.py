import logging
import re
from typing import Dict, Any, Optional, List, Tuple
import unicodedata
from .advanced_typography import advanced_typography_processor
from .adaptive_formatter import adaptive_formatter
from .quality_assessor import quality_assessor

logger = logging.getLogger("EzTalkProxy.Services.MarkdownProcessor")

class UnifiedMarkdownProcessor:
    """
    统一的Markdown和数学公式处理器
    适用于所有大模型的输出格式化
    """
    
    def __init__(self):
        self.stats = {
            "math_fixes": 0,
            "code_fixes": 0,
            "table_fixes": 0,
            "list_fixes": 0,
            "whitespace_fixes": 0,
            "typography_fixes": 0,
            "paragraph_fixes": 0,
            "punctuation_fixes": 0
        }

        # 智能排版配置
        self.typography_config = {
            "enable_smart_quotes": True,
            "enable_paragraph_optimization": True,
            "enable_punctuation_spacing": True,
            "enable_chinese_english_spacing": True,
            "max_consecutive_newlines": 3,
            "preserve_code_formatting": True
        }
    
    def process_content(self, text: str, request_id: str = "", model_type: str = "general") -> str:
        """
        统一处理所有大模型的markdown内容
        
        Args:
            text: 原始文本
            request_id: 请求ID用于日志
            model_type: 模型类型 ("gemini", "openai", "claude", "general")
        
        Returns:
            处理后的文本
        """
        if not isinstance(text, str) or not text.strip():
            return text
        
        log_prefix = f"RID-{request_id}" if request_id else "MarkdownProcessor"
        original_length = len(text)
        
        logger.info(f"{log_prefix}: Processing {original_length} chars for {model_type} model")
        
        # 重置统计
        self.stats = {k: 0 for k in self.stats}
        
        try:
            # 0. 应用自适应格式化策略
            text = adaptive_formatter.apply_adaptive_formatting(text, model_type)

            # 1. 数学公式优化
            text = self._fix_math_formulas(text, log_prefix)
            
            # 2. 代码块修复
            text = self._fix_code_blocks(text, log_prefix)
            
            # 3. 表格格式化
            text = self._fix_table_formatting(text, log_prefix)
            
            # 4. 列表优化
            text = self._fix_list_formatting(text, log_prefix)
            
            # 5. 根据模型类型应用特定优化
            if model_type == "gemini":
                text = self._apply_gemini_specific_fixes(text, log_prefix)
            elif model_type == "openai":
                text = self._apply_openai_specific_fixes(text, log_prefix)
            
            # 6. 智能排版优化
            text = self._apply_smart_typography(text, log_prefix)

            # 7. 段落结构优化
            text = self._optimize_paragraph_structure(text, log_prefix)

            # 8. 通用空白处理
            text = self._optimize_whitespace(text, log_prefix)

            # 9. 高级排版处理
            text = advanced_typography_processor.process_text(text, log_prefix)

            # 10. 最终清理
            text = self._final_cleanup(text, log_prefix)
            
            final_length = len(text)

            # 质量评估
            if original_length > 0:  # 确保有原始文本进行比较
                quality_metrics = quality_assessor.assess_quality(text, text)  # 这里可以传入原始文本作为对比
                logger.info(f"{log_prefix}: Quality assessment - Score: {quality_metrics.overall_score:.1f}, Level: {quality_metrics.quality_level.value}")
                if quality_metrics.recommendations:
                    logger.debug(f"{log_prefix}: Recommendations: {', '.join(quality_metrics.recommendations[:3])}")

            logger.info(f"{log_prefix}: Processing completed. {original_length} -> {final_length} chars. Fixes: {self.stats}")

            return text
            
        except Exception as e:
            logger.error(f"{log_prefix}: Error processing markdown: {e}", exc_info=True)
            return text
    
    def _fix_math_formulas(self, text: str, log_prefix: str) -> str:
        """修复数学公式格式"""
        if '$' not in text:
            return text
        
        original_text = text
        
        # 1. 修复LaTeX语法错误
        fixes = {
            # 分数格式化
            r'\\frac\s+(\w+)\s+(\w+)': r'\\frac{\1}{\2}',
            r'\\frac\s*\{\s*([^}]+)\s*\}\s*\{\s*([^}]+)\s*\}': r'\\frac{\1}{\2}',
            
            # 根号格式化
            r'\\sqrt\s+(\w+)': r'\\sqrt{\1}',
            r'\\sqrt\s*\{\s*([^}]+)\s*\}': r'\\sqrt{\1}',
            
            # 求和和积分
            r'\\sum\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)': r'\\sum_{\1}^{\2}',
            r'\\int\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)': r'\\int_{\1}^{\2}',
            
            # 上下标
            r'([a-zA-Z])\s*_\s*([^{\s]+)\s*\^\s*([^{\s]+)': r'\1_{\2}^{\3}',
            r'([a-zA-Z])\s*\^\s*([^{\s]+)\s*_\s*([^{\s]+)': r'\1^{\2}_{\3}',
            
            # 数学函数
            r'\\log\s+([^{\s]+)': r'\\log{\1}',
            r'\\ln\s+([^{\s]+)': r'\\ln{\1}',
            
            # 修复空格问题
            r'\$\s+': r'$',
            r'\s+\$': r'$',
            r'\$\$\s+': r'$$',
            r'\s+\$\$': r'$$',
            
            # 大括号空格
            r'\{\s+': r'{',
            r'\s+\}': r'}',
            
            # 矩阵和向量
            r'\\begin\s*\{\s*([a-zA-Z]+)\s*\}': r'\\begin{\1}',
            r'\\end\s*\{\s*([a-zA-Z]+)\s*\}': r'\\end{\1}',
        }
        
        for pattern, replacement in fixes.items():
            new_text = re.sub(pattern, replacement, text)
            if new_text != text:
                self.stats["math_fixes"] += 1
                text = new_text
        
        # 2. 确保数学分隔符平衡
        text = self._balance_math_delimiters(text)
        
        # 3. 修复常见的KaTeX兼容性问题
        text = self._fix_katex_compatibility(text)
        
        if text != original_text:
            logger.info(f"{log_prefix}: Applied {self.stats['math_fixes']} math formula fixes")
        
        return text
    
    def _balance_math_delimiters(self, text: str) -> str:
        """确保数学分隔符平衡"""
        # 处理单个$符号
        single_dollar_count = 0
        result = []
        i = 0
        
        while i < len(text):
            if i < len(text) - 1 and text[i:i+2] == '$$':
                result.append('$$')
                i += 2
            elif text[i] == '$':
                single_dollar_count += 1
                result.append('$')
                i += 1
            else:
                result.append(text[i])
                i += 1
        
        # 如果单个$符号数量是奇数，添加一个$
        if single_dollar_count % 2 == 1:
            result.append('$')
            self.stats["math_fixes"] += 1
        
        return ''.join(result)
    
    def _fix_katex_compatibility(self, text: str) -> str:
        """修复KaTeX兼容性问题"""
        katex_fixes = {
            # 常见的LaTeX命令转KaTeX
            r'\\displaystyle': '',  # KaTeX自动处理显示样式
            r'\\text\s*\{\s*([^}]+)\s*\}': r'\\text{\1}',  # 清理文本命令
            r'\\mathrm\s*\{\s*([^}]+)\s*\}': r'\\mathrm{\1}',  # 罗马字体
            r'\\mathbf\s*\{\s*([^}]+)\s*\}': r'\\mathbf{\1}',  # 粗体
            r'\\mathit\s*\{\s*([^}]+)\s*\}': r'\\mathit{\1}',  # 斜体

            # 修复align环境
            r'\\begin\s*\{\s*align\s*\}': r'\\begin{aligned}',
            r'\\end\s*\{\s*align\s*\}': r'\\end{aligned}',
            r'\\begin\s*\{\s*align\*\s*\}': r'\\begin{aligned}',
            r'\\end\s*\{\s*align\*\s*\}': r'\\end{aligned}',

            # 增强的数学函数处理
            r'\\arcsin': r'\\arcsin',
            r'\\arccos': r'\\arccos',
            r'\\arctan': r'\\arctan',
            r'\\sinh': r'\\sinh',
            r'\\cosh': r'\\cosh',
            r'\\tanh': r'\\tanh',

            # 修复常见的希腊字母
            r'\\alpha': r'\\alpha',
            r'\\beta': r'\\beta',
            r'\\gamma': r'\\gamma',
            r'\\delta': r'\\delta',
            r'\\epsilon': r'\\epsilon',
            r'\\theta': r'\\theta',
            r'\\lambda': r'\\lambda',
            r'\\mu': r'\\mu',
            r'\\pi': r'\\pi',
            r'\\sigma': r'\\sigma',
            r'\\phi': r'\\phi',
            r'\\omega': r'\\omega',

            # 修复数学符号
            r'\\infty': r'\\infty',
            r'\\partial': r'\\partial',
            r'\\nabla': r'\\nabla',
            r'\\pm': r'\\pm',
            r'\\mp': r'\\mp',
            r'\\times': r'\\times',
            r'\\div': r'\\div',
            r'\\cdot': r'\\cdot',
            r'\\leq': r'\\leq',
            r'\\geq': r'\\geq',
            r'\\neq': r'\\neq',
            r'\\approx': r'\\approx',
            r'\\equiv': r'\\equiv',

            # 修复集合符号
            r'\\in': r'\\in',
            r'\\notin': r'\\notin',
            r'\\subset': r'\\subset',
            r'\\supset': r'\\supset',
            r'\\subseteq': r'\\subseteq',
            r'\\supseteq': r'\\supseteq',
            r'\\cup': r'\\cup',
            r'\\cap': r'\\cap',
            r'\\emptyset': r'\\emptyset',

            # 修复箭头符号
            r'\\rightarrow': r'\\rightarrow',
            r'\\leftarrow': r'\\leftarrow',
            r'\\leftrightarrow': r'\\leftrightarrow',
            r'\\Rightarrow': r'\\Rightarrow',
            r'\\Leftarrow': r'\\Leftarrow',
            r'\\Leftrightarrow': r'\\Leftrightarrow',
        }

        for pattern, replacement in katex_fixes.items():
            new_text = re.sub(pattern, replacement, text)
            if new_text != text:
                self.stats["math_fixes"] += 1
                text = new_text

        # 增强的数学公式验证和修复
        text = self._enhance_math_formulas(text)

        return text

    def _enhance_math_formulas(self, text: str) -> str:
        """增强数学公式处理"""
        # 修复常见的数学表达式格式问题

        # 1. 修复分数表达式
        text = re.sub(r'(\d+)/(\d+)', r'\\frac{\1}{\2}', text)
        text = re.sub(r'([a-zA-Z]+)/([a-zA-Z]+)', r'\\frac{\1}{\2}', text)

        # 2. 修复指数表达式
        text = re.sub(r'([a-zA-Z0-9])\^([a-zA-Z0-9])', r'\1^{\2}', text)
        text = re.sub(r'([a-zA-Z0-9])_([a-zA-Z0-9])', r'\1_{\2}', text)

        # 3. 修复根号表达式
        text = re.sub(r'sqrt\(([^)]+)\)', r'\\sqrt{\1}', text)

        # 4. 修复三角函数
        trig_functions = ['sin', 'cos', 'tan', 'sec', 'csc', 'cot']
        for func in trig_functions:
            text = re.sub(f'{func}\\(([^)]+)\\)', f'\\\\{func}(\\1)', text)

        # 5. 修复对数函数
        text = re.sub(r'log\(([^)]+)\)', r'\\log(\1)', text)
        text = re.sub(r'ln\(([^)]+)\)', r'\\ln(\1)', text)

        # 6. 修复求和和积分
        text = re.sub(r'sum\(([^)]+)\)', r'\\sum(\1)', text)
        text = re.sub(r'int\(([^)]+)\)', r'\\int(\1)', text)

        return text
    
    def _fix_code_blocks(self, text: str, log_prefix: str) -> str:
        """修复代码块格式"""
        if '```' not in text:
            return text
        
        original_text = text
        
        # 1. 统计代码块标记
        code_block_count = text.count('```')
        if code_block_count % 2 != 0:
            # 奇数个标记，添加闭合标记
            text += '\n```'
            self.stats["code_fixes"] += 1
            logger.info(f"{log_prefix}: Added missing code block closing marker")
        
        # 2. 修复内联代码块标记
        lines = text.split('\n')
        fixed_lines = []
        in_code_block = False
        
        for line in lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                # 清理语言标识符
                if in_code_block and len(line.strip()) > 3:
                    lang = line.strip()[3:].strip()
                    fixed_lines.append(f"```{lang}")
                else:
                    fixed_lines.append("```")
            elif '```' in line and not line.strip().startswith('```'):
                # 处理行中间的代码块标记
                parts = line.split('```')
                if len(parts) >= 2:
                    new_line = parts[0].rstrip()
                    if new_line:
                        fixed_lines.append(new_line)
                    
                    for j in range(1, len(parts)):
                        if j % 2 == 1:  # 开始标记
                            lang = parts[j].split()[0] if parts[j].strip() else ""
                            fixed_lines.append(f"```{lang}")
                            if len(parts[j].split()) > 1:
                                fixed_lines.append(' '.join(parts[j].split()[1:]))
                        else:  # 结束标记
                            if parts[j].strip():
                                fixed_lines.append(parts[j].lstrip())
                            fixed_lines.append("```")
                    self.stats["code_fixes"] += 1
                else:
                    fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        
        text = '\n'.join(fixed_lines)
        
        if text != original_text:
            logger.info(f"{log_prefix}: Applied {self.stats['code_fixes']} code block fixes")
        
        return text
    
    def _fix_table_formatting(self, text: str, log_prefix: str) -> str:
        """修复表格格式"""
        if '|' not in text:
            return text
        
        original_text = text
        lines = text.split('\n')
        fixed_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            if '|' in line and line.strip() and not self._is_code_line(line, lines, i):
                # 检测表格开始
                table_lines = []
                j = i
                
                # 收集所有表格行
                while j < len(lines) and '|' in lines[j] and lines[j].strip():
                    table_lines.append(lines[j])
                    j += 1
                
                if len(table_lines) >= 1:
                    # 修复表格格式
                    fixed_table = self._fix_table_lines(table_lines)
                    fixed_lines.extend(fixed_table)
                    self.stats["table_fixes"] += 1
                
                i = j
            else:
                fixed_lines.append(line)
                i += 1
        
        text = '\n'.join(fixed_lines)
        
        if text != original_text:
            logger.info(f"{log_prefix}: Applied {self.stats['table_fixes']} table fixes")
        
        return text
    
    def _is_code_line(self, line: str, all_lines: list, line_index: int) -> bool:
        """检查是否是代码块内的行"""
        code_block_count = 0
        for i in range(line_index):
            if '```' in all_lines[i]:
                code_block_count += all_lines[i].count('```')
        
        return code_block_count % 2 == 1
    
    def _fix_table_lines(self, table_lines: list) -> list:
        """修复表格行格式"""
        if not table_lines:
            return []

        fixed = []
        max_columns = 0

        # 第一遍：确定最大列数
        for line in table_lines:
            if not self._is_table_separator(line):
                cells = [cell.strip() for cell in line.split('|')]
                # 移除开头和结尾的空单元格
                if cells and cells[0] == '':
                    cells = cells[1:]
                if cells and cells[-1] == '':
                    cells = cells[:-1]
                max_columns = max(max_columns, len(cells))

        # 第二遍：格式化所有行
        separator_added = False
        for idx, line in enumerate(table_lines):
            if self._is_table_separator(line):
                # 重新生成分隔符以确保列数一致
                separator = '| ' + ' | '.join(['---'] * max_columns) + ' |'
                fixed.append(separator)
                separator_added = True
            else:
                # 处理数据行
                cells = [cell.strip() for cell in line.split('|')]

                # 移除开头和结尾的空单元格
                if cells and cells[0] == '':
                    cells = cells[1:]
                if cells and cells[-1] == '':
                    cells = cells[:-1]

                # 补齐列数
                while len(cells) < max_columns:
                    cells.append('')

                # 智能对齐处理
                aligned_cells = self._align_table_cells(cells, idx == 0)

                if aligned_cells:
                    formatted_line = '| ' + ' | '.join(aligned_cells) + ' |'
                    fixed.append(formatted_line)

                    # 在第一行后添加分隔符（如果缺失）
                    if idx == 0 and not separator_added and len(table_lines) > 1:
                        next_line = table_lines[1] if idx + 1 < len(table_lines) else ""
                        if not self._is_table_separator(next_line):
                            separator = '| ' + ' | '.join(['---'] * len(aligned_cells)) + ' |'
                            fixed.append(separator)
                            separator_added = True

        return fixed

    def _align_table_cells(self, cells: list, is_header: bool = False) -> list:
        """智能对齐表格单元格"""
        aligned = []

        for cell in cells:
            # 清理单元格内容
            cleaned_cell = cell.strip()

            # 如果是数字，右对齐；如果是文本，左对齐
            if cleaned_cell.replace('.', '').replace('-', '').replace('+', '').isdigit():
                # 数字内容，保持原样（Markdown表格对齐由CSS处理）
                aligned.append(cleaned_cell)
            else:
                # 文本内容，确保适当的间距
                aligned.append(cleaned_cell)

        return aligned
    
    def _is_table_separator(self, line: str) -> bool:
        """检查是否是表格分隔符行"""
        return bool(re.match(r'^\s*\|[\s\-:]+\|\s*$', line))
    
    def _fix_list_formatting(self, text: str, log_prefix: str) -> str:
        """修复列表格式"""
        original_text = text
        
        # 1. 修复重复的列表标记
        text = self._fix_duplicate_list_markers(text)
        
        # 2. 重新编号有序列表
        text = self._renumber_ordered_lists(text)
        
        if text != original_text:
            self.stats["list_fixes"] += 1
            logger.info(f"{log_prefix}: Applied list formatting fixes")
        
        return text
    
    def _fix_duplicate_list_markers(self, text: str) -> str:
        """修复重复的列表标记"""
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            if '|' in line:  # 跳过表格行
                cleaned_lines.append(line)
                continue
            
            # 修复重复的数字列表标记
            duplicate_number_pattern = r'^(\s*)(\d+\.\s+)+(\d+\.\s+)(.*)$'
            match = re.match(duplicate_number_pattern, line)
            if match:
                indent = match.group(1)
                last_number = match.group(3)
                content = match.group(4)
                cleaned_lines.append(f"{indent}{last_number}{content}")
                continue
            
            # 修复重复的项目符号
            duplicate_bullet_pattern = r'^(\s*)([*+-]\s+)+([*+-]\s+)(.*)$'
            match = re.match(duplicate_bullet_pattern, line)
            if match:
                indent = match.group(1)
                bullet = match.group(3)
                content = match.group(4)
                cleaned_lines.append(f"{indent}{bullet}{content}")
                continue
            
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _renumber_ordered_lists(self, lines_text: str) -> str:
        """重新编号有序列表"""
        lines = lines_text.split('\n')
        result = []
        indent_counters = {}
        
        # 检查是否有重复编号问题
        has_duplicate_numbering = False
        first_items = [line for line in lines if re.match(r'^\s*1\.\s+.*', line.strip())]
        if len(first_items) > 1:
            has_duplicate_numbering = True
        
        if not has_duplicate_numbering:
            return lines_text
        
        for line in lines:
            stripped_line = line.strip()
            
            # 检查是否是有序列表项
            ordered_list_match = re.match(r'^(\d+)\.\s+(.*)$', stripped_line)
            
            if ordered_list_match:
                content = ordered_list_match.group(2)
                current_indent = len(line) - len(line.lstrip())
                
                # 初始化或递增计数器
                if current_indent not in indent_counters:
                    indent_counters[current_indent] = 1
                else:
                    indent_counters[current_indent] += 1
                
                # 重置更深层次的计数器
                keys_to_remove = [k for k in indent_counters.keys() if k > current_indent]
                for k in keys_to_remove:
                    del indent_counters[k]
                
                result.append(' ' * current_indent + f"{indent_counters[current_indent]}. {content}")
            else:
                # 非有序列表项
                if stripped_line and not re.match(r'^[*+-]\s+.*', stripped_line):
                    # 遇到非列表内容时，可能需要重置计数器
                    if not any(re.match(r'^\s*\d+\.\s+.*', l.strip()) for l in lines[lines.index(line)+1:lines.index(line)+3] if lines.index(line)+1 < len(lines)):
                        indent_counters.clear()
                result.append(line)
        
        return '\n'.join(result)
    
    def _apply_gemini_specific_fixes(self, text: str, log_prefix: str) -> str:
        """应用Gemini特定的修复"""
        # Gemini经常在联网搜索结果中产生格式问题
        
        # 1. 修复搜索结果中的引用格式
        citation_pattern = r'\[(\d+)\]\s*\(([^)]+)\)'
        text = re.sub(citation_pattern, r'[\1](\2)', text)
        
        # 2. 修复思考标记的格式
        text = re.sub(r'<think>\s*([^<]+)\s*</think>', r'<思考>\n\1\n</思考>', text)
        
        return text
    
    def _apply_openai_specific_fixes(self, text: str, log_prefix: str) -> str:
        """应用OpenAI特定的修复"""
        # OpenAI模型经常在reasoning输出中有格式问题
        
        # 1. 修复reasoning标记
        text = re.sub(r'<reasoning>\s*([^<]+)\s*</reasoning>', r'**推理过程：**\n\1', text)
        
        # 2. 修复工具调用格式
        text = re.sub(r'```json\s*(\{[^}]+\})\s*```', r'```json\n\1\n```', text)
        
        return text
    
    def _optimize_whitespace(self, text: str, log_prefix: str) -> str:
        """优化空白字符"""
        original_text = text
        
        # 1. 保守的换行清理 - 保留段落结构和AI输出的自然换行
        # 只清理6个或更多的连续换行符，保留更多的段落分隔
        text = re.sub(r'\n{6,}', '\n\n\n\n', text)
        
        # 2. 清理标点符号后的过多换行（但保留一些自然分隔）
        text = re.sub(r'([：:。！？])\n{4,}', r'\1\n\n\n', text)
        
        # 3. 清理分隔符后的过多换行
        text = re.sub(r'(---)\n{4,}', r'\1\n\n\n', text)
        
        # 4. 移除行尾空白但保留空行用于段落分隔
        text = re.sub(r'[ \t]+$', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s+$', '', text, flags=re.MULTILINE)
        
        # 5. 处理多余的空白行（更宽松的处理）
        text = re.sub(r'\n\s*\n\s*\n\s*\n\s*\n', '\n\n\n\n', text)
        
        if text != original_text:
            self.stats["whitespace_fixes"] += 1
        
        return text
    
    def _final_cleanup(self, text: str, log_prefix: str) -> str:
        """最终清理"""
        # 1. 修复markdown标记周围的空格
        text = re.sub(r'\*\*\s+(.*?)\s+\*\*', r'**\1**', text)
        text = re.sub(r'\*\s+(.*?)\s+\*', r'*\1*', text)
        
        # 2. 修复链接格式
        text = re.sub(r'\[\s+(.*?)\s+\]\s*\(\s*(.*?)\s*\)', r'[\1](\2)', text)
        
        # 3. 最终空白清理
        text = text.strip()
        
        # 4. 确保文档结构完整
        if text and not text.endswith('\n') and ('\n' in text):
            text += '\n'
        
        return text

    def _apply_smart_typography(self, text: str, log_prefix: str) -> str:
        """应用智能排版优化"""
        if not self.typography_config["enable_smart_quotes"] and not self.typography_config["enable_punctuation_spacing"]:
            return text

        original_text = text

        # 1. 智能引号处理
        if self.typography_config["enable_smart_quotes"]:
            text = self._fix_smart_quotes(text)

        # 2. 标点符号间距优化
        if self.typography_config["enable_punctuation_spacing"]:
            text = self._optimize_punctuation_spacing(text)

        # 3. 中英文混排优化
        if self.typography_config["enable_chinese_english_spacing"]:
            text = self._optimize_chinese_english_spacing(text)

        if text != original_text:
            self.stats["typography_fixes"] += 1
            logger.info(f"{log_prefix}: Applied smart typography fixes")

        return text

    def _fix_smart_quotes(self, text: str) -> str:
        """修复智能引号"""
        # 避免在代码块中处理
        lines = text.split('\n')
        result = []
        in_code_block = False

        for line in lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                result.append(line)
                continue

            if not in_code_block:
                # 修复中文引号
                line = re.sub(r'"([^"]*)"', r'"\1"', line)
                line = re.sub(r"'([^']*)'", r''\1'', line)

                # 修复英文引号配对
                line = re.sub(r'\s"([^"]+)"\s', r' "\1" ', line)

            result.append(line)

        return '\n'.join(result)

    def _optimize_punctuation_spacing(self, text: str) -> str:
        """优化标点符号间距"""
        # 中文标点后不需要空格，英文标点后需要空格

        # 中文标点符号处理
        chinese_punctuation = '，。！？；：""''（）【】《》'
        for punct in chinese_punctuation:
            # 移除中文标点后的多余空格
            text = re.sub(f'{re.escape(punct)}\\s+', punct, text)

        # 英文标点符号处理
        english_punctuation = ',.!?;:'
        for punct in english_punctuation:
            # 确保英文标点后有适当空格（但不在行尾）
            text = re.sub(f'{re.escape(punct)}(?=[a-zA-Z0-9])', f'{punct} ', text)

        # 修复括号间距
        text = re.sub(r'\(\s+', '(', text)
        text = re.sub(r'\s+\)', ')', text)
        text = re.sub(r'\[\s+', '[', text)
        text = re.sub(r'\s+\]', ']', text)

        return text

    def _optimize_chinese_english_spacing(self, text: str) -> str:
        """优化中英文混排间距"""
        # 中文字符和英文字符之间添加空格
        text = re.sub(r'([\u4e00-\u9fa5])([a-zA-Z0-9])', r'\1 \2', text)
        text = re.sub(r'([a-zA-Z0-9])([\u4e00-\u9fa5])', r'\1 \2', text)

        # 中文和数字之间的空格
        text = re.sub(r'([\u4e00-\u9fa5])(\d)', r'\1 \2', text)
        text = re.sub(r'(\d)([\u4e00-\u9fa5])', r'\1 \2', text)

        # 避免在标点符号周围添加多余空格
        text = re.sub(r'([\u4e00-\u9fa5])\s+([，。！？；：])', r'\1\2', text)

        return text

    def _optimize_paragraph_structure(self, text: str, log_prefix: str) -> str:
        """优化段落结构"""
        if not self.typography_config["enable_paragraph_optimization"]:
            return text

        original_text = text

        # 1. 智能段落分割
        text = self._smart_paragraph_splitting(text)

        # 2. 修复段落间距
        text = self._fix_paragraph_spacing(text)

        if text != original_text:
            self.stats["paragraph_fixes"] += 1
            logger.info(f"{log_prefix}: Applied paragraph structure optimization")

        return text

    def _smart_paragraph_splitting(self, text: str) -> str:
        """智能段落分割"""
        lines = text.split('\n')
        result = []

        for i, line in enumerate(lines):
            result.append(line)

            # 检查是否需要在当前行后添加段落分隔
            if i < len(lines) - 1:
                current_line = line.strip()
                next_line = lines[i + 1].strip()

                # 如果当前行以句号、感叹号、问号结尾，且下一行不是空行或列表项
                if (current_line and
                    current_line[-1] in '。！？.!?' and
                    next_line and
                    not next_line.startswith(('- ', '* ', '+ ', '1. ', '2. ', '3. ', '4. ', '5. ')) and
                    not next_line.startswith('#') and
                    not next_line.startswith('```') and
                    not next_line.startswith('|')):

                    # 检查下一行是否已经有足够的空行分隔
                    if i + 1 < len(lines) and lines[i + 1] != '':
                        result.append('')  # 添加空行分隔段落

        return '\n'.join(result)

    def _fix_paragraph_spacing(self, text: str) -> str:
        """修复段落间距"""
        # 限制连续空行数量
        max_newlines = self.typography_config["max_consecutive_newlines"]
        pattern = f'\\n{{{max_newlines + 1},}}'
        replacement = '\n' * max_newlines
        text = re.sub(pattern, replacement, text)

        # 确保标题前后有适当间距
        text = re.sub(r'\n(#{1,6}\s+[^\n]+)\n(?!\n)', r'\n\n\1\n\n', text)

        # 确保代码块前后有适当间距
        text = re.sub(r'\n```([^\n]*)\n', r'\n\n```\1\n', text)
        text = re.sub(r'\n```\n(?!\n)', r'\n```\n\n', text)

        return text

# 全局实例
markdown_processor = UnifiedMarkdownProcessor()

def process_content_for_model(text: str, request_id: str = "", model_type: str = "general") -> str:
    """
    便捷函数：处理指定模型类型的内容
    
    Args:
        text: 原始文本
        request_id: 请求ID
        model_type: 模型类型 ("gemini", "openai", "claude", "general")
    
    Returns:
        处理后的文本
    """
    return markdown_processor.process_content(text, request_id, model_type)