"""
文本格式化质量评估器
提供格式化效果的量化指标和质量评估
"""

import re
import logging
import math
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class QualityLevel(Enum):
    """质量等级"""
    EXCELLENT = "excellent"  # 90-100分
    GOOD = "good"           # 75-89分
    FAIR = "fair"           # 60-74分
    POOR = "poor"           # 40-59分
    VERY_POOR = "very_poor" # 0-39分

@dataclass
class QualityMetrics:
    """质量指标"""
    overall_score: float
    readability_score: float
    formatting_score: float
    consistency_score: float
    structure_score: float
    math_quality_score: float
    code_quality_score: float
    table_quality_score: float
    
    # 详细指标
    line_length_variance: float
    whitespace_consistency: float
    punctuation_accuracy: float
    markdown_compliance: float
    
    # 问题统计
    formatting_issues: int
    consistency_issues: int
    structure_issues: int
    
    quality_level: QualityLevel
    recommendations: List[str]

class FormattingQualityAssessor:
    """格式化质量评估器"""
    
    def __init__(self):
        self.weights = {
            "readability": 0.25,
            "formatting": 0.25,
            "consistency": 0.20,
            "structure": 0.15,
            "math_quality": 0.05,
            "code_quality": 0.05,
            "table_quality": 0.05
        }
        
        self.thresholds = {
            "max_line_length": 120,
            "ideal_line_length": 80,
            "max_consecutive_newlines": 3,
            "min_paragraph_length": 20,
            "max_whitespace_ratio": 0.4
        }
        
        self.stats = {
            "assessments_performed": 0,
            "average_score": 0.0,
            "quality_distribution": {level.value: 0 for level in QualityLevel}
        }
    
    def assess_quality(self, original_text: str, formatted_text: str) -> QualityMetrics:
        """评估格式化质量"""
        if not formatted_text or not formatted_text.strip():
            return self._create_empty_metrics()
        
        # 计算各项指标
        readability_score = self._assess_readability(formatted_text)
        formatting_score = self._assess_formatting(formatted_text)
        consistency_score = self._assess_consistency(formatted_text)
        structure_score = self._assess_structure(formatted_text)
        math_quality_score = self._assess_math_quality(formatted_text)
        code_quality_score = self._assess_code_quality(formatted_text)
        table_quality_score = self._assess_table_quality(formatted_text)
        
        # 计算综合分数
        overall_score = (
            readability_score * self.weights["readability"] +
            formatting_score * self.weights["formatting"] +
            consistency_score * self.weights["consistency"] +
            structure_score * self.weights["structure"] +
            math_quality_score * self.weights["math_quality"] +
            code_quality_score * self.weights["code_quality"] +
            table_quality_score * self.weights["table_quality"]
        )
        
        # 计算详细指标
        line_length_variance = self._calculate_line_length_variance(formatted_text)
        whitespace_consistency = self._assess_whitespace_consistency(formatted_text)
        punctuation_accuracy = self._assess_punctuation_accuracy(formatted_text)
        markdown_compliance = self._assess_markdown_compliance(formatted_text)
        
        # 统计问题
        formatting_issues = self._count_formatting_issues(formatted_text)
        consistency_issues = self._count_consistency_issues(formatted_text)
        structure_issues = self._count_structure_issues(formatted_text)
        
        # 确定质量等级
        quality_level = self._determine_quality_level(overall_score)
        
        # 生成建议
        recommendations = self._generate_recommendations(
            formatted_text, overall_score, {
                "readability": readability_score,
                "formatting": formatting_score,
                "consistency": consistency_score,
                "structure": structure_score
            }
        )
        
        # 更新统计
        self._update_stats(overall_score, quality_level)
        
        return QualityMetrics(
            overall_score=overall_score,
            readability_score=readability_score,
            formatting_score=formatting_score,
            consistency_score=consistency_score,
            structure_score=structure_score,
            math_quality_score=math_quality_score,
            code_quality_score=code_quality_score,
            table_quality_score=table_quality_score,
            line_length_variance=line_length_variance,
            whitespace_consistency=whitespace_consistency,
            punctuation_accuracy=punctuation_accuracy,
            markdown_compliance=markdown_compliance,
            formatting_issues=formatting_issues,
            consistency_issues=consistency_issues,
            structure_issues=structure_issues,
            quality_level=quality_level,
            recommendations=recommendations
        )
    
    def _assess_readability(self, text: str) -> float:
        """评估可读性"""
        score = 100.0
        lines = text.split('\n')
        
        # 行长度评估
        long_lines = sum(1 for line in lines if len(line) > self.thresholds["max_line_length"])
        if long_lines > 0:
            score -= min(long_lines * 5, 30)
        
        # 段落长度评估
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        very_long_paragraphs = sum(1 for p in paragraphs if len(p) > 1000)
        if very_long_paragraphs > 0:
            score -= min(very_long_paragraphs * 10, 25)
        
        # 空白字符比例
        whitespace_ratio = len(re.findall(r'\s', text)) / len(text) if text else 0
        if whitespace_ratio > self.thresholds["max_whitespace_ratio"]:
            score -= 15
        
        return max(score, 0.0)
    
    def _assess_formatting(self, text: str) -> float:
        """评估格式化质量"""
        score = 100.0
        
        # 检查Markdown格式
        markdown_issues = 0
        
        # 标题格式检查
        title_pattern = r'^#{1,6}\s+.+'
        titles = re.findall(title_pattern, text, re.MULTILINE)
        malformed_titles = re.findall(r'^#{1,6}[^\s]', text, re.MULTILINE)
        if malformed_titles:
            markdown_issues += len(malformed_titles)
        
        # 列表格式检查
        list_items = re.findall(r'^[-*+]\s+', text, re.MULTILINE)
        malformed_lists = re.findall(r'^[-*+][^\s]', text, re.MULTILINE)
        if malformed_lists:
            markdown_issues += len(malformed_lists)
        
        # 代码块格式检查
        code_blocks = re.findall(r'```.*?```', text, re.DOTALL)
        unclosed_code_blocks = text.count('```') % 2
        if unclosed_code_blocks:
            markdown_issues += 1
        
        # 数学公式格式检查
        math_delimiters = text.count('$')
        if math_delimiters % 2 != 0:
            markdown_issues += 1
        
        score -= min(markdown_issues * 5, 40)
        
        return max(score, 0.0)
    
    def _assess_consistency(self, text: str) -> float:
        """评估一致性"""
        score = 100.0
        
        # 列表标记一致性
        list_markers = re.findall(r'^([-*+])\s+', text, re.MULTILINE)
        if len(set(list_markers)) > 1:
            score -= 10
        
        # 标题层级一致性
        headers = re.findall(r'^(#{1,6})\s+', text, re.MULTILINE)
        if headers:
            header_levels = [len(h) for h in headers]
            # 检查是否有跳级
            for i in range(1, len(header_levels)):
                if header_levels[i] - header_levels[i-1] > 1:
                    score -= 5
        
        # 空行使用一致性
        consecutive_newlines = re.findall(r'\n{2,}', text)
        if consecutive_newlines:
            newline_counts = [len(nl) for nl in consecutive_newlines]
            if len(set(newline_counts)) > 2:  # 允许两种空行模式
                score -= 10
        
        return max(score, 0.0)
    
    def _assess_structure(self, text: str) -> float:
        """评估结构质量"""
        score = 100.0
        
        # 段落结构
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        if not paragraphs:
            return 0.0
        
        # 检查段落长度分布
        paragraph_lengths = [len(p) for p in paragraphs]
        avg_length = sum(paragraph_lengths) / len(paragraph_lengths)
        
        # 过短段落惩罚
        short_paragraphs = sum(1 for length in paragraph_lengths if length < self.thresholds["min_paragraph_length"])
        if short_paragraphs > len(paragraphs) * 0.3:
            score -= 15
        
        # 层次结构检查
        headers = re.findall(r'^#{1,6}\s+', text, re.MULTILINE)
        if len(text) > 500 and not headers:
            score -= 20  # 长文本缺少标题结构
        
        return max(score, 0.0)
    
    def _assess_math_quality(self, text: str) -> float:
        """评估数学公式质量"""
        if '$' not in text:
            return 100.0
        
        score = 100.0
        
        # 检查数学分隔符平衡
        single_dollar = text.count('$') - text.count('$$') * 2
        if single_dollar % 2 != 0:
            score -= 30
        
        # 检查常见LaTeX错误
        latex_errors = [
            r'\\frac\s+\w+\s+\w+',  # 未用大括号的分数
            r'\\sqrt\s+\w+',        # 未用大括号的根号
            r'\$\s+[^$]+\s+\$',     # 数学公式内多余空格
        ]
        
        for pattern in latex_errors:
            matches = re.findall(pattern, text)
            score -= len(matches) * 5
        
        return max(score, 0.0)
    
    def _assess_code_quality(self, text: str) -> float:
        """评估代码块质量"""
        if '```' not in text:
            return 100.0
        
        score = 100.0
        
        # 检查代码块闭合
        if text.count('```') % 2 != 0:
            score -= 40
        
        # 检查代码块语言标识
        code_blocks = re.findall(r'```(\w*)\n', text)
        unlabeled_blocks = sum(1 for lang in code_blocks if not lang)
        if unlabeled_blocks > 0:
            score -= unlabeled_blocks * 10
        
        return max(score, 0.0)
    
    def _assess_table_quality(self, text: str) -> float:
        """评估表格质量"""
        if '|' not in text:
            return 100.0
        
        score = 100.0
        
        # 查找表格行
        table_lines = [line for line in text.split('\n') if '|' in line and not line.strip().startswith('```')]
        
        if not table_lines:
            return 100.0
        
        # 检查表格格式一致性
        for line in table_lines:
            cells = line.split('|')
            # 检查是否有空的开始或结束
            if line.startswith('|') and line.endswith('|'):
                continue
            elif not line.startswith('|') and not line.endswith('|'):
                continue
            else:
                score -= 5  # 格式不一致
        
        return max(score, 0.0)
    
    def _calculate_line_length_variance(self, text: str) -> float:
        """计算行长度方差"""
        lines = [line for line in text.split('\n') if line.strip()]
        if not lines:
            return 0.0
        
        lengths = [len(line) for line in lines]
        mean_length = sum(lengths) / len(lengths)
        variance = sum((length - mean_length) ** 2 for length in lengths) / len(lengths)
        
        return math.sqrt(variance)
    
    def _assess_whitespace_consistency(self, text: str) -> float:
        """评估空白字符一致性"""
        # 检查缩进一致性
        indented_lines = [line for line in text.split('\n') if line.startswith(' ') or line.startswith('\t')]
        if not indented_lines:
            return 100.0
        
        # 检查是否混用空格和制表符
        space_indents = sum(1 for line in indented_lines if line.startswith(' '))
        tab_indents = sum(1 for line in indented_lines if line.startswith('\t'))
        
        if space_indents > 0 and tab_indents > 0:
            return 50.0  # 混用空格和制表符
        
        return 100.0
    
    def _assess_punctuation_accuracy(self, text: str) -> float:
        """评估标点符号准确性"""
        score = 100.0
        
        # 检查常见标点问题
        issues = [
            len(re.findall(r'[，。！？；：]\s+[a-zA-Z]', text)),  # 中文标点后跟英文
            len(re.findall(r'[a-zA-Z][，。！？；：]', text)),     # 英文后跟中文标点
            len(re.findall(r'[.!?;:]\w', text)),                # 英文标点后无空格
        ]
        
        total_issues = sum(issues)
        score -= min(total_issues * 3, 30)
        
        return max(score, 0.0)
    
    def _assess_markdown_compliance(self, text: str) -> float:
        """评估Markdown规范遵循度"""
        score = 100.0
        
        # 检查各种Markdown元素的规范性
        violations = 0
        
        # 标题后应有空行
        headers = re.findall(r'^#{1,6}\s+.*\n(?!\n)', text, re.MULTILINE)
        violations += len(headers)
        
        # 列表项格式
        malformed_lists = re.findall(r'^[-*+](?!\s)', text, re.MULTILINE)
        violations += len(malformed_lists)
        
        score -= min(violations * 5, 40)
        
        return max(score, 0.0)
    
    def _count_formatting_issues(self, text: str) -> int:
        """统计格式化问题"""
        issues = 0
        
        # 未闭合的代码块
        if text.count('```') % 2 != 0:
            issues += 1
        
        # 未闭合的数学公式
        if text.count('$') % 2 != 0:
            issues += 1
        
        # 格式错误的标题
        issues += len(re.findall(r'^#{1,6}[^\s]', text, re.MULTILINE))
        
        # 格式错误的列表
        issues += len(re.findall(r'^[-*+][^\s]', text, re.MULTILINE))
        
        return issues
    
    def _count_consistency_issues(self, text: str) -> int:
        """统计一致性问题"""
        issues = 0
        
        # 混用列表标记
        list_markers = set(re.findall(r'^([-*+])\s+', text, re.MULTILINE))
        if len(list_markers) > 1:
            issues += 1
        
        # 混用缩进方式
        has_spaces = bool(re.search(r'^\s+', text, re.MULTILINE))
        has_tabs = bool(re.search(r'^\t+', text, re.MULTILINE))
        if has_spaces and has_tabs:
            issues += 1
        
        return issues
    
    def _count_structure_issues(self, text: str) -> int:
        """统计结构问题"""
        issues = 0
        
        # 标题层级跳跃
        headers = re.findall(r'^(#{1,6})\s+', text, re.MULTILINE)
        if headers:
            header_levels = [len(h) for h in headers]
            for i in range(1, len(header_levels)):
                if header_levels[i] - header_levels[i-1] > 1:
                    issues += 1
        
        # 过多连续空行
        excessive_newlines = re.findall(r'\n{4,}', text)
        issues += len(excessive_newlines)
        
        return issues
    
    def _determine_quality_level(self, score: float) -> QualityLevel:
        """确定质量等级"""
        if score >= 90:
            return QualityLevel.EXCELLENT
        elif score >= 75:
            return QualityLevel.GOOD
        elif score >= 60:
            return QualityLevel.FAIR
        elif score >= 40:
            return QualityLevel.POOR
        else:
            return QualityLevel.VERY_POOR
    
    def _generate_recommendations(self, text: str, overall_score: float, scores: Dict[str, float]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if scores["readability"] < 70:
            recommendations.append("建议优化行长度，避免过长的行和段落")
        
        if scores["formatting"] < 70:
            recommendations.append("建议检查Markdown格式，确保标题、列表、代码块格式正确")
        
        if scores["consistency"] < 70:
            recommendations.append("建议保持格式一致性，统一使用相同的列表标记和缩进方式")
        
        if scores["structure"] < 70:
            recommendations.append("建议改善文档结构，添加适当的标题和段落分割")
        
        if '```' in text and text.count('```') % 2 != 0:
            recommendations.append("发现未闭合的代码块，请检查代码块格式")
        
        if '$' in text and text.count('$') % 2 != 0:
            recommendations.append("发现未闭合的数学公式，请检查数学公式格式")
        
        if overall_score < 60:
            recommendations.append("整体格式化质量较低，建议进行全面的格式优化")
        
        return recommendations
    
    def _create_empty_metrics(self) -> QualityMetrics:
        """创建空的质量指标"""
        return QualityMetrics(
            overall_score=0.0,
            readability_score=0.0,
            formatting_score=0.0,
            consistency_score=0.0,
            structure_score=0.0,
            math_quality_score=0.0,
            code_quality_score=0.0,
            table_quality_score=0.0,
            line_length_variance=0.0,
            whitespace_consistency=0.0,
            punctuation_accuracy=0.0,
            markdown_compliance=0.0,
            formatting_issues=0,
            consistency_issues=0,
            structure_issues=0,
            quality_level=QualityLevel.VERY_POOR,
            recommendations=["文本为空或无效"]
        )
    
    def _update_stats(self, score: float, quality_level: QualityLevel):
        """更新统计信息"""
        self.stats["assessments_performed"] += 1
        
        # 更新平均分数
        current_avg = self.stats["average_score"]
        count = self.stats["assessments_performed"]
        self.stats["average_score"] = (current_avg * (count - 1) + score) / count
        
        # 更新质量分布
        self.stats["quality_distribution"][quality_level.value] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "assessments_performed": 0,
            "average_score": 0.0,
            "quality_distribution": {level.value: 0 for level in QualityLevel}
        }

# 全局实例
quality_assessor = FormattingQualityAssessor()
