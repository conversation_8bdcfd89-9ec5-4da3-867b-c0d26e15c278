package com.example.everytalk.util

/**
 * 智能文本处理器
 * 负责处理思考标签、智能预处理等高级文本处理功能
 */
class IntelligentTextProcessor(
    private var formatConfig: FormatCorrectionConfig,
    private val performanceMetrics: com.example.everytalk.data.PerformanceMetrics
) {
    
    /**
     * 更新配置
     */
    fun updateConfig(config: FormatCorrectionConfig) {
        this.formatConfig = config
    }
    
    /**
     * 重置思考标签状态
     */
    fun resetThinkTagState() {
        // 重置内部状态（如果有的话）
    }
    
    /**
     * 处理思考标签，返回思考内容和常规内容
     */
    fun processThinkTags(text: String): Pair<String?, String?> {
        // 提取思考内容的正则表达式
        val thinkPatterns = listOf(
            "<think>([\\s\\S]*?)</think>".toRegex(),
            "<thinking>([\\s\\S]*?)</thinking>".toRegex(),
            "\\*\\*思考过程\\*\\*([\\s\\S]*?)(?=\\n\\n|\\*\\*|$)".toRegex(),
            "思考：([\\s\\S]*?)(?=\\n\\n|$)".toRegex()
        )
        
        var thinkingContent: String? = null
        var processedText = text
        
        // 提取思考内容
        for (pattern in thinkPatterns) {
            val matches = pattern.findAll(text)
            if (matches.any()) {
                thinkingContent = matches.map { it.groupValues[1].trim() }.joinToString("\n\n")
                processedText = pattern.replace(text, "")
                break
            }
        }
        
        // 清理处理后的文本
        processedText = processedText.replace(Regex("\\n{3,}"), "\n\n").trim()
        
        return Pair(
            thinkingContent?.takeIf { it.isNotBlank() },
            processedText.takeIf { it.isNotBlank() }
        )
    }
    
    /**
     * 渐进式矫正
     */
    fun progressiveCorrection(text: String): String {
        if (text.isBlank()) return text
        
        var corrected = text
        
        // 基础清理
        corrected = corrected.replace(Regex("\\s+"), " ")
        corrected = corrected.replace(Regex("\\n\\s*\\n\\s*\\n"), "\n\n")
        
        // 修复常见的标点符号问题
        corrected = corrected.replace(Regex("([.!?])([A-Z])"), "$1 $2")
        corrected = corrected.replace(Regex("([。！？])([\\u4e00-\\u9fa5])"), "$1$2")
        
        return corrected.trim()
    }
    
    /**
     * 超级智能预处理
     */
    fun superIntelligentPreprocessing(text: String): String {
        if (text.isBlank()) return text
        
        var processed = text
        
        // 智能格式优化
        processed = optimizeMarkdownFormat(processed)
        processed = optimizeCodeBlocks(processed)
        processed = optimizeMathFormulas(processed)
        
        return processed
    }
    
    /**
     * 优化Markdown格式
     */
    private fun optimizeMarkdownFormat(text: String): String {
        var optimized = text
        
        // 确保标题格式正确
        optimized = optimized.replace(Regex("^(#{1,6})([^\\s#])"), "$1 $2")
        optimized = optimized.replace(Regex("\n(#{1,6})([^\\s#])"), "\n$1 $2")
        
        // 确保列表格式正确
        optimized = optimized.replace(Regex("^([\\-\\*\\+])([^\\s])"), "$1 $2")
        optimized = optimized.replace(Regex("\n([\\-\\*\\+])([^\\s])"), "\n$1 $2")
        
        // 确保有序列表格式正确
        optimized = optimized.replace(Regex("^(\\d+\\.)([^\\s])"), "$1 $2")
        optimized = optimized.replace(Regex("\n(\\d+\\.)([^\\s])"), "\n$1 $2")
        
        // 确保代码块标记前后有换行
        optimized = optimized.replace(Regex("([^\\n])```"), "$1\n```")
        optimized = optimized.replace(Regex("```([^\\n])"), "```\n$1")
        
        // 保留自然的段落结构 - 只清理过度的空行
        optimized = optimized.replace(Regex("\\n{5,}"), "\n\n\n")
        
        return optimized
    }
    
    /**
     * 优化代码块
     */
    private fun optimizeCodeBlocks(text: String): String {
        var optimized = text
        
        // 确保代码块标记前后有换行
        optimized = optimized.replace(Regex("([^\\n])```"), "$1\n```")
        optimized = optimized.replace(Regex("```([^\\n])"), "```\n$1")
        
        return optimized
    }
    
    /**
     * 优化数学公式
     */
    private fun optimizeMathFormulas(text: String): String {
        var optimized = text
        
        // 标准化数学公式分隔符
        optimized = optimized.replace(Regex("\\\\\\(([^)]+)\\\\\\)"), "$$1$")
        optimized = optimized.replace(Regex("\\\\\\[([^]]+)\\\\\\]"), "$$$1$$")
        
        return optimized
    }
    
}