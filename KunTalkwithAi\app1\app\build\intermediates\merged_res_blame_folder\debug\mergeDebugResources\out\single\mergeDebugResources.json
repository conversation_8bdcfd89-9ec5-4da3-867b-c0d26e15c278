[{"merged": "com.example.everytalk.app-debug-55:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.everytalk.app-debug-55:/xml_file_paths.xml.flat", "source": "com.example.everytalk.app-main-57:/xml/file_paths.xml"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-xxhdpi_kztalk.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-xxhdpi/kztalk.webp"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.everytalk.app-debug-55:/drawable_ic_foreground_logo.webp.flat", "source": "com.example.everytalk.app-main-57:/drawable/ic_foreground_logo.webp"}, {"merged": "com.example.everytalk.app-debug-55:/xml_data_extraction_rules.xml.flat", "source": "com.example.everytalk.app-main-57:/xml/data_extraction_rules.xml"}, {"merged": "com.example.everytalk.app-debug-55:/xml_backup_rules.xml.flat", "source": "com.example.everytalk.app-main-57:/xml/backup_rules.xml"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.everytalk.app-debug-55:/xml_network_security_config.xml.flat", "source": "com.example.everytalk.app-main-57:/xml/network_security_config.xml"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.everytalk.app-main-57:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.everytalk.app-main-57:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-hdpi_kztalk.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-hdpi/kztalk.webp"}, {"merged": "com.example.everytalk.app-debug-55:/drawable_ic_launcher_background.xml.flat", "source": "com.example.everytalk.app-main-57:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-xhdpi_kztalk.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-xhdpi/kztalk.webp"}, {"merged": "com.example.everytalk.app-debug-55:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.everytalk.app-main-57:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.everytalk.app-debug-55:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.example.everytalk.app-main-57:/mipmap-hdpi/ic_launcher_foreground.webp"}]