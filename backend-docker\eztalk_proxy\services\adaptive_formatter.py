"""
自适应格式化策略处理器
根据不同AI模型的输出特点，实现自适应的格式化策略
"""

import re
import logging
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum

logger = logging.getLogger(__name__)

class ModelType(Enum):
    """AI模型类型"""
    OPENAI = "openai"
    CLAUDE = "claude"
    GEMINI = "gemini"
    DEEPSEEK = "deepseek"
    QWEN = "qwen"
    GENERAL = "general"

class FormattingIntensity(Enum):
    """格式化强度"""
    LIGHT = "light"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"

class AdaptiveFormattingStrategy:
    """自适应格式化策略"""
    
    def __init__(self):
        # 不同模型的特性配置
        self.model_characteristics = {
            ModelType.OPENAI: {
                "common_issues": ["excessive_whitespace", "incomplete_code_blocks", "math_formatting"],
                "formatting_intensity": FormattingIntensity.MODERATE,
                "special_handling": ["reasoning_blocks", "step_by_step"],
                "math_formula_style": "katex",
                "code_block_preference": "fenced",
                "list_style": "markdown",
                "table_alignment": "auto"
            },
            ModelType.CLAUDE: {
                "common_issues": ["verbose_explanations", "nested_formatting", "citation_format"],
                "formatting_intensity": FormattingIntensity.LIGHT,
                "special_handling": ["thinking_tags", "artifact_blocks"],
                "math_formula_style": "latex",
                "code_block_preference": "fenced",
                "list_style": "structured",
                "table_alignment": "left"
            },
            ModelType.GEMINI: {
                "common_issues": ["search_results", "citation_links", "mixed_content"],
                "formatting_intensity": FormattingIntensity.MODERATE,
                "special_handling": ["search_integration", "web_citations"],
                "math_formula_style": "katex",
                "code_block_preference": "fenced",
                "list_style": "numbered",
                "table_alignment": "center"
            },
            ModelType.DEEPSEEK: {
                "common_issues": ["code_heavy_output", "technical_formatting", "algorithm_blocks"],
                "formatting_intensity": FormattingIntensity.AGGRESSIVE,
                "special_handling": ["code_analysis", "algorithm_steps"],
                "math_formula_style": "katex",
                "code_block_preference": "highlighted",
                "list_style": "technical",
                "table_alignment": "auto"
            },
            ModelType.QWEN: {
                "common_issues": ["chinese_english_mixing", "punctuation_spacing", "cultural_context"],
                "formatting_intensity": FormattingIntensity.MODERATE,
                "special_handling": ["chinese_formatting", "bilingual_content"],
                "math_formula_style": "katex",
                "code_block_preference": "fenced",
                "list_style": "mixed",
                "table_alignment": "auto"
            },
            ModelType.GENERAL: {
                "common_issues": ["general_formatting", "basic_cleanup"],
                "formatting_intensity": FormattingIntensity.MODERATE,
                "special_handling": [],
                "math_formula_style": "katex",
                "code_block_preference": "fenced",
                "list_style": "markdown",
                "table_alignment": "auto"
            }
        }
        
        self.stats = {
            "model_specific_fixes": 0,
            "adaptive_adjustments": 0,
            "intensity_changes": 0,
            "strategy_switches": 0
        }
    
    def get_formatting_strategy(self, model_type: str, content: str) -> Dict[str, Any]:
        """获取自适应格式化策略"""
        try:
            model_enum = ModelType(model_type.lower())
        except ValueError:
            model_enum = ModelType.GENERAL
        
        base_config = self.model_characteristics[model_enum].copy()
        
        # 根据内容特征调整策略
        content_analysis = self._analyze_content(content)
        adjusted_config = self._adjust_strategy_based_on_content(base_config, content_analysis)
        
        return adjusted_config
    
    def _analyze_content(self, content: str) -> Dict[str, Any]:
        """分析内容特征"""
        analysis = {
            "has_math": bool(re.search(r'\$.*?\$|\\\(.*?\\\)|\\\[.*?\\\]', content)),
            "has_code": bool(re.search(r'```|`[^`]+`', content)),
            "has_tables": bool(re.search(r'\|.*\|', content)),
            "has_lists": bool(re.search(r'^[-*+]\s|^\d+\.\s', content, re.MULTILINE)),
            "has_chinese": bool(re.search(r'[\u4e00-\u9fa5]', content)),
            "has_citations": bool(re.search(r'\[\d+\]|\[.*?\]\(.*?\)', content)),
            "content_length": len(content),
            "line_count": content.count('\n') + 1,
            "whitespace_ratio": len(re.findall(r'\s', content)) / len(content) if content else 0,
            "complexity_score": self._calculate_complexity_score(content)
        }
        
        return analysis
    
    def _calculate_complexity_score(self, content: str) -> float:
        """计算内容复杂度分数"""
        score = 0.0
        
        # 基于不同元素的复杂度
        score += content.count('$') * 0.1  # 数学公式
        score += content.count('```') * 0.2  # 代码块
        score += content.count('|') * 0.05  # 表格
        score += len(re.findall(r'^[-*+]\s', content, re.MULTILINE)) * 0.03  # 列表项
        score += len(re.findall(r'^\d+\.\s', content, re.MULTILINE)) * 0.03  # 有序列表
        score += content.count('#') * 0.02  # 标题
        score += len(re.findall(r'\[.*?\]\(.*?\)', content)) * 0.04  # 链接
        
        # 基于长度的复杂度
        if len(content) > 5000:
            score += 0.5
        elif len(content) > 2000:
            score += 0.3
        elif len(content) > 1000:
            score += 0.1
        
        return min(score, 10.0)  # 限制最大分数
    
    def _adjust_strategy_based_on_content(self, base_config: Dict[str, Any], analysis: Dict[str, Any]) -> Dict[str, Any]:
        """根据内容分析调整策略"""
        adjusted_config = base_config.copy()
        
        # 根据复杂度调整格式化强度
        if analysis["complexity_score"] > 5.0:
            if base_config["formatting_intensity"] == FormattingIntensity.LIGHT:
                adjusted_config["formatting_intensity"] = FormattingIntensity.MODERATE
                self.stats["intensity_changes"] += 1
        elif analysis["complexity_score"] < 2.0:
            if base_config["formatting_intensity"] == FormattingIntensity.AGGRESSIVE:
                adjusted_config["formatting_intensity"] = FormattingIntensity.MODERATE
                self.stats["intensity_changes"] += 1
        
        # 根据内容特征添加特殊处理
        special_handling = list(adjusted_config["special_handling"])
        
        if analysis["has_math"] and "math_optimization" not in special_handling:
            special_handling.append("math_optimization")
        
        if analysis["has_code"] and "code_optimization" not in special_handling:
            special_handling.append("code_optimization")
        
        if analysis["has_tables"] and "table_optimization" not in special_handling:
            special_handling.append("table_optimization")
        
        if analysis["has_chinese"] and "chinese_optimization" not in special_handling:
            special_handling.append("chinese_optimization")
        
        if analysis["whitespace_ratio"] > 0.3 and "whitespace_cleanup" not in special_handling:
            special_handling.append("whitespace_cleanup")
        
        adjusted_config["special_handling"] = special_handling
        
        # 根据内容长度调整处理策略
        if analysis["content_length"] > 10000:
            adjusted_config["chunk_processing"] = True
            adjusted_config["chunk_size"] = 2000
        else:
            adjusted_config["chunk_processing"] = False
        
        return adjusted_config
    
    def apply_adaptive_formatting(self, content: str, model_type: str) -> str:
        """应用自适应格式化"""
        strategy = self.get_formatting_strategy(model_type, content)
        
        formatted_content = content
        
        # 应用特殊处理
        for handling in strategy["special_handling"]:
            if handling == "math_optimization":
                formatted_content = self._optimize_math_content(formatted_content)
            elif handling == "code_optimization":
                formatted_content = self._optimize_code_content(formatted_content)
            elif handling == "table_optimization":
                formatted_content = self._optimize_table_content(formatted_content)
            elif handling == "chinese_optimization":
                formatted_content = self._optimize_chinese_content(formatted_content)
            elif handling == "whitespace_cleanup":
                formatted_content = self._cleanup_whitespace(formatted_content)
            elif handling == "reasoning_blocks":
                formatted_content = self._format_reasoning_blocks(formatted_content)
            elif handling == "search_integration":
                formatted_content = self._format_search_results(formatted_content)
        
        # 根据格式化强度应用不同级别的处理
        intensity = strategy["formatting_intensity"]
        if intensity == FormattingIntensity.AGGRESSIVE:
            formatted_content = self._apply_aggressive_formatting(formatted_content)
        elif intensity == FormattingIntensity.MODERATE:
            formatted_content = self._apply_moderate_formatting(formatted_content)
        elif intensity == FormattingIntensity.LIGHT:
            formatted_content = self._apply_light_formatting(formatted_content)
        
        self.stats["adaptive_adjustments"] += 1
        return formatted_content
    
    def _optimize_math_content(self, content: str) -> str:
        """优化数学内容"""
        # 确保数学公式格式正确
        content = re.sub(r'\$\s+([^$]+?)\s+\$', r'$\1$', content)
        content = re.sub(r'\$\$\s+([^$]+?)\s+\$\$', r'$$\1$$', content)
        return content
    
    def _optimize_code_content(self, content: str) -> str:
        """优化代码内容"""
        # 确保代码块格式正确
        content = re.sub(r'```\s*(\w+)?\s*\n', r'```\1\n', content)
        content = re.sub(r'\n\s*```', r'\n```', content)
        return content
    
    def _optimize_table_content(self, content: str) -> str:
        """优化表格内容"""
        # 标准化表格格式
        lines = content.split('\n')
        result = []
        for line in lines:
            if '|' in line and not line.strip().startswith('```'):
                # 标准化表格行
                cells = [cell.strip() for cell in line.split('|')]
                if cells and cells[0] == '':
                    cells = cells[1:]
                if cells and cells[-1] == '':
                    cells = cells[:-1]
                if cells:
                    formatted_line = '| ' + ' | '.join(cells) + ' |'
                    result.append(formatted_line)
                else:
                    result.append(line)
            else:
                result.append(line)
        return '\n'.join(result)
    
    def _optimize_chinese_content(self, content: str) -> str:
        """优化中文内容"""
        # 中英文混排优化
        content = re.sub(r'([\u4e00-\u9fa5])([a-zA-Z0-9])', r'\1 \2', content)
        content = re.sub(r'([a-zA-Z0-9])([\u4e00-\u9fa5])', r'\1 \2', content)
        return content
    
    def _cleanup_whitespace(self, content: str) -> str:
        """清理空白字符"""
        # 移除多余的空白
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = re.sub(r'[ \t]+', ' ', content)
        return content.strip()
    
    def _format_reasoning_blocks(self, content: str) -> str:
        """格式化推理块"""
        # OpenAI特有的推理格式
        content = re.sub(r'<thinking>(.*?)</thinking>', r'**思考过程：**\n\1\n', content, flags=re.DOTALL)
        return content
    
    def _format_search_results(self, content: str) -> str:
        """格式化搜索结果"""
        # Gemini特有的搜索结果格式
        content = re.sub(r'\[(\d+)\]\((https?://[^)]+)\)', r'[\1](\2)', content)
        return content
    
    def _apply_aggressive_formatting(self, content: str) -> str:
        """应用激进格式化"""
        # 最大程度的格式化处理
        content = self._cleanup_whitespace(content)
        content = self._optimize_math_content(content)
        content = self._optimize_code_content(content)
        content = self._optimize_table_content(content)
        return content
    
    def _apply_moderate_formatting(self, content: str) -> str:
        """应用适中格式化"""
        # 平衡的格式化处理
        content = re.sub(r'\n{3,}', '\n\n', content)
        content = self._optimize_math_content(content)
        return content
    
    def _apply_light_formatting(self, content: str) -> str:
        """应用轻度格式化"""
        # 最小程度的格式化处理
        content = re.sub(r'\n{4,}', '\n\n\n', content)
        return content.strip()
    
    def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        for key in self.stats:
            self.stats[key] = 0

# 全局实例
adaptive_formatter = AdaptiveFormattingStrategy()
