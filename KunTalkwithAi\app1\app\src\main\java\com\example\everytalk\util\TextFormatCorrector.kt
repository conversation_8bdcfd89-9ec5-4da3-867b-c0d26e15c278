package com.example.everytalk.util

import com.example.everytalk.data.PerformanceMetrics

/**
 * 文本格式矫正器
 * 负责对文本进行格式化和矫正处理
 */
class TextFormatCorrector(
    private var config: FormatCorrectionConfig,
    private val performanceMetrics: PerformanceMetrics
) {
    
    /**
     * 更新配置
     */
    fun updateConfig(newConfig: FormatCorrectionConfig) {
        this.config = newConfig
    }
    
    /**
     * 清理多余的空白字符
     */
    fun cleanExcessiveWhitespace(text: String): String {
        if (text.isBlank()) return text
        
        var cleaned = text
        
        // 移除行尾空白
        cleaned = cleaned.replace(Regex("[ \\t]+$"), "")
        
        // 标准化多个空格为单个空格（但不影响换行）
        cleaned = cleaned.replace(Regex("[ \\t]+"), " ")
        
        // 只移除过度的空行（保留最多3个连续换行，允许更自然的段落分隔）
        cleaned = cleaned.replace(Regex("\\n{5,}"), "\n\n\n")
        
        // 移除开头和结尾的空白
        cleaned = cleaned.trim()
        
        return cleaned
    }
    
    /**
     * 增强格式矫正
     */
    fun enhancedFormatCorrection(text: String): String {
        if (text.isBlank()) return text
        
        var corrected = text
        
        // 首先清理多余的空白
        corrected = cleanExcessiveWhitespace(corrected)
        
        // 应用基本矫正
        corrected = applyCorrection(corrected)
        
        // 额外的增强矫正
        corrected = applyEnhancedCorrections(corrected)
        
        return corrected
    }
    
    /**
     * 应用增强矫正
     */
    private fun applyEnhancedCorrections(text: String): String {
        var corrected = text
        
        // 修复代码块内的格式问题
        corrected = fixCodeBlockFormatting(corrected)
        
        // 修复数学公式格式
        corrected = fixMathFormatting(corrected)
        
        // 修复表格格式
        corrected = fixTableFormatting(corrected)
        
        // 修复链接格式
        corrected = fixLinkFormatting(corrected)
        
        return corrected
    }
    
    /**
     * 修复代码块格式
     */
    private fun fixCodeBlockFormatting(text: String): String {
        var fixed = text
        
        // 确保代码块前后有空行
        fixed = fixed.replace(Regex("([^\\n])\\n```"), "$1\n\n```")
        fixed = fixed.replace(Regex("```\\n([^\\n])"), "```\n\n$1")
        
        // 修复代码块语言标识
        fixed = fixed.replace(Regex("```\\s*([a-zA-Z]+)\\s*\\n"), "```$1\n")
        
        return fixed
    }
    
    /**
     * 修复数学公式格式
     */
    private fun fixMathFormatting(text: String): String {
        var fixed = text

        // 修复行内数学公式
        fixed = fixed.replace(Regex("\\$\\s+([^$]+?)\\s+\\$"), "$$1$")

        // 修复块级数学公式
        fixed = fixed.replace(Regex("\\$\\$\\s+([^$]+?)\\s+\\$\\$"), "$$$1$$")

        // 应用增强的数学公式处理
        fixed = enhancedMathFormatting(fixed)

        return fixed
    }

    /**
     * 增强的数学公式格式化
     */
    private fun enhancedMathFormatting(text: String): String {
        var enhanced = text

        // 修复常见的数学函数
        val mathFunctions = listOf("sin", "cos", "tan", "log", "ln", "exp", "lim", "max", "min")
        for (func in mathFunctions) {
            enhanced = enhanced.replace(Regex("\\b$func\\s*\\("), "\\\\$func(")
        }

        // 修复希腊字母
        val greekLetters = mapOf(
            "alpha" to "\\\\alpha", "beta" to "\\\\beta", "gamma" to "\\\\gamma",
            "delta" to "\\\\delta", "epsilon" to "\\\\epsilon", "theta" to "\\\\theta",
            "lambda" to "\\\\lambda", "mu" to "\\\\mu", "pi" to "\\\\pi",
            "sigma" to "\\\\sigma", "phi" to "\\\\phi", "omega" to "\\\\omega"
        )

        for ((letter, latex) in greekLetters) {
            enhanced = enhanced.replace(Regex("\\b$letter\\b"), latex)
        }

        // 修复数学符号
        enhanced = enhanced.replace(Regex("\\b(infinity|infty)\\b"), "\\\\infty")
        enhanced = enhanced.replace(Regex("\\+/-"), "\\\\pm")
        enhanced = enhanced.replace(Regex("-/\\+"), "\\\\mp")
        enhanced = enhanced.replace(Regex("\\*"), "\\\\times")
        enhanced = enhanced.replace(Regex("<="), "\\\\leq")
        enhanced = enhanced.replace(Regex(">="), "\\\\geq")
        enhanced = enhanced.replace(Regex("!="), "\\\\neq")
        enhanced = enhanced.replace(Regex("~="), "\\\\approx")

        // 修复分数表达式
        enhanced = enhanced.replace(Regex("(\\d+)/(\\d+)"), "\\\\frac{$1}{$2}")
        enhanced = enhanced.replace(Regex("([a-zA-Z]+)/([a-zA-Z]+)"), "\\\\frac{$1}{$2}")

        // 修复指数和下标
        enhanced = enhanced.replace(Regex("([a-zA-Z0-9])\\^([a-zA-Z0-9])"), "$1^{$2}")
        enhanced = enhanced.replace(Regex("([a-zA-Z0-9])_([a-zA-Z0-9])"), "$1_{$2}")

        // 修复根号表达式
        enhanced = enhanced.replace(Regex("sqrt\\(([^)]+)\\)"), "\\\\sqrt{$1}")

        // 修复求和和积分
        enhanced = enhanced.replace(Regex("sum\\(([^)]+)\\)"), "\\\\sum($1)")
        enhanced = enhanced.replace(Regex("int\\(([^)]+)\\)"), "\\\\int($1)")

        return enhanced
    }
    
    /**
     * 修复表格格式
     */
    private fun fixTableFormatting(text: String): String {
        var fixed = text

        // 标准化表格单元格间距
        fixed = fixed.replace(Regex("\\|\\s*([^|]+?)\\s*\\|"), "| $1 |")

        // 确保表格前后有空行
        fixed = fixed.replace(Regex("([^\\n])\\n\\|"), "$1\n\n|")
        fixed = fixed.replace(Regex("\\|([^\\n]*)\\n([^\\n|])"), "|$1\n\n$2")

        // 应用增强的表格处理
        fixed = enhancedTableFormatting(fixed)

        return fixed
    }

    /**
     * 增强的表格格式化
     */
    private fun enhancedTableFormatting(text: String): String {
        val lines = text.split("\n").toMutableList()
        var enhanced = mutableListOf<String>()
        var i = 0

        while (i < lines.size) {
            val line = lines[i]

            // 检查是否是表格行
            if (line.trim().startsWith("|") && line.trim().endsWith("|")) {
                val tableLines = mutableListOf<String>()

                // 收集连续的表格行
                while (i < lines.size && lines[i].trim().startsWith("|") && lines[i].trim().endsWith("|")) {
                    tableLines.add(lines[i])
                    i++
                }

                // 处理表格
                val processedTable = processTableBlock(tableLines)
                enhanced.addAll(processedTable)
            } else {
                enhanced.add(line)
                i++
            }
        }

        return enhanced.joinToString("\n")
    }

    /**
     * 处理表格块
     */
    private fun processTableBlock(tableLines: List<String>): List<String> {
        if (tableLines.isEmpty()) return emptyList()

        val processed = mutableListOf<String>()
        var maxColumns = 0

        // 确定最大列数
        for (line in tableLines) {
            val cells = line.split("|").filter { it.isNotBlank() }
            maxColumns = maxOf(maxColumns, cells.size)
        }

        // 处理每一行
        for ((index, line) in tableLines.withIndex()) {
            if (isTableSeparator(line)) {
                // 重新生成分隔符行
                val separator = "| " + (1..maxColumns).joinToString(" | ") { "---" } + " |"
                processed.add(separator)
            } else {
                // 处理数据行
                val cells = line.split("|").map { it.trim() }.filter { it.isNotEmpty() }
                val paddedCells = mutableListOf<String>()

                // 补齐列数
                for (i in 0 until maxColumns) {
                    paddedCells.add(if (i < cells.size) cells[i] else "")
                }

                val formattedLine = "| " + paddedCells.joinToString(" | ") + " |"
                processed.add(formattedLine)

                // 在第一行后添加分隔符（如果缺失）
                if (index == 0 && tableLines.size > 1 && !isTableSeparator(tableLines[1])) {
                    val separator = "| " + (1..maxColumns).joinToString(" | ") { "---" } + " |"
                    processed.add(separator)
                }
            }
        }

        return processed
    }

    /**
     * 检查是否是表格分隔符行
     */
    private fun isTableSeparator(line: String): Boolean {
        val cleaned = line.replace("|", "").replace("-", "").replace(" ", "")
        return cleaned.isEmpty()
    }
    
    /**
     * 修复链接格式
     */
    private fun fixLinkFormatting(text: String): String {
        var fixed = text
        
        // 标准化链接格式
        fixed = fixed.replace(Regex("\\[\\s*([^\\]]+?)\\s*\\]\\s*\\(\\s*([^)]+?)\\s*\\)"), "[$1]($2)")
        
        return fixed
    }
    
    /**
     * 应用格式矫正
     */
    fun applyCorrection(text: String): String {
        if (text.isBlank()) return text
        
        var corrected = text
        
        // 根据矫正强度应用不同级别的矫正
        when (config.correctionIntensity) {
            CorrectionIntensity.LIGHT -> {
                corrected = applyLightCorrection(corrected)
            }
            CorrectionIntensity.MODERATE -> {
                corrected = applyLightCorrection(corrected)
                corrected = applyModerateCorrection(corrected)
            }
            CorrectionIntensity.AGGRESSIVE -> {
                corrected = applyLightCorrection(corrected)
                corrected = applyModerateCorrection(corrected)
                corrected = applyAggressiveCorrection(corrected)
            }
        }
        
        return corrected
    }
    
    /**
     * 轻度矫正 - 基本的格式化
     */
    private fun applyLightCorrection(text: String): String {
        var corrected = text
        
        // 标准化换行符
        corrected = corrected.replace(Regex("\\r\\n?"), "\n")
        
        // 移除多余的空白字符
        corrected = corrected.replace(Regex("[ \\t]+"), " ")
        
        // 标准化标题格式
        corrected = corrected.replace(Regex("^(#{1,6})([^\\s#])"), "$1 $2")
        
        return corrected
    }
    
    /**
     * 中度矫正 - 更复杂的格式化
     */
    private fun applyModerateCorrection(text: String): String {
        var corrected = text
        
        // 修复代码块格式
        corrected = corrected.replace(Regex("```([^\\n]*)\\n"), "```$1\n")
        
        // 修复列表格式
        corrected = corrected.replace(Regex("^([*+-])([^\\s])"), "$1 $2")
        
        // 修复链接格式
        corrected = corrected.replace(Regex("\\[([^\\]]+)\\]\\s*\\(([^)]+)\\)"), "[$1]($2)")
        
        return corrected
    }
    
    /**
     * 激进矫正 - 最全面的格式化
     */
    private fun applyAggressiveCorrection(text: String): String {
        var corrected = text
        
        // 修复数学公式格式
        corrected = corrected.replace(Regex("\\$\\s+([^$]+)\\s+\\$"), "$$1$")
        corrected = corrected.replace(Regex("\\$\\$\\s+([^$]+)\\s+\\$\\$"), "$$$1$$")
        
        // 修复表格格式
        corrected = corrected.replace(Regex("\\|\\s*([^|]+)\\s*\\|"), "| $1 |")
        
        // 修复引用格式
        corrected = corrected.replace(Regex("^>([^\\s])"), "> $1")
        
        return corrected
    }
    
    /**
     * 检查是否需要矫正
     */
    fun needsCorrection(text: String): Boolean {
        if (text.isBlank()) return false
        
        // 检查常见的格式问题
        val hasFormatIssues = listOf(
            Regex("#{1,6}[^\\s#]"), // 标题格式问题
            Regex("^[*+-][^\\s]"), // 列表格式问题
            Regex("\\$\\s+[^$]+\\s+\\$"), // 数学公式格式问题
            Regex("```[^\\n]*[^\\n]"), // 代码块格式问题
        ).any { it.containsMatchIn(text) }
        
        return hasFormatIssues
    }
}