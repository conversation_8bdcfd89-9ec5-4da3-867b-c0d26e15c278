package com.example.everytalk.util

import com.example.everytalk.data.PerformanceMetrics

/**
 * 文本格式矫正器
 * 负责对文本进行格式化和矫正处理
 */
class TextFormatCorrector(
    private var config: FormatCorrectionConfig,
    private val performanceMetrics: PerformanceMetrics
) {
    
    /**
     * 更新配置
     */
    fun updateConfig(newConfig: FormatCorrectionConfig) {
        this.config = newConfig
    }
    
    /**
     * 清理多余的空白字符
     */
    fun cleanExcessiveWhitespace(text: String): String {
        if (text.isBlank()) return text
        
        var cleaned = text
        
        // 移除行尾空白
        cleaned = cleaned.replace(Regex("[ \\t]+$"), "")
        
        // 标准化多个空格为单个空格（但不影响换行）
        cleaned = cleaned.replace(Regex("[ \\t]+"), " ")
        
        // 只移除过度的空行（保留最多3个连续换行，允许更自然的段落分隔）
        cleaned = cleaned.replace(Regex("\\n{5,}"), "\n\n\n")
        
        // 移除开头和结尾的空白
        cleaned = cleaned.trim()
        
        return cleaned
    }
    
    /**
     * 增强格式矫正
     */
    fun enhancedFormatCorrection(text: String): String {
        if (text.isBlank()) return text
        
        var corrected = text
        
        // 首先清理多余的空白
        corrected = cleanExcessiveWhitespace(corrected)
        
        // 应用基本矫正
        corrected = applyCorrection(corrected)
        
        // 额外的增强矫正
        corrected = applyEnhancedCorrections(corrected)
        
        return corrected
    }
    
    /**
     * 应用增强矫正
     */
    private fun applyEnhancedCorrections(text: String): String {
        var corrected = text
        
        // 修复代码块内的格式问题
        corrected = fixCodeBlockFormatting(corrected)
        
        // 修复数学公式格式
        corrected = fixMathFormatting(corrected)
        
        // 修复表格格式
        corrected = fixTableFormatting(corrected)
        
        // 修复链接格式
        corrected = fixLinkFormatting(corrected)
        
        return corrected
    }
    
    /**
     * 修复代码块格式
     */
    private fun fixCodeBlockFormatting(text: String): String {
        var fixed = text
        
        // 确保代码块前后有空行
        fixed = fixed.replace(Regex("([^\\n])\\n```"), "$1\n\n```")
        fixed = fixed.replace(Regex("```\\n([^\\n])"), "```\n\n$1")
        
        // 修复代码块语言标识
        fixed = fixed.replace(Regex("```\\s*([a-zA-Z]+)\\s*\\n"), "```$1\n")
        
        return fixed
    }
    
    /**
     * 修复数学公式格式
     */
    private fun fixMathFormatting(text: String): String {
        var fixed = text
        
        // 修复行内数学公式
        fixed = fixed.replace(Regex("\\$\\s+([^$]+?)\\s+\\$"), "$$1$")
        
        // 修复块级数学公式
        fixed = fixed.replace(Regex("\\$\\$\\s+([^$]+?)\\s+\\$\\$"), "$$$1$$")
        
        return fixed
    }
    
    /**
     * 修复表格格式
     */
    private fun fixTableFormatting(text: String): String {
        var fixed = text
        
        // 标准化表格单元格间距
        fixed = fixed.replace(Regex("\\|\\s*([^|]+?)\\s*\\|"), "| $1 |")
        
        // 确保表格前后有空行
        fixed = fixed.replace(Regex("([^\\n])\\n\\|"), "$1\n\n|")
        fixed = fixed.replace(Regex("\\|([^\\n]*)\\n([^\\n|])"), "|$1\n\n$2")
        
        return fixed
    }
    
    /**
     * 修复链接格式
     */
    private fun fixLinkFormatting(text: String): String {
        var fixed = text
        
        // 标准化链接格式
        fixed = fixed.replace(Regex("\\[\\s*([^\\]]+?)\\s*\\]\\s*\\(\\s*([^)]+?)\\s*\\)"), "[$1]($2)")
        
        return fixed
    }
    
    /**
     * 应用格式矫正
     */
    fun applyCorrection(text: String): String {
        if (text.isBlank()) return text
        
        var corrected = text
        
        // 根据矫正强度应用不同级别的矫正
        when (config.correctionIntensity) {
            CorrectionIntensity.LIGHT -> {
                corrected = applyLightCorrection(corrected)
            }
            CorrectionIntensity.MODERATE -> {
                corrected = applyLightCorrection(corrected)
                corrected = applyModerateCorrection(corrected)
            }
            CorrectionIntensity.AGGRESSIVE -> {
                corrected = applyLightCorrection(corrected)
                corrected = applyModerateCorrection(corrected)
                corrected = applyAggressiveCorrection(corrected)
            }
        }
        
        return corrected
    }
    
    /**
     * 轻度矫正 - 基本的格式化
     */
    private fun applyLightCorrection(text: String): String {
        var corrected = text
        
        // 标准化换行符
        corrected = corrected.replace(Regex("\\r\\n?"), "\n")
        
        // 移除多余的空白字符
        corrected = corrected.replace(Regex("[ \\t]+"), " ")
        
        // 标准化标题格式
        corrected = corrected.replace(Regex("^(#{1,6})([^\\s#])"), "$1 $2")
        
        return corrected
    }
    
    /**
     * 中度矫正 - 更复杂的格式化
     */
    private fun applyModerateCorrection(text: String): String {
        var corrected = text
        
        // 修复代码块格式
        corrected = corrected.replace(Regex("```([^\\n]*)\\n"), "```$1\n")
        
        // 修复列表格式
        corrected = corrected.replace(Regex("^([*+-])([^\\s])"), "$1 $2")
        
        // 修复链接格式
        corrected = corrected.replace(Regex("\\[([^\\]]+)\\]\\s*\\(([^)]+)\\)"), "[$1]($2)")
        
        return corrected
    }
    
    /**
     * 激进矫正 - 最全面的格式化
     */
    private fun applyAggressiveCorrection(text: String): String {
        var corrected = text
        
        // 修复数学公式格式
        corrected = corrected.replace(Regex("\\$\\s+([^$]+)\\s+\\$"), "$$1$")
        corrected = corrected.replace(Regex("\\$\\$\\s+([^$]+)\\s+\\$\\$"), "$$$1$$")
        
        // 修复表格格式
        corrected = corrected.replace(Regex("\\|\\s*([^|]+)\\s*\\|"), "| $1 |")
        
        // 修复引用格式
        corrected = corrected.replace(Regex("^>([^\\s])"), "> $1")
        
        return corrected
    }
    
    /**
     * 检查是否需要矫正
     */
    fun needsCorrection(text: String): Boolean {
        if (text.isBlank()) return false
        
        // 检查常见的格式问题
        val hasFormatIssues = listOf(
            Regex("#{1,6}[^\\s#]"), // 标题格式问题
            Regex("^[*+-][^\\s]"), // 列表格式问题
            Regex("\\$\\s+[^$]+\\s+\\$"), // 数学公式格式问题
            Regex("```[^\\n]*[^\\n]"), // 代码块格式问题
        ).any { it.containsMatchIn(text) }
        
        return hasFormatIssues
    }
}